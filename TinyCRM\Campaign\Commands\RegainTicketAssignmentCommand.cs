﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Threading.Tasks;
using TinyCRM.Campaign.Queries;
using Webaby;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;

namespace TinyCRM.Campaign.Commands
{
    public class RegainTicketAssignmentCommand : CommandBase
    {
        public SearchTicketAssignmentListQuery SearchParams { get; set; }
        public List<Guid> SelectedCampaignAssignmentIds { get; set; }
        public int RegainCount { get; set; }
        public Guid AdminId { get; set; }
    }

    internal class RegainTicketAssignmentQueryHandler : CommandHandlerBase<RegainTicketAssignmentCommand>
    {
        public RegainTicketAssignmentQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task ExecuteAsync(RegainTicketAssignmentCommand command)
        {
            var cmd = EntitySet.CreateDbCommand();
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@CampaignId", command.SearchParams.CampaignId));
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@OrganizationId", command.SearchParams.OrganizationId));
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@LoaiDichVuId", command.SearchParams.LoaiDichVuId));
            cmd.Parameters.Add(DbParameterHelper.AddNullableEnum(cmd, "@AssignmentStatus", command.SearchParams.AssignmentStatus));
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@OwnerId", command.SearchParams.OwnerId));
            cmd.Parameters.Add(DbParameterHelper.NewIdListParameter("@SelectedCampaignAssignmentIds", command.SelectedCampaignAssignmentIds));
            cmd.Parameters.Add(DbParameterHelper.AddNullableInt(cmd, "@RegainCount", command.RegainCount));
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@AdminId", command.AdminId));

            cmd.CommandText = "RegainCampaignAssignment";
            cmd.CommandType = CommandType.StoredProcedure;
            await EntitySet.ExecuteNonQueryAsync(cmd);
        }
    }
}

﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using TinyCRM.DigitalChannel.Queries;
using TinyCRM.DigitalPushCode.Queries;
using Webaby;

namespace TinyCRM.DigitalChannel.Commands
{
    public class CreateEditDigitalChannelCommand : CommandBase
    {
        public Guid Id { get; set; }

        public string Name { get; set; }

        public string ChannelCode { get; set; }

        public ChannelType ChannelType { get; set; }

        public string Configuration { get; set; }
    }

    internal class CreateEditDigitalChannelCommandHandler : CommandHandlerBase<CreateEditDigitalChannelCommand>
    {
        public CreateEditDigitalChannelCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }
        public override async Task ExecuteAsync(CreateEditDigitalChannelCommand command)
        {
            var channel = await EntitySet.GetAsync<DigitalChannelEntity>(command.Id);
            if (channel == null)
            {
                channel = new DigitalChannelEntity();
                channel.Id = command.Id;
            }
            channel.Name = command.Name;
            channel.ChannelCode = command.ChannelCode;
            channel.ChannelType = command.ChannelType;
            if (command.Configuration.IsNullOrEmpty())
            {
                channel.Configuration = "";
            } else {
                channel.Configuration = command.Configuration;
            }            
            await Repository.SaveAsync(channel);
        }
    }
}

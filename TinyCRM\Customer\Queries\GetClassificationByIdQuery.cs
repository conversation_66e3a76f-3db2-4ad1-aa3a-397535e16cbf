﻿using System;
using Webaby;

namespace TinyCRM.Customer.Queries
{
    public class GetClassificationByIdQuery : QueryBase<ClassificationData>
    {
        public Guid Id { get; set; }
    }

    internal class GetClassificationByIdQueryHandler : QueryHandlerBase<GetClassificationByIdQuery, ClassificationData>
    {
        public GetClassificationByIdQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<ClassificationData>> ExecuteAsync(GetClassificationByIdQuery query)
        {
            var entity = await EntitySet.GetAsync<ClassificationEntity>(query.Id);
            return new QueryResult<ClassificationData>(Mapper.Map<ClassificationData>(entity));
        }
    }
}
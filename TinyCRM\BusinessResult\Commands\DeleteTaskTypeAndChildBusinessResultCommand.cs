﻿using AutoMapper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TinyCRM.TaskType;
using Webaby;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.BusinessResult.Commands
{
    public class DeleteTaskTypeAndChildBusinessResultCommand : CommandBase
    {
        public Guid TaskTypeId { get; set; }

        public Guid NodeParentId { get; set; }
    }

    internal class DeleteTaskTypeAndChildBusinessResultCommandHandler : CommandHandlerBase<DeleteTaskTypeAndChildBusinessResultCommand>
    {
        public DeleteTaskTypeAndChildBusinessResultCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task ExecuteAsync(DeleteTaskTypeAndChildBusinessResultCommand command)
        {
            List<IEntity> deletedEntites = new List<IEntity>();
            var entity = (from br in EntitySet.Get<BusinessResultEntity>()
                          join ttbr in EntitySet.Get<BusinessResultReferenceEntity>() on br.Id equals ttbr.BusinessResultId
                          where ttbr.ReferenceObjectId == command.TaskTypeId && br.ParentId == command.NodeParentId
                          select ttbr
                          ).ToList();
            deletedEntites.AddRange(entity);

            BusinessResultReferenceEntity taskTypeBusinessResultEntity = EntitySet.Get<BusinessResultReferenceEntity>().Where(ttbr => ttbr.ReferenceObjectId == command.TaskTypeId && ttbr.BusinessResultId == command.NodeParentId).SingleOrDefault();
            if (taskTypeBusinessResultEntity != null)
            {
                await Repository.DeleteAsync(taskTypeBusinessResultEntity);
            }

            if (deletedEntites != null && deletedEntites.Count > 0)
            {
                await Repository.DeleteAsync(deletedEntites);
            }
        }
    }
}
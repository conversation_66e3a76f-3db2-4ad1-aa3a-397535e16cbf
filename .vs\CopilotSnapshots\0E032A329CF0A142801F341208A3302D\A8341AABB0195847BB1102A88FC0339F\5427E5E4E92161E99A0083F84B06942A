﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Webaby.Data;
using Webaby.Localization;
using Webaby;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace TinyCRM.Outbound.CallResult.Queries
{
    public class GetCallStrategyListQuery : QueryBase<CallStrategyData>
    {
        public Guid? CallResultId { get; set; }
    }

    internal class GetCallStrategyListQueryHandler : QueryHandlerBase<GetCallStrategyListQuery, CallStrategyData>
    {
        public GetCallStrategyListQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<CallStrategyData>> ExecuteAsync(GetCallStrategyListQuery query)
        {
            var callStrategyQuery = EntitySet.Get<CallResultCallPlanStrategyEntity>();
            if (query.CallResultId.HasValue)
            {
                callStrategyQuery = callStrategyQuery.Where(t => t.CallResultId == query.CallResultId);
            }
            var entities = await callStrategyQuery.ToListAsync();
            var mapped = entities.Select(x => Mapper.Map<CallStrategyData>(x));
            return QueryResult.Create(mapped);
        }
    }
}
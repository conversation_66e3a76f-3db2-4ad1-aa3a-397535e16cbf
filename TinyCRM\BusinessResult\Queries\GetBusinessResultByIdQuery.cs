﻿using System;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;

namespace TinyCRM.BusinessResult.Queries
{
    public class GetBusinessResultByIdQuery : QueryBase<BusinessResultData>
    {
        public Guid Id { get; set; }
    }

    internal class GetBusinessResultByIdQueryHandler : QueryHandlerBase<GetBusinessResultByIdQuery, BusinessResultData>
    {
        public GetBusinessResultByIdQueryHandler(IServiceProvider serviceProvider)
            : base(serviceProvider) { }

        public override async Task<QueryResult<BusinessResultData>> ExecuteAsync(GetBusinessResultByIdQuery query)
        {
            var entity = await EntitySet.GetAsync<BusinessResultEntity>(query.Id);
            return new QueryResult<BusinessResultData>(Mapper.Map<BusinessResultData>(entity));
        }
    }
}
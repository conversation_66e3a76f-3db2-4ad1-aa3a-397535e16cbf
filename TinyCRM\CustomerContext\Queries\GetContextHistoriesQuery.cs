﻿using System;
using System.Data;
using System.Data.Common;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;

namespace TinyCRM.CustomerContext.Queries
{
    public class GetContextHistoriesQuery : QueryBase<CustomerContextHistoryData>
    {
        public string Token { get; set; }
    }

    internal class GetContextHistoriesQueryHandler : QueryHandlerBase<GetContextHistoriesQuery, CustomerContextHistoryData>
    {
        public GetContextHistoriesQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<CustomerContextHistoryData>> ExecuteAsync(GetContextHistoriesQuery query)
        {
            int startRow = query.Pagination.Index * query.Pagination.Size + 1;
            int endRow = query.Pagination.Index * query.Pagination.Size + query.Pagination.Size;

            var cmd = EntitySet.CreateDbCommand();
            cmd.CommandText = "dbo.GetContextHistoriesQuery";
            cmd.CommandType = CommandType.StoredProcedure;

            cmd.Parameters.Add(DbParameterHelper.AddNullableString(cmd, "@Token", query.Token));
            cmd.Parameters.Add(DbParameterHelper.AddNullableInt(cmd, "@StartRow", startRow));
            cmd.Parameters.Add(DbParameterHelper.AddNullableInt(cmd, "@EndRow", endRow));

            var main = await EntitySet.ExecuteReadCommandAsync<CustomerContextHistoryData>(cmd);
            return new QueryResult<CustomerContextHistoryData>(main);
        }
    }
}

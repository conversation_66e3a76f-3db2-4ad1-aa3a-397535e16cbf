﻿using System;
using System.Data;
using System.Data.SqlClient;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;
using Microsoft.Data.SqlClient;
using TinyCRM.Customer.Queries;
using Webaby.Validation.PhoneNumber;
using AutoMapper;
using Webaby.Localization;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;

namespace TinyCRM.Customer.Commands
{
    public class DedupSignleBackendAndTempCustomersCommand : CommandBase
    {
        public Guid BackendCustomerId { get; set; }

        public string Phone1 { get; set; }

        public string Phone2 { get; set; }

        public string Phone3 { get; set; }

        public string Email { get; set; }

        public string FacebookId { get; set; }
    }

    internal class DedupSignleBackendAndTempCustomersCommandHandler : CommandHandlerBase<DedupSignleBackendAndTempCustomersCommand>
    {                
        IText _text { get; set; }
        IConfiguration _configuration { get; set; }
        public string CustomerDedupColumns { get { return _configuration.GetValue<string>("customer.dedup.definitions"); } }
        public DedupSignleBackendAndTempCustomersCommandHandler(IServiceProvider serviceProvider, IText text, IConfiguration configuration) : base(serviceProvider) { _text = text; _configuration = configuration; }

        public override async Task ExecuteAsync(DedupSignleBackendAndTempCustomersCommand command)
        {
            DataTable paramValues = new DataTable("StringString");
            paramValues.Columns.Add("Value1", typeof(string));
            paramValues.Columns.Add("Value2", typeof(string));

            string phone1 = PhoneNumberValidator.Input(command.Phone1, _text).Output;
            string phone2 = PhoneNumberValidator.Input(command.Phone2, _text).Output;
            string phone3 = PhoneNumberValidator.Input(command.Phone3, _text).Output;

            paramValues.Rows.Add("Phone1", phone1.IsNullOrEmpty() ? string.Empty : phone1.Trim());
            paramValues.Rows.Add("Phone2", phone2.IsNullOrEmpty() ? string.Empty : phone2.Trim());
            paramValues.Rows.Add("Phone3", phone3.IsNullOrEmpty() ? string.Empty : phone3.Trim());

            paramValues.Rows.Add("Email", command.Email.IsNullOrEmpty() ? string.Empty : command.Email.Trim());
            paramValues.Rows.Add("FacebookId", command.FacebookId.IsNullOrEmpty() ? string.Empty : command.FacebookId.Trim());

            DataSet dedupColumns = CustomerDedupDefinition.GetFromJsonString(CustomerDedupColumns);

            var cmd = EntitySet.CreateDbCommand();            

            cmd.Parameters.AddRange(new[]
            {
                DbParameterHelper.AddNullableGuid(cmd, "@BackendCustomerId", command.BackendCustomerId),
                new SqlParameter("@ParamValues", SqlDbType.Structured)
                {
                    Value = paramValues
                },
                new SqlParameter("@CustomerDedupColumns", SqlDbType.Structured)
                {
                    Value = dedupColumns.Tables["DedupSingle"]
                }
            });

            cmd.CommandType = CommandType.StoredProcedure;
            cmd.CommandText = "dbo.DedupSignleBackendAndTempCustomers";
            await EntitySet.ExecuteNonQueryAsync(cmd);
        }
    }
}
﻿using Webaby.Data;
using AutoMapper;
using Webaby.Localization;
using Webaby;
using System;
using System.Linq;
using System.Threading.Tasks;
using TinyCRM.Enums;

namespace TinyCRM.Customer.Queries
{
    public class GetCustomersByClassificationChannelQuery : QueryBase<CustomerData>
    {
        public Guid ClassificationChannelId { get; set; }
    }

    internal class GetCustomersByClassificationChannelQueryHandler : QueryHandlerBase<GetCustomersByClassificationChannelQuery, CustomerData>
    {
        public GetCustomersByClassificationChannelQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<CustomerData>> ExecuteAsync(GetCustomersByClassificationChannelQuery query)
        {
            var classificationEntities = EntitySet.Get<ClassificationEntity>();
            var customerEntity = await EntitySet.GetAsync<CustomerEntity>();

            var mainQuery = from c in customerEntity
                            join cf in classificationEntities on c.SourceClassificationId equals cf.Id
                            where cf.ClassificationChannelId == query.ClassificationChannelId
                            orderby c.Code, c.Name
                            select c;

            return QueryResult.Create(mainQuery, Mapper.Map<CustomerData>);
        }
    }
}

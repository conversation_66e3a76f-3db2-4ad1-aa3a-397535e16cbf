﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using Webaby;
using Webaby.Data;
using Microsoft.Data.SqlClient;

namespace TinyCRM.Customer.Queries
{
    public class GetCustomerAlternativeAddressByListCustomerIdAndRpContactQuery : QueryBase<CustomerAlternativeAddressItem>
    {
        public List<Guid> CustomerIdList { get; set; }

        public string PhoneNumber { get; set; }

        public string Email { get; set; }

        public string FacebookId { get; set; }
    }

    internal class GetCustomerAlternativeAddressByListCustomerIdAndRpContactQueryHandler : QueryHandlerBase<GetCustomerAlternativeAddressByListCustomerIdAndRpContactQuery, CustomerAlternativeAddressItem>
    {
        public GetCustomerAlternativeAddressByListCustomerIdAndRpContactQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }
        public override async Task<QueryResult<CustomerAlternativeAddressItem>> ExecuteAsync(GetCustomerAlternativeAddressByListCustomerIdAndRpContactQuery query)
        {
            var cmd = EntitySet.CreateDbCommand();
            var dtCustomerId = new DataTable();
            dtCustomerId.Columns.Add("Item", typeof(string));
            if (query.CustomerIdList != null)
            {
                foreach (var id in query.CustomerIdList)
                {
                    dtCustomerId.Rows.Add(id);
                }
            }
            cmd.Parameters.Add(new SqlParameter("@CustomerIdList", SqlDbType.Structured)
            {
                TypeName = "dbo.GuidList",
                Value = dtCustomerId
            });
            cmd.Parameters.Add(DbParameterHelper.AddNullableString(cmd, "@PhoneNumber", query.PhoneNumber));
            cmd.Parameters.Add(DbParameterHelper.AddNullableString(cmd, "@Email", query.Email));
            cmd.Parameters.Add(DbParameterHelper.AddNullableString(cmd, "@FacebookId", query.FacebookId));
            cmd.CommandText = "GetCustomerAlternativeAddressByListCustomerIdAndRpContact";
            cmd.CommandType = CommandType.StoredProcedure;
            var mainQuery = await EntitySet.ExecuteReadCommandAsync<CustomerAlternativeAddressItem>(cmd);
            return new QueryResult<CustomerAlternativeAddressItem>(mainQuery);
        }
    }
}

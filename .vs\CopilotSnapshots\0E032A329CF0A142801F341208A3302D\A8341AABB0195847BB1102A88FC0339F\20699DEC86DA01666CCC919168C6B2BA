﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Webaby.Data;
using Webaby.Localization;
using Webaby;
using System;
using System.Threading.Tasks;

namespace TinyCRM.Outbound.CallResult.Queries
{
    public class GetCallResultByIdQuery : QueryBase<CallResultData>
    {
        public Guid Id { get; set; }
    }

    internal class GetCallResultByIdQueryHandler : QueryHandlerBase<GetCallResultByIdQuery, CallResultData>
    {
        public GetCallResultByIdQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<CallResultData>> ExecuteAsync(GetCallResultByIdQuery query)
        {
            var callResult = await EntitySet.GetAsync<CallResultEntity>(query.Id);
            if (callResult == null) throw new InvalidOperationException(T["Không tìm  thấy CallResult có id '{0}'", query.Id]);
            return new QueryResult<CallResultData>(Mapper.Map<CallResultData>(callResult));
        }
    }
}
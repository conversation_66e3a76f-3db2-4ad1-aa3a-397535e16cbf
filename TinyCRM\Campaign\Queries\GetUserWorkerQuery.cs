﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Threading.Tasks;
using Webaby.Core.UserAccount;
using Webaby;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;

namespace TinyCRM.Campaign.Queries
{
    public class GetUserWorkerQuery : QueryBase<UserListItemData>
    {
        public Guid CampaignId { get; set; }

        public string SearchText { get; set; }

        public string BusinessRole { get; set; }

        public Guid? OrganizationId { get; set; }

        public bool DoInsert { get; set; }

        public List<Guid> SelectedUser { get; set; }

        public Guid DoInsertBy { get; set; }
    }

    internal class GetUserWorkerQueryHandler : QueryHandlerBase<GetUserWorkerQuery, UserListItemData>
    {
        public GetUserWorkerQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<UserListItemData>> ExecuteAsync(GetUserWorkerQuery query)
        {
            var cmd = EntitySet.CreateDbCommand();
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@CampaignId", query.CampaignId));
            cmd.Parameters.Add(DbParameterHelper.AddNullableString(cmd, "@SearchText", query.SearchText));
            cmd.Parameters.Add(DbParameterHelper.AddNullableString(cmd, "@BusinessRole", query.BusinessRole));
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@OrganizationId", query.OrganizationId));
            cmd.Parameters.Add(DbParameterHelper.NewNullableBooleanParameter(cmd, "@DoInsert", query.DoInsert));
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@DoInsertBy", query.DoInsertBy));
            cmd.Parameters.Add(DbParameterHelper.NewIdListParameter("@SelectedUserIds", query.SelectedUser));
            cmd.Parameters.Add(DbParameterHelper.AddNullableInt(cmd, "@StartRow", query.Pagination.StartRow));
            cmd.Parameters.Add(DbParameterHelper.AddNullableInt(cmd, "@EndRow", query.Pagination.EndRow));
            cmd.CommandText = "SearchUsersAddToCampaign";
            cmd.CommandType = CommandType.StoredProcedure;
            var mainQuery = await EntitySet.ExecuteReadCommandAsync<UserListItemData>(cmd);
            return new QueryResult<UserListItemData>(mainQuery);
        }
    }
}
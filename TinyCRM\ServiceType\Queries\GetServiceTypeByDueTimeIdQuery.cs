﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TinyCRM.ServiceCategory;
using Webaby;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;

namespace TinyCRM.ServiceType.Queries
{
    public class GetServiceTypeByDueTimeIdQuery : QueryBase<ServiceTypeData>
    {
        public Guid Id { get; set; }
    }

    internal class GetServiceTypeByDueTimeIdQueryHandler : QueryHandlerBase<GetServiceTypeByDueTimeIdQuery, ServiceTypeData>
    {
        public GetServiceTypeByDueTimeIdQueryHandler(IEntitySet entitySet, IRepository repository, IText text, IMapper mapper)
            : base(entitySet, repository, text, mapper) { }

        public override async Task<QueryResult<ServiceTypeData>> ExecuteAsync(GetServiceTypeByDueTimeIdQuery query)
        {
            var stList = (await EntitySet.GetAsync<ServiceTypeEntity>()).Where(x => x.AcceptDueTimeId == query.Id || x.ProcessDueTimeId == query.Id);
            var catList = await EntitySet.GetAsync<ServiceCategoryEntity>();
            var data = from st in stList
                       join l1 in catList on st.Level1Id equals l1.Id
                       join l2 in catList on st.Level2Id equals l2.Id
                       join l3 in catList on st.Level3Id equals l3.Id
                       join l4 in catList on st.Level4Id equals l4.Id
                       select new
                       {
                           Main = st,
                           N1 = l1.Code + " - " + l1.Name,
                           N2 = l2.Code + " - " + l2.Name,
                           N3 = l3.Code + " - " + l3.Name,
                           N4 = l4.Code + " - " + l4.Name
                       };
            return QueryResult.Create(data, query.Pagination, x =>
            {
                var d = Mapper.Map<ServiceTypeData>(x.Main);
                d.Level1Name = x.N1;
                d.Level2Name = x.N2;
                d.Level3Name = x.N3;
                d.Level4Name = x.N4;
                return d;
            });
        }
    }
}

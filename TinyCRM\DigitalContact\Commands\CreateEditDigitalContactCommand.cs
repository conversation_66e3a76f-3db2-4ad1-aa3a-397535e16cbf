﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using TinyCRM.DigitalChannel.Queries;
using TinyCRM.DigitalPushCode.Queries;
using Webaby;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;

namespace TinyCRM.DigitalContact.Commands
{
    public class CreateEditDigitalContactCommand : CommandBase
    {
        public Guid Id { get; set; }

        public string UserId { get; set; }

        public Guid DigitalContactTypeId { get; set; }

        public Guid? CustomerId { get; set; }

        public string CustomerName { get; set; }
    }

    internal class CreateDigitalContactCommandHandler : CommandHandlerBase<CreateEditDigitalContactCommand>
    {
        public CreateDigitalContactCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task ExecuteAsync(CreateEditDigitalContactCommand command)
        {
            var digitalContact = await EntitySet.GetAsync<DigitalContactEntity>(command.Id);
            if (digitalContact == null)
            {
                digitalContact = new DigitalContactEntity();
                digitalContact.Id = command.Id;
            }
            digitalContact.UserId = command.UserId;
            digitalContact.DigitalContactTypeId = command.DigitalContactTypeId;
            digitalContact.CustomerId = command.CustomerId;
            digitalContact.Name = command.CustomerName;
            await Repository.SaveAsync(digitalContact);
        }
    }
}

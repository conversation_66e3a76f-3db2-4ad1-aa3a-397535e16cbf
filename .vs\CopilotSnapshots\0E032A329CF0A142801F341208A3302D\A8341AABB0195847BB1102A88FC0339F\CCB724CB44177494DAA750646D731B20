﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Webaby.Data;
using Webaby.Localization;
using Webaby;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace TinyCRM.Outbound.CallResult.Queries
{
    public class GetCallResultListQuery : QueryBase<CallResultData>
    {
        public Guid? ResultCodeSuiteId { get; set; }
    }

    internal class GetCallResultListQueryHandler : QueryHandlerBase<GetCallResultListQuery, CallResultData>
    {
        public GetCallResultListQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<CallResultData>> ExecuteAsync(GetCallResultListQuery query)
        {
            var callResultQuery = EntitySet.Get<CallResultEntity>().Where(cr => cr.IsDeleted == false);

            if (query.ResultCodeSuiteId.IsNotNullOrEmpty())
            {
                callResultQuery = callResultQuery.Where(cr => cr.ResultCodeSuiteId == query.ResultCodeSuiteId);
            }

            callResultQuery = (from c in callResultQuery
                               orderby c.Code
                               select c);

            var entities = await callResultQuery.ToListAsync();
            var mapped = entities.Select(x => Mapper.Map<CallResultData>(x));
            return QueryResult.Create(mapped);
        }
    }
}
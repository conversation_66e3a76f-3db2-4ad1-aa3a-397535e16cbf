﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Webaby.Data;
using Webaby.Localization;
using Webaby;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace TinyCRM.Outbound.CallResult.Queries
{
    public class GetResultCodeSuiteListQuery : QueryBase<ResultCodeSuiteData>
    {
        public string SuiteName { get; set; }
    }

    internal class GetResultCodeSuiteListQueryHandler : QueryHandlerBase<GetResultCodeSuiteListQuery, ResultCodeSuiteData>
    {
        public GetResultCodeSuiteListQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<ResultCodeSuiteData>> ExecuteAsync(GetResultCodeSuiteListQuery query)
        {
            var resultCodeSuiteQuery = EntitySet.Get<ResultCodeSuiteEntity>();
            if (query.SuiteName.IsNotNullOrEmpty())
            {
                resultCodeSuiteQuery = resultCodeSuiteQuery.Where(x => x.Name.Contains(query.SuiteName));
            }
            var entities = await resultCodeSuiteQuery.ToListAsync();
            var mapped = entities.Select(x => Mapper.Map<ResultCodeSuiteData>(x));
            return QueryResult.Create(mapped);
        }
    }
}
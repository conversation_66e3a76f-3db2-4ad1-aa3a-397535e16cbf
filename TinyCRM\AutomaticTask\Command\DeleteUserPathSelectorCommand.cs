﻿using System;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;

namespace TinyCRM.AutomaticTask.Command
{
    public class DeleteUserPathSelectorCommand : CommandBase
    {
        public Guid Id { get; set; }
    }

    internal class DeleteUserPathSelectorCommandHandler : CommandHandlerBase<DeleteUserPathSelectorCommand>
    {
        public DeleteUserPathSelectorCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task ExecuteAsync(DeleteUserPathSelectorCommand command)
        {
            await Repository.DeleteAsync<UserPathSelectorEntity>(command.Id);
        }
    }
}

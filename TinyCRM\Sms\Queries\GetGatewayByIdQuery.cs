﻿using System;
using System.Data;
using System.Threading.Tasks;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;
using Webaby;

namespace TinyCRM.Sms.Queries
{
    public class GetGatewayByIdQuery : QueryBase<GatewayData>
    {
        public Guid Id { get; set; }
    }

    internal class GetGatewayByIdQueryHandler : QueryHandlerBase<GetGatewayByIdQuery, GatewayData>
    {
        public GetGatewayByIdQueryHandler(IEntitySet entitySet, IRepository repository, IText text, IMapper mapper)
            : base(entitySet, repository, text, mapper) { }

        public override async Task<QueryResult<GatewayData>> ExecuteAsync(GetGatewayByIdQuery query)
        {
            var mainQuery = await EntitySet.GetAsync<GatewayEntity>(query.Id);
            return new QueryResult<GatewayData>(Mapper.Map<GatewayData>(mainQuery));
        }
    }
}

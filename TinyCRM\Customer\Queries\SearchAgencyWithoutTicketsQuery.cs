﻿using System;
using System.Data;
using System.Data.SqlClient;
using Webaby;
using Webaby.Data;

namespace TinyCRM.Customer.Queries
{
    public class SearchAgencyWithoutTicketsQuery : QueryBase<CustomerData>
    {
        public string Designation { get; set; }

        public Guid ServiceTypeId { get; set; }
    }

    internal class SearchAgencyWithoutTicketsQueryHandler: QueryHandlerBase<SearchAgencyWithoutTicketsQuery, CustomerData>
    {
        public SearchAgencyWithoutTicketsQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<CustomerData>> ExecuteAsync(SearchAgencyWithoutTicketsQuery query)
        {
            var cmd = EntitySet.CreateDbCommand();
            cmd.Parameters.AddRange(new[]
            {
                DbParameterHelper.AddNullableString(cmd, "@Designation", query.Designation),
                DbParameterHelper.AddNullableGuid(cmd, "@ServiceType", query.ServiceTypeId)
            });

            cmd.CommandText = "dbo.SearchAgencyWithoutTickets";
            cmd.CommandType = CommandType.StoredProcedure;
            var mainQuery = await EntitySet.ExecuteReadCommandAsync<CustomerData>(cmd);
            return new QueryResult<CustomerData>(mainQuery);
        }
    }
}

﻿using System;
using System.Data;
using System.Data.SqlClient;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;

namespace TinyCRM.Sms.Queries
{
    public class SearchGatewayQuery : QueryBase<GatewayListItem>
    {
        public string Name { get; set; }

        public string PhoneNumberPrefix { get; set; }

        public string Endpoint { get; set; }

        public Guid? GatewayIntergrationId { get; set; }

        public bool? IsDefault { get; set; }
    }

    internal class SearchGatewayQueryHandler : QueryHandlerBase<SearchGatewayQuery, GatewayListItem>
    {
        public SearchGatewayQueryHandler(IEntitySet entitySet, IRepository repository, IText text, IMapper mapper)
            : base(entitySet, repository, text, mapper) { }

        public override async Task<QueryResult<GatewayListItem>> ExecuteAsync(SearchGatewayQuery query)
        {
            int startRow = query.Pagination.Index * query.Pagination.Size + 1;
            int endRow = query.Pagination.Index * query.Pagination.Size + query.Pagination.Size;

            var cmd = EntitySet.CreateDbCommand();
            DbParameterHelper.AddNullableString(cmd, "@Name", query.Name);
            DbParameterHelper.AddNullableString(cmd, "@PhoneNumberPrefix", query.PhoneNumberPrefix);
            DbParameterHelper.AddNullableString(cmd, "@Endpoint", query.Endpoint);
            DbParameterHelper.AddNullableGuid(cmd, "@GatewayIntergrationId", query.GatewayIntergrationId);
            DbParameterHelper.NewNullableBooleanParameter(cmd, "@IsDefault", query.IsDefault);
            DbParameterHelper.AddNullableInt(cmd, "@StartRow", startRow);
            DbParameterHelper.AddNullableInt(cmd, "@EndRow", endRow);

            cmd.CommandText = "dbo.SearchGateways";
            cmd.CommandType = CommandType.StoredProcedure;

            var mainQuery = await EntitySet.ExecuteReadCommandAsync<GatewayListItem>(cmd);
            return new QueryResult<GatewayListItem>(mainQuery);
        }
    }
}

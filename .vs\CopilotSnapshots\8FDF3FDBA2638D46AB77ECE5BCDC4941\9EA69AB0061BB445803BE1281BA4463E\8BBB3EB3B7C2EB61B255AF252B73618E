﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;

namespace TinyCRM.BusinessResult.Queries
{
    public class GetBusinessResultListQuery : QueryBase<BusinessResultData>
    {
    }

    public class GetBusinessResultListQueryHandler : QueryHandlerBase<GetBusinessResultListQuery, BusinessResultData>
    {
        public GetBusinessResultListQueryHandler(IServiceProvider serviceProvider)
            : base(serviceProvider) { }

        public override async Task<QueryResult<BusinessResultData>> ExecuteAsync(GetBusinessResultListQuery query)
        {
            var entityList = (await EntitySet.GetAsync<BusinessResultEntity>()).OrderBy(tt => tt.DisplayOrder);
            return QueryResult.Create(entityList, Mapper.Map<BusinessResultData>);
        }
    }
}
﻿using System;
using System.Data;
using System.Threading.Tasks;
using AutoMapper;
using TinyCRM.Enums;
using Webaby;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.Campaign.Queries
{
    public class SearchAgentCampaignTicketAssginmentListQuery : QueryBase<AgentCampaignTicketAssginmentListItem>
    {
        public Guid CampaignId { get; set; }

        public Guid UserId { get; set; }

        public Guid? OrganizationId { get; set; }

        public string MaKH { get; set; }

        public string PhoneNumber { get; set; }

        public Guid? LoaiDichVuId { get; set; }

        public AssignmentStatus? AssignmentStatus { get; set; }

        public string AssignmentTag { get; set; }
    }

    internal class SearchAgentCampaignTicketAssginmentListQueryHandler : QueryHandlerBase<SearchAgentCampaignTicketAssginmentListQuery, AgentCampaignTicketAssginmentListItem>
    {
        public SearchAgentCampaignTicketAssginmentListQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<AgentCampaignTicketAssginmentListItem>> ExecuteAsync(SearchAgentCampaignTicketAssginmentListQuery query)
        {
            var cmd = EntitySet.CreateDbCommand();
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@CampaignId", query.CampaignId));
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@UserId", query.UserId));
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@OrganizationId", query.OrganizationId));
            cmd.Parameters.Add(DbParameterHelper.AddNullableString(cmd, "@MaKH", string.IsNullOrEmpty(query.MaKH) ? string.Empty : query.MaKH));
            cmd.Parameters.Add(DbParameterHelper.AddNullableString(cmd, "@PhoneNumber", string.IsNullOrEmpty(query.PhoneNumber) ? string.Empty : query.PhoneNumber));
            cmd.Parameters.Add(DbParameterHelper.AddNullableString(cmd, "@AssignmentTag", string.IsNullOrEmpty(query.AssignmentTag) ? string.Empty : query.AssignmentTag));
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@LoaiDichVuId", query.LoaiDichVuId));
            cmd.Parameters.Add(DbParameterHelper.AddNullableInt(cmd, "@AssignmentStatus", query.AssignmentStatus.HasValue ? (int)query.AssignmentStatus.Value : (int?)null));
            cmd.Parameters.Add(DbParameterHelper.AddNullableInt(cmd, "@StartRow", query.Pagination.StartRow));
            cmd.Parameters.Add(DbParameterHelper.AddNullableInt(cmd, "@EndRow", query.Pagination.EndRow));
            cmd.CommandText = "SearchAgentCampaignTicketAssginmentList";
            cmd.CommandType = CommandType.StoredProcedure;
            var mainQuery = await EntitySet.ExecuteReadCommandAsync<AgentCampaignTicketAssginmentListItem>(cmd);
            return new QueryResult<AgentCampaignTicketAssginmentListItem>(mainQuery);
        }
    }
}
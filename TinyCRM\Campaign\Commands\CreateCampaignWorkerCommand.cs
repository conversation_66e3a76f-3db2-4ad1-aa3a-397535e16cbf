﻿using AutoMapper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Webaby;
using Webaby.Core.UserAccount;
using Webaby.Data;
using Webaby.Localization;
using Webaby.Security;

namespace TinyCRM.Campaign.Commands
{
    public class CreateCampaignWorkerCommand : CommandBase
    {
        public int AddCount { get; set; }

        public Guid CampaignId { get; set; }

        public List<Guid> SelectedUser { get; set; }

        public Guid? OrganizationId { get; set; }

        public string BusinessRole { get; set; }
    }

    internal class CreateCampaignWorkerCommandHandler : CommandHandlerBase<CreateCampaignWorkerCommand>
    {
        public CreateCampaignWorkerCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task ExecuteAsync(CreateCampaignWorkerCommand command)
        {
            var user = from u in EntitySet.Get<AspNetUserEntity>()
                       join wk in (await EntitySet.GetAsync<CampaignWorkerEntity>()).Where(x => x.CampaignId == command.CampaignId) on u.Id equals wk.UserId into _wk
                       from wk in _wk.DefaultIfEmpty()
                       where wk == null
                       select u;

            if (command.SelectedUser != null && command.SelectedUser.Count > 0)
            {
                user = user.Where(x => command.SelectedUser.Contains(x.Id));
            }
            else
            {
                if (command.OrganizationId.HasValue) user = user.Where(x => x.OrganizationId == command.OrganizationId);
                if (!string.IsNullOrEmpty(command.BusinessRole)) user = user.Where(x => x.BusinessRole == command.BusinessRole);
            }

            user = user.Take(command.AddCount);
            var users = user.AsEnumerable();
            var deletedUserQuery =
                from worker in (await EntitySet.GetAsync<CampaignWorkerEntity>(true)).Where(x => x.Deleted && x.CampaignId == command.CampaignId) select worker;

            var deletedUsers = deletedUserQuery.AsEnumerable();
            
            var newAssignment = users.Where(x => deletedUsers.All(y => y.UserId != x.Id)).Select(x =>
            {
                return new CampaignWorkerEntity
                {
                    Id = Guid.NewGuid(),
                    CampaignId = command.CampaignId,
                    UserId = x.Id,                    
                    Deleted = false
                };
            });

            var oldAssignment = users.Where(x => deletedUsers.Any(y => y.UserId == x.Id)).Select(x =>
            {
                return new CampaignWorkerEntity
                {
                    Id = deletedUsers.Single(y => y.UserId == x.Id).Id,
                    CampaignId = command.CampaignId,
                    UserId = x.Id,                    
                    Deleted = false
                };
            });

            await Repository.SaveAsync(newAssignment);
            await Repository.SaveAsync(oldAssignment);
        }
    }
}

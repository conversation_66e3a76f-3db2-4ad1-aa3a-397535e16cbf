﻿using System;
using Webaby;

namespace TinyCRM.Outbound.AdditionalDataTemplate.Queries
{
    public class GetAdditionalDataTemplateByIdQuery : QueryBase<AdditionalDataTemplateInfo>
    {
        public Guid Id { get; set; }
    }

    internal class GetAdditionalDataTemplateByIdQueryHandler : QueryHandlerBase<GetAdditionalDataTemplateByIdQuery, AdditionalDataTemplateInfo>
    {
        public override QueryResult<AdditionalDataTemplateInfo> Execute(GetAdditionalDataTemplateByIdQuery query)
        {
            var entity = EntitySet.Get<AdditionalDataTemplateEntity>(query.Id);
            if (entity == null) throw new InvalidOperationException(T["Không tìm  thấy AdditionalDataTemplate có id '{0}'", query.Id]);
            return new QueryResult<AdditionalDataTemplateInfo>(AdditionalDataTemplateInfo.FromEntity(entity));
        }
    }
}

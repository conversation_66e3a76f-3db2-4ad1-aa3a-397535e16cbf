﻿using AutoMapper;
using Newtonsoft.Json;
using RestSharp;
using System;
using System.Collections.Generic;
using System.ComponentModel.Composition;
using System.Configuration;
using System.Linq;
using TinyCRM.Outbound.Campaign.Queries;
using TinyCRM.Outbound.ContactCallResultServiceCallback.Queries;
using Webaby;
using Webaby.Data;
using Webaby.Scheduling;

namespace TinyCRM.Outbound.ContactCallResultServiceCallback.Tasks
{
    `
    {
        public Guid Id => Guid.Parse("5A6DC4F0-BDC3-4206-B88D-DA2767F099FA");
        public bool IsSystem => false;
        public bool SingletonRequired => true;

        public DateTime? StartTime => null;
        public DateTime? EndTime => null;
        public bool StopOnError => false;

        [Import("scheduledtask.callservicecallbackscheduledtask.disabled", AllowDefault = true)]
        public bool Disabled { get; set; }

        [Import("scheduledtask.callservicecallbackscheduledtask.maxexecuteminutes", AllowDefault = true)]
        public int MaxExecuteMinutes { get; set; }

        [Import("scheduledtask.callservicecallbackscheduledtask.interval.seconds", AllowDefault = true)]
        public int IntervalSeconds { get; set; }

        [Import("scheduledtask.autodialscheduledtask.retrycount", AllowDefault = true)]
        public int RetryCount { get; set; }

        public int Interval
        {
            get
            {
                if (IntervalSeconds > 0)
                {
                    return IntervalSeconds * 1000;
                }

                return 60 * 1000; // Mặc định 1 phút
            }
        }

        [Import]
        public IRepository Repository { get; set; }

        [Import]
        public IQueryExecutor QueryExecutor { get; set; }

        public void Execute(DateTime? lastRunTime)
        {
            if (RetryCount == 0)
            {
                RetryCount = 3;
            }

            List<ContactCallResultServiceCallbackData> contactCallResultServiceCallbackList = QueryExecutor.ExecuteMany(new GetUncompletedContactCallResultServiceCallbackQuery { }).ToList();
            foreach (var contactCallResultServiceCallback in contactCallResultServiceCallbackList)
            {
                ContactCallResultServiceCallbackEntity contactCallResultServiceCallbackEntity = new ContactCallResultServiceCallbackEntity();
                Mapper.Map(contactCallResultServiceCallback, contactCallResultServiceCallbackEntity);

                try
                {
                    IVRRequestConfig coreServiceCallbackRequest = JsonConvert.DeserializeObject<IVRRequestConfig>(contactCallResultServiceCallback.ConcreteServiceCallbackRequest);

                    RestClient coreServiceCallbackRestClient = new RestClient(coreServiceCallbackRequest.Url)
                    {
                        Timeout = 15000
                    };
                    var coreServiceCallbackHttpRequest = new RestRequest();
                    coreServiceCallbackHttpRequest.Method = (RestSharp.Method)coreServiceCallbackRequest.Method.GetHashCode();

                    if (coreServiceCallbackRequest.Parameters != null && coreServiceCallbackRequest.Parameters.Count > 0)
                    {
                        foreach (var param in coreServiceCallbackRequest.Parameters)
                        {
                            if (param.Type == IVRRequestParameterType.HttpHeader)
                            {
                                coreServiceCallbackHttpRequest.AddHeader(param.Name, param.Value);
                            }
                            else
                            {
                                coreServiceCallbackHttpRequest.AddParameter(param.Name, param.Value);
                            }
                        }
                    }

                    IRestResponse coreServiceCallbackResponse = coreServiceCallbackRestClient.Execute(coreServiceCallbackHttpRequest);
                    contactCallResultServiceCallbackEntity.LastResponseStatusCode = coreServiceCallbackResponse.StatusCode.GetHashCode() + "-" + coreServiceCallbackResponse.StatusCode.ToString();

                    if (coreServiceCallbackResponse.StatusCode == System.Net.HttpStatusCode.OK)
                    {
                        contactCallResultServiceCallbackEntity.Status = ContactCallResultServiceCallbackStatus.Success;
                        contactCallResultServiceCallbackEntity.LastErrorMessage = string.Empty;
                    }
                    else
                    {
                        contactCallResultServiceCallbackEntity.LastErrorMessage = coreServiceCallbackResponse.ErrorMessage;
                        if (contactCallResultServiceCallbackEntity.RetryCount.HasValue)
                        {
                            contactCallResultServiceCallbackEntity.RetryCount++;
                        }
                        else
                        {
                            contactCallResultServiceCallbackEntity.RetryCount = 1;
                        }
                    }
                }
                catch (Exception ex)
                {
                    contactCallResultServiceCallbackEntity.Status = ContactCallResultServiceCallbackStatus.Error;
                    contactCallResultServiceCallbackEntity.LastErrorMessage = ex.Message;

                    if (contactCallResultServiceCallbackEntity.RetryCount.HasValue)
                    {
                        contactCallResultServiceCallbackEntity.RetryCount++;
                    }
                    else
                    {
                        contactCallResultServiceCallbackEntity.RetryCount = 1;
                    }
                }

                if (contactCallResultServiceCallbackEntity.RetryCount >= RetryCount)
                {
                    contactCallResultServiceCallbackEntity.Status = ContactCallResultServiceCallbackStatus.Failed;
                }

                Repository.Save(contactCallResultServiceCallbackEntity);
            }
        }
    }
}
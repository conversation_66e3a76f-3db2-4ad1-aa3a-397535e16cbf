﻿using AutoMapper;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using TinyCRM.ServiceCategory;
using TinyCRM.Workflow.Commands;
using Webaby;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.ServiceType.Commands
{
    public class CloneServiceTypeCommand : CommandBase
    {
        public Guid FromServiceTypeId { get; set; }
        public Guid NewServiceTypeId { get; set; }

        public bool CloneDynamicForm { get; set; }
        public Guid NewDynamicFormId { get; set; }

        public bool CloneDynamicDefinedTableSchema { get; set; }

        public bool CloneWorkflow { get; set; }
        public Guid NewWorkflowId { get; set; }

        public bool CloneTaskType { get; set; }

        public bool CloneChildWorkflow { get; set; }

        public bool CloneAutoCondition { get; set; }

        public String CloneName { get; set; }
        public String CloneCode { get; set; }
    }

    internal class CloneServiceTypeCommandHandler : CommandHandlerBase<CloneServiceTypeCommand>
    {
        public CloneServiceTypeCommandHandler(
            IText text,
            IMapper mapper,
            IRepository repository,
            IEntitySet entitySet,
            IQueryExecutor queryExecutor,
            ICommandExecutor commandExecutor,
            ILocalTransactionManager transactionManager,
            IEventBus eventBus
        ) : base(text, mapper, repository, entitySet, queryExecutor, commandExecutor, transactionManager, eventBus) { }

        public override async Task ExecuteAsync(CloneServiceTypeCommand command)
        {
            var fromServiceTypeEntity = await EntitySet.GetAsync<ServiceTypeEntity>(command.FromServiceTypeId);
            
            // Clone ra 1 ServiceType ngang cấp với ServiceType được Clone ra
            // Có nghĩa là ServiceType được clone đi tới Level nào, thì ServiceType được tạo ra cũng sẽ có tới Level đó
            // Chỉ có Level cuối sẽ được tạo ra ServiceCategory mới với tên là CloneName
            var newServiceTypeEntity = new ServiceTypeEntity();
            Mapper.Map(fromServiceTypeEntity, newServiceTypeEntity);
            newServiceTypeEntity.Id = command.NewServiceTypeId;
            newServiceTypeEntity.CreatedDate = DateTime.Now;

            //if (Container.One<IDynamicFormUtility>().CheckDuplicateCodeServiceType(command.CloneCode, command.NewServiceTypeId))
            //{
            //    throw new Exception(T["Mã bị trùng vui lòng nhập lại"]);
            //}

            var createServiceCategories = new List<ServiceCategoryEntity>();
            
            if (fromServiceTypeEntity.Level4Id.IsNotNullOrEmpty())
            {
                var serviceCategory = await EntitySet.GetAsync<ServiceCategoryEntity>(fromServiceTypeEntity.Level4Id.Value);
                if (serviceCategory == null)
                {
                    serviceCategory.Id = Guid.NewGuid();
                    serviceCategory.Name = command.CloneName;
                    serviceCategory.Code = command.CloneCode;
                    serviceCategory.Type = Enums.ServiceCategoryType.Product;
                    serviceCategory.CreatedDate = DateTime.Now;

                    //if (Container.One<IDynamicFormUtility>().CheckDuplicateNameAndCodeServiceCategory("name", serviceCategory.Name, serviceCategory.Type, serviceCategory.Id))
                    //{
                    //    //throw new Exception(T["Tên loại dịch vụ: {0} bị trùng trong level {1}", serviceCategory.Name, (int)serviceCategory.Type]);
                    //    throw new Exception(T["Tên bị trùng vui lòng nhập lại"]);
                    //}
                    //if (Container.One<IDynamicFormUtility>().CheckDuplicateNameAndCodeServiceCategory("code", serviceCategory.Code, serviceCategory.Type, serviceCategory.Id))
                    //{
                    //    throw new Exception(T["Mã bị trùng vui lòng nhập lại"]);
                    //}

                    createServiceCategories.Add(serviceCategory);
                    newServiceTypeEntity.Level4Id = serviceCategory.Id;
                }
            }            
            else if (fromServiceTypeEntity.Level3Id.IsNotNullOrEmpty())
            {
                var serviceCategory = await EntitySet.GetAsync<ServiceCategoryEntity>(fromServiceTypeEntity.Level3Id.Value);
                if (serviceCategory != null)
                {
                    serviceCategory.Id = Guid.NewGuid();
                    serviceCategory.Name = command.CloneName;
                    serviceCategory.Code = command.CloneCode;
                    serviceCategory.Type = Enums.ServiceCategoryType.GroupProduct;
                    serviceCategory.CreatedDate = DateTime.Now;

                    //if (Container.One<IDynamicFormUtility>().CheckDuplicateNameAndCodeServiceCategory("name", serviceCategory.Name, serviceCategory.Type, serviceCategory.Id))
                    //{
                    //    throw new Exception(T["Tên bị trùng vui lòng nhập lại"]);
                    //}
                    //if (Container.One<IDynamicFormUtility>().CheckDuplicateNameAndCodeServiceCategory("code", serviceCategory.Code, serviceCategory.Type, serviceCategory.Id))
                    //{
                    //    throw new Exception(T["Mã bị trùng vui lòng nhập lại"]);
                    //}

                    createServiceCategories.Add(serviceCategory);
                    newServiceTypeEntity.Level3Id = serviceCategory.Id;
                }
            }
            else if (fromServiceTypeEntity.Level2Id != null)
            {
                var serviceCategory = await EntitySet.GetAsync<ServiceCategoryEntity>(fromServiceTypeEntity.Level2Id.Value);
                if (serviceCategory != null)
                {
                    serviceCategory.Id = Guid.NewGuid();
                    serviceCategory.Name = command.CloneName;
                    serviceCategory.Code = command.CloneCode;
                    serviceCategory.Type = Enums.ServiceCategoryType.Service;
                    serviceCategory.CreatedDate = DateTime.Now;

                    //if (Container.One<IDynamicFormUtility>().CheckDuplicateNameAndCodeServiceCategory("name", serviceCategory.Name, serviceCategory.Type, serviceCategory.Id))
                    //{
                    //    throw new Exception(T["Tên bị trùng vui lòng nhập lại"]);
                    //}
                    //if (Container.One<IDynamicFormUtility>().CheckDuplicateNameAndCodeServiceCategory("code", serviceCategory.Code, serviceCategory.Type, serviceCategory.Id))
                    //{
                    //    throw new Exception(T["Mã bị trùng vui lòng nhập lại"]);
                    //}

                    createServiceCategories.Add(serviceCategory);
                    newServiceTypeEntity.Level2Id = serviceCategory.Id;
                }
            }
            else if (fromServiceTypeEntity.Level1Id != null)
            {
                var serviceCategory = await EntitySet.GetAsync<ServiceCategoryEntity>(fromServiceTypeEntity.Level1Id.Value);
                if (serviceCategory != null)
                {
                    serviceCategory.Id = Guid.NewGuid();
                    serviceCategory.Name = command.CloneName;
                    serviceCategory.Code = command.CloneCode;
                    serviceCategory.Type = Enums.ServiceCategoryType.Request;
                    serviceCategory.CreatedDate = DateTime.Now;

                    //if (Container.One<IDynamicFormUtility>().CheckDuplicateNameAndCodeServiceCategory("name", serviceCategory.Name, serviceCategory.Type, serviceCategory.Id))
                    //{
                    //    throw new Exception(T["Tên bị trùng vui lòng nhập lại"]);
                    //}
                    //if (Container.One<IDynamicFormUtility>().CheckDuplicateNameAndCodeServiceCategory("code", serviceCategory.Code, serviceCategory.Type, serviceCategory.Id))
                    //{
                    //    throw new Exception(T["Mã bị trùng vui lòng nhập lại"]);
                    //}

                    createServiceCategories.Add(serviceCategory);
                    newServiceTypeEntity.Level1Id = serviceCategory.Id;
                }
            }
            await Repository.SaveAsync(createServiceCategories);

            if (fromServiceTypeEntity.DynamicFormId.IsNotNullOrEmpty() && command.CloneDynamicForm)
            {
                //await CommandExecutor.ExecuteAsync(new CloneDynamicFormCommand
                //{
                //    DynamicFormId = fromServiceTypeEntity.DynamicFormId.Value,
                //    NewDynamicFormId = command.NewDynamicFormId,
                //    CloneName = command.CloneName,
                //    CloneCode = command.CloneCode,
                //    CloneDynamicDefinedTableSchema = command.CloneDynamicDefinedTableSchema
                //});

                newServiceTypeEntity.DynamicFormId = command.NewDynamicFormId;
            }

            if (fromServiceTypeEntity.WorkflowId.IsNotNullOrEmpty() && command.CloneWorkflow)
            {
                await CommandExecutor.ExecuteAsync(new CloneWorkflowCommand
                {
                    WorkflowId = fromServiceTypeEntity.WorkflowId.Value,
                    NewWorkflowId = command.NewWorkflowId,
                    CloneName = command.CloneName,
                    CloneCode = command.CloneCode,
                    CloneTaskType = command.CloneTaskType,
                    CloneChildWorkflow = command.CloneChildWorkflow,
                    CloneAutoCondition = command.CloneAutoCondition,
                });

                newServiceTypeEntity.WorkflowId = command.NewWorkflowId;
            }
            
            newServiceTypeEntity.Code = command.CloneCode;
            await Repository.SaveAsync(newServiceTypeEntity);

            var serviceTypeRoles = (await EntitySet.GetAsync<ServiceTypeRoleEntity>()).Where(x => x.ServiceTypeId == command.FromServiceTypeId).ToList();
            if (serviceTypeRoles.Count > 0)
            {
                await Repository.SaveAsync(serviceTypeRoles.Select(x => new ServiceTypeRoleEntity
                {
                    Id = Guid.NewGuid(),
                    RoleId = x.RoleId,
                    ServiceTypeId = command.NewServiceTypeId,                    
                }));
            }
        }
    }
}
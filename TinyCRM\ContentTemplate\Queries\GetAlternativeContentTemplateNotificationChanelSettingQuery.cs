﻿using AutoMapper;
using System;
using System.Data;
using System.Data.SqlClient;
using TinyCRM.ContentTemplate.Queries;
using TinyCRM.ScheduledTaskConfig;
using Webaby;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.ContentTemplate.Queries
{
    public class GetAlternativeContentTemplateNotificationChanelSettingQuery : QueryBase<AlternativeNotiChannelContentTemplateData>
    {
        public Guid? ServiceTypeId { get; set; }

        public Guid? Level1Id { get; set; }

        public Guid? Level2Id { get; set; }

        public Guid? Level3Id { get; set; }

        public Guid? Level4Id { get; set; }

        public Guid? TaskTypeId { get; set; }

        public Guid NotificationChannelSettingId { get; set; }
        
    }

    internal class GetAlternativeContentTemplateNotificationChanelSettingQueryHandler : QueryHandlerBase<GetAlternativeContentTemplateNotificationChanelSettingQuery, AlternativeNotiChannelContentTemplateData>
    {
        public GetAlternativeContentTemplateNotificationChanelSettingQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<AlternativeNotiChannelContentTemplateData>> ExecuteAsync(GetAlternativeContentTemplateNotificationChanelSettingQuery query)
        {
            var cmd = EntitySet.CreateDbCommand();
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@ServiceTypeId", query.ServiceTypeId));
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@Level1Id", query.Level1Id));
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@Level2Id", query.Level2Id));
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@Level3Id", query.Level3Id));
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@Level4Id", query.Level4Id));
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@TaskTypeId", query.TaskTypeId));
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@NotificationChannelSettingId", query.NotificationChannelSettingId));

            cmd.CommandText = "GetAlternativeContentTemplateNotificationChanelSetting";
            cmd.CommandType = CommandType.StoredProcedure;
            var mainQuery = await EntitySet.ExecuteReadCommandAsync<AlternativeNotiChannelContentTemplateData>(cmd);
            return new QueryResult<AlternativeNotiChannelContentTemplateData>(mainQuery);
        }
    }
}

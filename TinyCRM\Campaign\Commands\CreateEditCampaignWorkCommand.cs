﻿using System;
using TinyCRM.Enums;
using Webaby;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;

namespace TinyCRM.Campaign.Commands
{
    public class CreateEditCampaignWorkCommand : CommandBase
    {
        public Guid Id { get; set; }

        public Guid CampaignId { get; set; }

        public Guid ReferenceObjectId { get; set; }

        public string ReferenceObjectType { get; set; }

        public Guid? ResultId { get; set; }

        public string ResultObjectType { get; set; }

        public CampaignWorkStatus Status { get; set; }

        public Guid? CurrentAssignmentId { get; set; }

        public Guid CreatedBy { get; set; }

        public DateTime CreatedDate { get; set; }
    }

    internal class CreateEditCampaignWorkCommandHandler : CommandHandlerBase<CreateEditCampaignWorkCommand>
    {
        public CreateEditCampaignWorkCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task ExecuteAsync(CreateEditCampaignWorkCommand command)
        {
            var entity = await EntitySet.GetAsync<CampaignWorkEntity>(command.Id);
            if (entity == null)
            {
                entity = new CampaignWorkEntity();
            }
            entity.Id = command.Id;
            entity.CampaignId = command.CampaignId;
            entity.ReferenceObjectId = command.ReferenceObjectId;
            entity.ReferenceObjectType = command.ReferenceObjectType;
            entity.ResultId = command.ResultId;
            entity.ResultObjectType = command.ResultObjectType;
            entity.Status = command.Status;
            entity.CurrentAssignmentId = command.CurrentAssignmentId;
            entity.CreatedBy = command.CreatedBy;
            entity.CreatedDate = command.CreatedDate;

            await Repository.SaveAsync(entity);
        }
    }
}

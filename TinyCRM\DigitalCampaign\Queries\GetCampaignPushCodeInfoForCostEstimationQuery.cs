﻿using System;
using System.Data.SqlClient;
using Webaby.Data;
using Webaby;
using System.Data;
using System.Threading.Tasks;
using AutoMapper;
using Webaby.Localization;

namespace TinyCRM.DigitalCampaign.Queries
{
    public class GetCampaignPushCodeInfoForCostEstimationQuery : QueryBase<CampaignPushCodeInfoForCostEstimationData>
    {
        public Guid CampaignId { get; set; } 
    }

    internal class GetCampaignPushCodeInfoForCostEstimationQueryHandler : QueryHandlerBase<GetCampaignPushCodeInfoForCostEstimationQuery, CampaignPushCodeInfoForCostEstimationData>
    {
        public GetCampaignPushCodeInfoForCostEstimationQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<CampaignPushCodeInfoForCostEstimationData>> ExecuteAsync(GetCampaignPushCodeInfoForCostEstimationQuery query)
        {
            var cmd = EntitySet.CreateDbCommand();
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@CampaignId", query.CampaignId));
            cmd.CommandText = "dbo.GetCampaignPushCodeInfoForCostEstimation";
            cmd.CommandType = CommandType.StoredProcedure;
            var mainQuery = await EntitySet.ExecuteReadCommandAsync<CampaignPushCodeInfoForCostEstimationData>(cmd);
            return new QueryResult<CampaignPushCodeInfoForCostEstimationData>(mainQuery);
        }
    }
}
﻿using System;
using Webaby;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;

namespace TinyCRM.Campaign.Commands
{
    public class DeleteCampaignWorkerCommand : CommandBase
    {
        public Guid Id { get; set; }
    }

    internal class DeleteCampaignWorkerCommandHandler : CommandHandlerBase<DeleteCampaignWorkerCommand>
    {
        public DeleteCampaignWorkerCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task ExecuteAsync(DeleteCampaignWorkerCommand command)
        {
            var entity = await EntitySet.GetAsync<CampaignWorkerEntity>(command.Id);
            if (entity != null)
            {
                await Repository.DeleteAsync(entity);
            }
        }
    }
}

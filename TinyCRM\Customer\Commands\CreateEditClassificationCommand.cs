﻿using AutoMapper;
using System;
using System.Threading.Tasks;
using TinyCRM.Enums;
using Webaby;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.Customer.Commands
{
    public class CreateEditClassificationCommand : CommandBase
    {
        public Guid Id { get; set; }

        public string Name { get; set; }

        public string Code { get; set; }

        public CustomerType Type { get; set; }
    }

    internal class CreateEditClassificationCommandHandler : CommandHandlerBase<CreateEditClassificationCommand>
    {
        public CreateEditClassificationCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task ExecuteAsync(CreateEditClassificationCommand command)
        {
            var entity = await EntitySet.GetAsync<ClassificationEntity>(command.Id);
            if (entity == null)
            {
                entity = new ClassificationEntity();
            }
            Mapper.Map(command, entity);
            await Repository.SaveAsync(entity);
        }
    }
}
﻿using System;
using System.Data;
using System.Data.SqlClient;
using Webaby;
using Webaby.Data;

namespace TinyCRM.Customer.Queries
{
    public class GetAgencyHierarchyQuery : QueryBase<CustomerData>
    {
        public string ParentNode { get; set; } 
    }

    internal class GetAgencyHierarchyQueryHandler: QueryHandlerBase<GetAgencyHierarchyQuery, CustomerData>
    {
        public GetAgencyHierarchyQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }
        public override async Task<QueryResult<CustomerData>> ExecuteAsync(GetAgencyHierarchyQuery query)
        {
            var cmd = EntitySet.CreateDbCommand();
            cmd.Parameters.AddRange(new[]
            {
                DbParameterHelper.AddNullableString(cmd ,"@ParentNode", query.ParentNode)
            });

            cmd.CommandText = "dbo.GetAgencyHierarchy";
            cmd.CommandType = CommandType.StoredProcedure;
            var mainQuery = await EntitySet.ExecuteReadCommandAsync<CustomerData>(cmd);
            return new QueryResult<CustomerData>(mainQuery);
        }
    }
}

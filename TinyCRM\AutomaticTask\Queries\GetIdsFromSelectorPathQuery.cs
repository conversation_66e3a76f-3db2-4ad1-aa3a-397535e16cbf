﻿using Webaby.Data;
using AutoMapper;
using Webaby.Localization;
using Webaby;
using System.Threading.Tasks;
using System;
using System.Data;
using System.Text;
using System.Text.RegularExpressions;
using Webaby.Core.UserAccount.Queries;

namespace TinyCRM.AutomaticTask.Queries
{
    public class GetIdsFromSelectorPathQuery : QueryBase<Guid>
    {
        public string Path { get; set; }
        public Guid OriginId { get; set; }
    }

    internal class GetIdsFromSelectorPathQueryHandler : QueryHandlerBase<GetIdsFromSelectorPathQuery, Guid>
    {
        public GetIdsFromSelectorPathQueryHandler(IServiceProvider serviceProvider)
            : base(serviceProvider) { }

        public override async Task<QueryResult<Guid>> ExecuteAsync(GetIdsFromSelectorPathQuery query)
        {
            var callQueryString = new StringBuilder();
            var functionPattern = new Regex(@"(?<Function>[\s\w]+$?)(\[(?<PropName>[\s\w]+$?)='(?<PropValue>[\s\S]+$?)'\])?", RegexOptions.Compiled);
            var selectorPath = query.Path.Split('.');
            if(selectorPath.Length > 0)
            {
                for (int i = 0; i < selectorPath.Length; i++)
                {
                    var parsePath = selectorPath[i];
                    if (functionPattern.IsMatch(parsePath))
                    {
                        string functionName = functionPattern.Match(selectorPath[i]).Groups["Function"].ToString();
                        string propName = functionPattern.Match(selectorPath[i]).Groups["PropName"].ToString();
                        string propValue = functionPattern.Match(selectorPath[i]).Groups["PropValue"].ToString();
                        string inputName = i == 0 ? "@inputOrigin" : string.Format("@output{0}", i - 1);
                        if (i == 0)
                        {
                            callQueryString.AppendLine("DECLARE @inputOrigin IdList");
                            callQueryString.AppendLine(string.Format("INSERT INTO @inputOrigin (Id) VALUES ('{0}')", query.OriginId));
                        }
                        callQueryString.AppendLine(string.Format("DECLARE @output{0} IdList", i));
                        if (string.IsNullOrEmpty(propName) && string.IsNullOrEmpty(propValue))
                        {
                            callQueryString.AppendLine(string.Format("INSERT INTO @output{0} EXEC dbo.{1} {2}", i, functionName, inputName));
                        }
                        else
                        {
                            callQueryString.AppendLine(string.Format("INSERT INTO @output{0} EXEC dbo.{1} {2}, N'{3}', N'{4}'", i, functionName, inputName, propName, propValue));
                        }
                    }
                }
                callQueryString.AppendLine(string.Format("SELECT Id FROM @output{0}", selectorPath.Length - 1));
            }
            var commandText = callQueryString.ToString();
            var sqlCommand = EntitySet.CreateDbCommand();
            sqlCommand.CommandText = commandText;
            sqlCommand.CommandType = CommandType.Text;
            var mainQuery = await EntitySet.ExecuteReadCommandAsync<Guid>(sqlCommand);
            return new QueryResult<Guid>(mainQuery);
        }
    }
}

﻿using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;
using TinyCRM.Geolocation;
using Webaby;

namespace TinyCRM.Customer.Queries
{
    public class GetCustomerAlternativeAddressByIdQuery : QueryBase<CustomerAlternativeAddressData>
    {
        public GetCustomerAlternativeAddressByIdQuery(Guid id)
        {
            Id = id;
        }

        public Guid Id { get; set; }
    }

    internal class GetCustomerAlternativeAddressByIdQueryHandler :
        QueryHandlerBase<GetCustomerAlternativeAddressByIdQuery, CustomerAlternativeAddressData>
    {
        public GetCustomerAlternativeAddressByIdQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<CustomerAlternativeAddressData>> ExecuteAsync(GetCustomerAlternativeAddressByIdQuery query)
        {
            var customerAlternativeAddressData = await(from cusAlt in EntitySet.Get<CustomerAlternativeAddressEntity>()
                                                   join ward in EntitySet.Get<GeolocationEntity>() on cusAlt.WardId equals ward.Id into wardN
                                                   from wardX in wardN.DefaultIfEmpty()
                                                   join district in EntitySet.Get<GeolocationEntity>() on cusAlt.DistrictId equals district.Id into
                                                       districtN
                                                   from districtX in districtN.DefaultIfEmpty()
                                                   join province in EntitySet.Get<GeolocationEntity>() on cusAlt.ProvinceId equals province.Id into
                                                       provinceN
                                                   from provinceX in provinceN.DefaultIfEmpty()
                                                   join classification in EntitySet.Get<ClassificationEntity>() on cusAlt.ClassificationId equals classification.Id into
                                                       classificationN
                                                   from classificationX in classificationN.DefaultIfEmpty()
                                                   where cusAlt.Id == query.Id
                                                   select new CustomerAlternativeAddressData
                                                   {
                                                       Id = cusAlt.Id,
                                                       AddressStreet = cusAlt.AddressStreet,
                                                       AddressNumber = cusAlt.AddressNumber,
                                                       Code = cusAlt.Code,
                                                       ProvinceId = cusAlt.ProvinceId,
                                                       ProvinceName = provinceX.Name,
                                                       DistrictId = cusAlt.DistrictId,
                                                       DistrictName = districtX.Name,
                                                       WardId = cusAlt.WardId,
                                                       WardName = wardX.Name,
                                                       FullAddress = cusAlt.FullAddress,
                                                       Name = cusAlt.Name,
                                                       Phone = cusAlt.Phone,
                                                       ClassificationId = cusAlt.ClassificationId,
                                                       ClassificationCode = classificationX.Code,
                                                   }).ToListAsync();
            return new QueryResult<CustomerAlternativeAddressData>(customerAlternativeAddressData);
        }
    }
}

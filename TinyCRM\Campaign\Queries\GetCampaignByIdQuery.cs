﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;
using TinyCRM.Outbound.Campaign;

namespace TinyCRM.Campaign.Queries
{
    public class GetCampaignByIdQuery : QueryBase<CampaignData>
    {
        public Guid Id { get; set; }

        public GetCampaignByIdQuery(Guid id)
        {
            Id = id;
        }
    }

    internal class GetCampaignByIdQueryHandler : QueryHandlerBase<GetCampaignByIdQuery, CampaignData>
    {
        public GetCampaignByIdQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<CampaignData>> ExecuteAsync(GetCampaignByIdQuery query)
        {
            var campaignQuery = await EntitySet.GetAsync<CampaignEntity>();
            var result = campaignQuery.FirstOrDefault(x => x.Id == query.Id);
            return new QueryResult<CampaignData>(Mapper.Map<CampaignData>(result));
        }
    }
}

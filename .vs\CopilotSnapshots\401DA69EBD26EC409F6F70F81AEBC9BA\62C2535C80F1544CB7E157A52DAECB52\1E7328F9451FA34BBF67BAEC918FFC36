﻿using System;
using System.ComponentModel.Composition;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Threading;
using System.Web;
using System.Web.Mvc;
using TinyCRM.RequestTicket.Commands;
using TinyCRM.Survey;
using Webaby;
using Webaby.Data;

namespace TinyCRM.RequestTicket.Events
{
    public class RequestTicketCreatedEvent : Event
    {
        public Guid RequestTicketId { get; set; }

        public Guid CustomerId { get; set; }

        public Guid ServiceTypeId { get; set; }

        public Guid CreatedBy { get; set; }

        public Guid? OwnerId { get; set; }

        public Guid? OwnedByOrganizationId { get; set; }

        public DateTime CreatedDate { get; set; }

        public string PhoneNumber { get; set; }

        public string Email { get; set; }

        public UrlHelper Url { get; set; }

        public HttpRequestBase Request { get; set; }

        public Guid DynamicFormValueId { get; set; }
    }

    public class TableTemp
    {
        public Guid? Id { get; set; }
    }

    public class TableAmountInfo
    {
        public Guid UsedAmountTicketId { get; set; }
        public Guid MaxAmountTicketId { get; set; }
    }

    public class RequestTicketCreatedEventHandler : IEventHandler<RequestTicketCreatedEvent>
    {
        public ICommandExecutor CommandExecutor { get; }
        public IEntitySet EntitySet { get; }
        public ILogger Logger { get; }

        public RequestTicketCreatedEventHandler(
            ICommandExecutor commandExecutor,
            IEntitySet entitySet,
            ILogger logger)
        {
            CommandExecutor = commandExecutor;
            EntitySet = entitySet;
            Logger = logger;
        }

        public void Handle(RequestTicketCreatedEvent @event)
        {
            if (@event.CustomerId != Guid.Empty)
            {
                var createTicketAutomaticSurveyFeedbacksCommand = new CreateTicketAutomaticSurveyFeedbacksCommand
                {
                    RequestTicketId = @event.RequestTicketId,
                    ServiceTypeId = @event.ServiceTypeId,
                    CustomerId = @event.CustomerId,
                    CreatedBy = @event.CreatedBy,
                    OwnerId = @event.OwnerId,
                    OwnedByOrganizationId = @event.OwnedByOrganizationId,
                    CreatedDate = @event.CreatedDate,
                    PhoneNumber = @event.PhoneNumber,
                    Email = @event.Email,
                    SurveyEvent = SurveyEvent.TicketCreated
                };

                var thread = new Thread(() =>
                {
                    try
                    {
                        CommandExecutor.Execute(createTicketAutomaticSurveyFeedbacksCommand);
                    }
                    catch (Exception ex)
                    {
                        Logger.Error(ex.Message);
                    }
                });

                thread.Start();
            }
            else
            {
                SqlConnection sqlConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["Default"].ConnectionString);
                try
                {
                    sqlConnection.Open();
                    SqlCommand sqlServiceType = new SqlCommand();
                    sqlServiceType.Connection = sqlConnection;
                    sqlServiceType.CommandType = System.Data.CommandType.Text;
                    sqlServiceType.CommandText = @"SELECT Id FROM dbo.InfoList WHERE Type='system.demo.claim.servicetype.id'";

                    SqlDataAdapter sqlDataAdapterServiceType = new SqlDataAdapter(sqlServiceType);
                    DataTable dataTableServiceType = new DataTable();
                    sqlDataAdapterServiceType.Fill(dataTableServiceType);

                    var checkServiceTypeId = dataTableServiceType.ToObject<TableTemp>().FirstOrDefault();
                    if (checkServiceTypeId != null)
                    {
                        if (@event.ServiceTypeId == checkServiceTypeId.Id)
                        {
                            // get table UsedAmount , MaxAmount ID in Ticket
                            SqlCommand sqlQuery = new SqlCommand();
                            sqlQuery.Connection = sqlConnection;
                            sqlQuery.CommandType = System.Data.CommandType.Text;
                            sqlQuery.CommandText = @"
                            SELECT usedAmount.Id UsedAmountTicketId, maxAmount.Id MaxAmountTicketId FROM dbo.DynamicFieldValue usedAmount
                            LEFT JOIN (
	                            SELECT DynamicFormValueId, Id FROM dbo.DynamicFieldValue WHERE DynamicFormValueId = @DynamicFormValueId AND DynamicFieldId = 'EFDE246C-267D-4275-B70E-9630DB4C96DB'
                            ) maxAmount ON maxAmount.DynamicFormValueId = usedAmount.DynamicFormValueId
                            WHERE usedAmount.DynamicFormValueId = @DynamicFormValueId AND usedAmount.DynamicFieldId = 'CE74117F-E55A-43C4-948B-7B50D5FB4E01'
                        ";
                            sqlQuery.Parameters.AddRange(new[]
                            {
                                SqlParameterHelper.AddNullableGuid("@DynamicFormValueId", @event.DynamicFormValueId),
                            });
                            SqlDataAdapter sqlDataQuery = new SqlDataAdapter(sqlQuery);
                            DataTable dataTableQuery = new DataTable();
                            sqlDataQuery.Fill(dataTableQuery);
                            var tableId = dataTableQuery.ToObject<TableAmountInfo>().FirstOrDefault();
                            var currentTime = DateTime.Now;

                            //Insert Data Amount All Ticket Except itself
                            SqlCommand sqlInsertAmountCommand = new SqlCommand();
                            sqlInsertAmountCommand.CommandType = System.Data.CommandType.Text;
                            sqlInsertAmountCommand.CommandText = @"
                            INSERT INTO dbo.DynamicDefinedTable_info_use_category_amount_month(Id, DynamicDefinedTableSchemaId, DynamicFieldValueId, RowNumber, Category, Used_Amount)
                            SELECT NEWID(),'EDD058E8-1389-4393-9254-AEAE5630C120', @DynamicFieldValueId, ROW_NUMBER() OVER ( ORDER BY NEWID()), amount_all.Category,SUM(amount_all.Exchange_To_USD) FROM dbo.RequestTicket rt
                            JOIN dbo.DynamicFieldValue dfv ON rt.DynamicFormValueId = dfv.DynamicFormValueId
                            JOIN dbo.DynamicDefinedTable_DEMO_LOCAL_CLAIM amount_all ON amount_all.DynamicFieldValueId = dfv.Id
                            WHERE  rt.ServiceTypeId = @ServiceTypeId AND rt.Status = 4 AND rt.Id <> @TicketId AND rt.OwnerId = @TicketOwnerId AND MONTH(rt.CreatedDate) = @CrMonth  AND YEAR(rt.CreatedDate) = @CrYear
                            GROUP BY amount_all.Category
                        ";
                            sqlInsertAmountCommand.Parameters.AddRange(new[]
                            {
                            SqlParameterHelper.AddNullableGuid("@DynamicFieldValueId", tableId.UsedAmountTicketId),
                            SqlParameterHelper.AddNullableGuid("@TicketId", @event.RequestTicketId),
                            SqlParameterHelper.AddNullableGuid("@ServiceTypeId", @event.ServiceTypeId),
                            SqlParameterHelper.AddNullableGuid("@TicketOwnerId", @event.OwnerId),
                            SqlParameterHelper.AddNullableInt("@CrMonth", currentTime.Month),
                            SqlParameterHelper.AddNullableInt("@CrYear", currentTime.Year)
                        });

                            EntitySet.ExecuteNonQuery(sqlInsertAmountCommand);
                        }
                    }
                }
                catch (Exception ex)
                {

                }
            }
        }
    }
}
﻿using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;
using TinyCRM.Geolocation;
using Webaby;

namespace TinyCRM.Customer.Queries
{
    public class GetCustomerBillingCodeByClassificationChannelIdQuery : QueryBase<CustomerBillingCodeData>
    {
        public GetCustomerBillingCodeByClassificationChannelIdQuery(Guid id)
        {
            Id = id;
        }

        public Guid Id { get; set; }
    }

    internal class GetCustomerBillingCodeByChannelQueryHandler :
        QueryHandlerBase<GetCustomerBillingCodeByClassificationChannelIdQuery, CustomerBillingCodeData>
    {
        public GetCustomerBillingCodeByChannelQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }
        public override async Task<QueryResult<CustomerBillingCodeData>> ExecuteAsync(GetCustomerBillingCodeByClassificationChannelIdQuery query)
        {
            var customerBillingCodeData = await(from cb in EntitySet.Get<CustomerBillingCodeEntity>()
                                          join cus in EntitySet.Get<CustomerEntity>() on cb.CustomerId equals cus.Id
                                          join cls in EntitySet.Get<ClassificationEntity>() on cus.SourceClassificationId equals cls.Id
                                          join ch in EntitySet.Get<ClassificationChannelEntity>() on cls.ClassificationChannelId equals ch.Id
                                          join cusAlt in EntitySet.Get<CustomerAlternativeAddressEntity>() on cb.Id equals
                                              cusAlt.CustomerBillingCodeId into cusAltN
                                          from cusAltX in cusAltN.DefaultIfEmpty()
                                          join geo in EntitySet.Get<GeolocationEntity>() on cusAltX.ProvinceId equals geo.Id into geoN
                                          from geoX in geoN.DefaultIfEmpty()
                                          where ch.Id == query.Id
                                          select new CustomerBillingCodeData
                                          {
                                              Id = cb.Id,
                                              Code = cb.Code,
                                              CustomerId = cb.CustomerId,
                                              CustomerName = cus.Name,
                                              ProvinceId = cusAltX.ProvinceId,
                                              ProvinceName = geoX.Name,
                                          }).ToListAsync();
            return new QueryResult<CustomerBillingCodeData>(customerBillingCodeData);
        }
    }
}

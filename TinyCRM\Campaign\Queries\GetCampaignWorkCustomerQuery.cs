﻿using System;
using System.Data;
using System.Threading.Tasks;
using AutoMapper;
using TinyCRM.Enums;
using Webaby;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.Campaign.Queries
{
    public class GetCampaignWorkCustomerQuery : QueryBase<WorkCustomerListItem>
    {
        public Guid CampaignId { get; set; }

        public Guid OrganizationId { get; set; }

        public string CustomerCode { get; set; }

        public string CustomerName { get; set; }

        public string PhoneNumber { get; set; }

        public Guid UserCallId { get; set; }

        public AssignmentStatus Status { get; set; }
    }

    internal class GetCampaignWorkCustomerQueryHandler :
        QueryHandlerBase<GetCampaignWorkCustomerQuery, WorkCustomerListItem>
    {
        public GetCampaignWorkCustomerQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<WorkCustomerListItem>> ExecuteAsync(GetCampaignWorkCustomerQuery query)
        {
            var customerName = string.Empty;
            if (!string.IsNullOrEmpty(query.CustomerName))
            {
                string tempCustomerName = query.CustomerName.Trim(' ').Replace(",", "").Replace(";", "");
                while (tempCustomerName.Contains("  "))
                {
                    tempCustomerName = tempCustomerName.Replace("  ", " ");
                }

                customerName = tempCustomerName.Replace(" ", " and ");
            }
            var cmd = EntitySet.CreateDbCommand();
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@campaignId", query.CampaignId));
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@organizationId", query.OrganizationId));
            cmd.Parameters.Add(DbParameterHelper.AddNullableString(cmd, "@customerCode", query.CustomerCode));
            cmd.Parameters.Add(DbParameterHelper.AddNullableString(cmd, "@customerName", customerName));
            cmd.Parameters.Add(DbParameterHelper.AddNullableString(cmd, "@phoneNumber", query.PhoneNumber));
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@userCallId", query.UserCallId));
            cmd.Parameters.Add(DbParameterHelper.AddNullableEnum(cmd, "@status", query.Status));
            cmd.Parameters.Add(DbParameterHelper.AddNullableInt(cmd, "@startRow", query.Pagination.StartRow));
            cmd.Parameters.Add(DbParameterHelper.AddNullableInt(cmd, "@endRow", query.Pagination.EndRow));
            cmd.CommandText = "GetCampaignWorkCustomer";
            cmd.CommandType = CommandType.StoredProcedure;
            var mainQuery = await EntitySet.ExecuteReadCommandAsync<WorkCustomerListItem>(cmd);
            return new QueryResult<WorkCustomerListItem>(mainQuery);
        }
    }
}

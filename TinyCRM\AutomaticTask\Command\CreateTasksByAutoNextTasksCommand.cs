﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.ComponentModel.Composition;
using System.Data;
using System.Linq;
using TinyCRM.AutomaticTask.Events;
using TinyCRM.AutomaticTask.Queries;
using TinyCRM.Enums;
using TinyCRM.Phase;
using TinyCRM.Phase.Command;
using TinyCRM.Phase.Queries;
using TinyCRM.RequestTicket;
using TinyCRM.RequestTicket.Queries;
using TinyCRM.ServiceType.Queries;
using TinyCRM.TaskType.Queries;
using TinyCRM.UserTaskAssignmentRouting;
using TinyCRM.Workflow;
using TinyCRM.Workflow.Queries;
using Webaby;
using Webaby.Core.DynamicForm;
using TinyCRM.Mail.Commands;
using TinyCRM.Mail;
using Webaby.MailClient;
using Webaby.Security;
using Webaby.Core.UserAccount;
using TinyCRM.RequestTicket.Commands;
using TinyCRM.RequestTicket.Events;
using TinyCRM.WorkflowTaskTypeGroup.Queries;
using Webaby.Core.DynamicForm.Queries;
using System.Globalization;
using TinyCRM.DynamicForm.Command;
using Webaby.Data;
using TinyCRM.DynamicForm.Query;
using AutoMapper;
using Webaby.Localization;

namespace TinyCRM.AutomaticTask.Command
{
    public class CreateTasksByAutoNextTasksCommand : CommandBase
    {
        public Guid ServiceTypeId { get; set; }

        public Guid WorkflowId { get; set; }

        public Guid? TaskTypeId { get; set; }

        public Guid RequestTicketId { get; set; }

        public Guid? PhaseId { get; set; }

        public Guid? TaskId { get; set; }

        public Guid? TaskBusinessResultId { get; set; }

        public Guid? RequestTicketCreatorId { get; set; }

        public Guid? RequestTicketOwnerId { get; set; }

        public Guid? TaskCreatorId { get; set; }

        public Guid? TaskOwnerId { get; set; }

        public TaskInfo TaskInfo { get; set; }

        public RequestTicketData RequestTicketData { get; set; }

        public bool TriggeredByTaskDone { get; set; }

        public MailMessage FromMailMessage { get; set; }

        public RequestTicketUpdatedEvent RequestTicketUpdatedEvent { get; set; }

        public AutoTriggeredEvent? AutoTriggeredEvent { get; set; }

        public string ErrorMessage { get; set; }
    }

    internal class CreateTasksByAutoNextTasksCommandHandler : CommandHandlerBase<CreateTasksByAutoNextTasksCommand>
    {
        public CreateTasksByAutoNextTasksCommandHandler(
            IText text,
            IMapper mapper,
            IRepository repository,
            IEntitySet entitySet,
            IQueryExecutor queryExecutor,
            ICommandExecutor commandExecutor,
            ILocalTransactionManager transactionManager,
            IEventBus eventBus
        ) : base(text, mapper, repository, entitySet, queryExecutor, commandExecutor, transactionManager, eventBus) { }

        [Import("send.by.email", AllowDefault = true, AllowRecomposition = true)]
        public string SendByEmail { get; set; }

        [Import("autonexttask.ticket.assignmentrouting.dynamicdefinedtableschemaid", AllowDefault = true)]
        public Guid TicketAssignmentRoutingDynamicDefinedTableSchemaId { get; set; }

        [Import("autonexttask.ticket.assignmentrouting.tasktypecolumnid", AllowDefault = true)]
        public Guid TicketAssignmentRoutingTaskTypeColumnId { get; set; }

        [Import("autonexttask.ticket.assignmentrouting.assignedusercolumnid", AllowDefault = true)]
        public Guid TicketAssignmentRoutingAssignedUserColumnId { get; set; }

        [Import]
        public ILocalTransactionManager LocalTransactionManager { get; set; }

        [Import]
        ILogger Logger { get; set; }

        [Import]
        IUserService UserService { get; set; }

        [Import]
        ITaskCommand TaskCommand { get; set; }

        public override void Execute(CreateTasksByAutoNextTasksCommand command)
        {
            WorkflowData _workflowData;
            List<TaskTypeData> _taskTypeList;

            if(command.RequestTicketData == null)
            {
                //Gán lại data cho RequestTicketData
                var ticketData = QueryExecutor.ExecuteOne(new GetRequestTicketByIdQuery(command.RequestTicketId));
                if (ticketData != null) command.RequestTicketData = ticketData;
            }

            if (command.RequestTicketData != null && command.RequestTicketData.Status == RequestTicketStatus.Done)
            {
                return;
            }

            if (command.RequestTicketData != null && command.RequestTicketData.OwnerId.IsNullOrEmpty())
            {
                command.ErrorMessage = T["Không thể tạo mới tác vụ cho phiếu yêu cầu không có người phụ trách"];

                AutoNextTaskErrorLogEntity autoNextTaskErrorLogEntity = new AutoNextTaskErrorLogEntity();
                autoNextTaskErrorLogEntity.IsNew = true;
                autoNextTaskErrorLogEntity.Id = Guid.NewGuid();
                autoNextTaskErrorLogEntity.RequestTicketId = command.RequestTicketId;
                autoNextTaskErrorLogEntity.TaskId = command.TaskId;
                autoNextTaskErrorLogEntity.ErrorMessage = T["Lỗi tạo Tác vụ Tự động: Không thể tạo mới tác vụ cho phiếu yêu cầu không có người phụ trách"];

                Repository.Save(autoNextTaskErrorLogEntity);

                return;
            }

            Guid? workflowTaskTypeGroupId = null;

            try
            {
                _workflowData = QueryExecutor.ExecuteOne(new GetWorkflowByIdQuery { Id = command.WorkflowId });
                if (_workflowData != null && _workflowData.WorkflowType != WorkflowType.Mannual)
                {
                    Dictionary<Guid, int> _taskTypeListWorkingOrders = new Dictionary<Guid, int>();
                    _taskTypeList = QueryExecutor.Execute(new GetTaskTypeListByWorkflowQuery { WorkflowId = command.WorkflowId }).Many.ToList();
                    for (int i = 0; i < _taskTypeList.Count; i++)
                    {
                        _taskTypeListWorkingOrders.Add(_taskTypeList[i].Id, i + 1);
                    }

                    List<AutoNextTaskData> autoNextTasks = new List<AutoNextTaskData>();
                    List<AutoNextTaskData> matchedAutoNextTasks = new List<AutoNextTaskData>();

                    #region Lấy AutoNexTasks

                    if (command.TaskTypeId.IsNullOrEmpty())
                    {
                        autoNextTasks = QueryExecutor.ExecuteMany(new GetAutoNextTaskByReferenceObjectIdQuery { ReferenceObjectId = _workflowData.Id }).ToList();
                    }
                    else
                    {
                        // Kiểm tra xem Task có nằm trong TaskGroup nào hay không?
                        // Nếu có, ưu tiên lấy AutoNextTask của TaskGroup
                        var workflowTaskType = QueryExecutor.ExecuteOne(new GetWorkflowTaskTypeByWorkflowAndTaskTypeIdQuery { WorkflowId = _workflowData.Id, TaskTypeId = command.TaskTypeId.Value });
                        if (workflowTaskType != null && workflowTaskType.WorkflowTaskTypeGroupId.IsNotNullOrEmpty())
                        {
                            workflowTaskTypeGroupId = workflowTaskType.WorkflowTaskTypeGroupId.Value;
                            var workflowTaskTypeGroup = QueryExecutor.ExecuteOne(new GetWorkflowTaskTypeGroupByIdQuery { Id = workflowTaskType.WorkflowTaskTypeGroupId.Value });
                            autoNextTasks = QueryExecutor.ExecuteMany(new GetAutoNextTaskByReferenceObjectIdQuery { ReferenceObjectId = workflowTaskTypeGroup.Id }).ToList();
                        }

                        // Nếu Task không nằm trong TaskGroup nào
                        // Hoặc TaskGroup không có cấu hình AutoNextTask
                        if (autoNextTasks.Count == 0)
                        {
                            autoNextTasks = QueryExecutor.ExecuteMany(new GetAutoNextTaskByWorkflowAndTaskTypeQuery { WorkflowId = _workflowData.Id, TaskTypeId = command.TaskTypeId.Value, TaskBusinessResultId = command.TaskBusinessResultId }).ToList();
                        }
                    }

                    #endregion

                    if (command.TriggeredByTaskDone && command.TaskTypeId.IsNotNullOrEmpty())
                    {
                        #region Tiếp theo, xét coi có AutoAction nào đồng bộ Dynamic Form vào phiếu không?

                        List<AutoNextTaskData> copyDynamicFormToTicketAutoNextTasks = autoNextTasks.Where(ant => ant.AutoAction == AutoNextAction.CopyDynamicFormToTicket).ToList();
                        AutoNextTaskData matchedCopyDynamicFormToTicketAutoNextTask = null;
                        foreach (AutoNextTaskData autoNextTask in copyDynamicFormToTicketAutoNextTasks)
                        {
                            bool isMatchedAutoNextTask = IsMatchedConditionAutoNextTask(command, _taskTypeList, autoNextTask);
                            if (isMatchedAutoNextTask)
                            {
                                matchedCopyDynamicFormToTicketAutoNextTask = autoNextTask;
                                break;
                            }
                        }

                        if (matchedCopyDynamicFormToTicketAutoNextTask != null)
                        {
                            UserService.SetThreadCurrentUser(AutomaticTaskConstants.AutoTaskCreatorUserId);

                            if (command.RequestTicketData != null && command.RequestTicketData.DynamicFormValueId.IsNotNullOrEmpty() && command.TaskInfo.DynamicFormValueId.IsNotNullOrEmpty())
                            {
                                var ticketDynamicFieldValueList = QueryExecutor.Execute(new GetDynamicFieldValueInfoQuery { DynamicFormValueId = command.RequestTicketData.DynamicFormValueId.Value }).Many.ToList();
                                var taskDynamicFieldValueList = QueryExecutor.Execute(new GetDynamicFieldValueInfoQuery { DynamicFormValueId = command.TaskInfo.DynamicFormValueId.Value }).Many.ToList();

                                taskDynamicFieldValueList.CopyValuesTo(ticketDynamicFieldValueList);
                                ticketDynamicFieldValueList.Save(command.RequestTicketData.Id, "dbo.RequestTicket", Guid.NewGuid(), false);

                                if (ticketDynamicFieldValueList.Any(dfv => dfv.FieldType == FieldType.Calculated))
                                {
                                    //tranh parse sai kiey array list
                                    //CommandExecutor.Execute(new ReCalculateDynamicFormValueCommand { DynamicFormValueId = command.RequestTicketData.DynamicFormValueId.Value });
                                }
                            }

                            UserService.InvalidateThreadCurrentUser();
                        }

                        #endregion

                        #region Tiếp theo, xét coi có AutoAction nào cập nhật BusinessResult cho phiếu không?

                        List<AutoNextTaskData> updateTicketAutoNextTasks = autoNextTasks.Where(ant => ant.AutoAction == AutoNextAction.UpdateTicketBusinessResult && ant.RequestTicketClosedBusinessResultId.IsNotNullOrEmpty()).ToList();
                        AutoNextTaskData matchedUpdateTicketAutoNextTask = null;
                        foreach (AutoNextTaskData autoNextTask in updateTicketAutoNextTasks)
                        {
                            bool isMatchedAutoNextTask = IsMatchedConditionAutoNextTask(command, _taskTypeList, autoNextTask);
                            if (isMatchedAutoNextTask)
                            {
                                matchedUpdateTicketAutoNextTask = autoNextTask;
                                break;
                            }
                        }

                        if (matchedUpdateTicketAutoNextTask != null)
                        {
                            UserService.SetThreadCurrentUser(AutomaticTaskConstants.AutoTaskCreatorUserId);
                            CommandExecutor.Execute(new UpdateRequestTicketBusinessResultCommand
                            {
                                RequestTicketId = command.RequestTicketData.Id,
                                TicketBusinessResultId = matchedUpdateTicketAutoNextTask.RequestTicketClosedBusinessResultId.Value
                            });
                            UserService.InvalidateThreadCurrentUser();
                        }

                        #endregion

                        #region Trước tiên, xét coi có AutoAction nào yêu cầu Đóng phiếu hay không?

                        List<AutoNextTaskData> closedAutoNextTasks = autoNextTasks.Where(ant => ant.AutoAction == AutoNextAction.FinishRequestTicket || ant.AutoAction == AutoNextAction.FinishRequestTicketForced).ToList();
                        AutoNextTaskData matchedClosedAutoNextTask = null;
                        foreach (AutoNextTaskData autoNextTask in closedAutoNextTasks)
                        {
                            bool isMatchedAutoNextTask = IsMatchedConditionAutoNextTask(command, _taskTypeList, autoNextTask);
                            if (isMatchedAutoNextTask)
                            {
                                matchedClosedAutoNextTask = autoNextTask;
                                break;
                            }
                        }

                        if (matchedClosedAutoNextTask != null)
                        {
                            bool forcedFinish = false;
                            if (matchedClosedAutoNextTask.AutoAction == AutoNextAction.FinishRequestTicketForced)
                            {
                                forcedFinish = true;
                            }

                            EventBus.Publish(new FinishRequestTicketByAutoNextTaskMatchedEvent
                            {
                                RequestTicketId = command.RequestTicketId,
                                BusinessResultId = matchedClosedAutoNextTask.RequestTicketClosedBusinessResultId,
                                TaskInfo = command.TaskInfo,
                                RequestTicketData = command.RequestTicketData,
                                FromMailMessage = command.FromMailMessage,
                                ForcedFinish = forcedFinish
                            });

                            // Mặc định, đã ĐÓNG Phiếu thì KHÔNG còn làm gì thêm nữa, nên "Về đi em"
                            return;
                        }

                        #endregion

                        #region Tiếp theo, xét coi có AutoAction nào Đóng các tác vụ trong TaskGroup hay không?

                        if (workflowTaskTypeGroupId.IsNotNullOrEmpty())
                        {
                            List<AutoNextTaskData> closedTaskGroupAutoNextTasks = autoNextTasks.Where(ant => ant.AutoAction == AutoNextAction.CloseAllTaskInGroup).ToList();
                            AutoNextTaskData matchedClosedTaskGroupAutoNextTask = null;
                            foreach (AutoNextTaskData autoNextTask in closedTaskGroupAutoNextTasks)
                            {
                                bool isMatchedAutoNextTask = IsMatchedConditionAutoNextTask(command, _taskTypeList, autoNextTask);
                                if (isMatchedAutoNextTask)
                                {
                                    matchedClosedTaskGroupAutoNextTask = autoNextTask;
                                    break;
                                }
                            }

                            if (matchedClosedTaskGroupAutoNextTask != null)
                            {
                                var taskListInTaskTypeGroup = QueryExecutor.ExecuteMany(new GetTaskListInTaskTypeGroupQuery { RequestTicketId = command.RequestTicketData.Id, TaskTypeGroupId = workflowTaskTypeGroupId.Value });
                                foreach (var taskItem in taskListInTaskTypeGroup)
                                {
                                    if (taskItem.Status != TaskStatus.Done)
                                    {
                                        CommandExecutor.Execute(new CloseTaskCommand { TaskId = taskItem.Id, BusinessResultId = matchedClosedTaskGroupAutoNextTask.WorkflowTaskTypeClosedBusinessResultId, ClosedBy = AutomaticTaskConstants.AutoTaskCreatorUserId });
                                    }
                                }
                            }
                        }

                        #endregion

                        #region Sau đó, xét tiếp có AutoAction nào là "Điểm kết thúc quy trình" của Quy trình con hay không?

                        List<AutoNextTaskData> endChildAutoNextTasks = autoNextTasks.Where(ant => ant.AutoAction == AutoNextAction.EndChildWorkflow).ToList();
                        AutoNextTaskData matchedEndChildAutoNextTask = null;
                        foreach (AutoNextTaskData autoNextTask in endChildAutoNextTasks)
                        {
                            bool isMatchedAutoNextTask = IsMatchedConditionAutoNextTask(command, _taskTypeList, autoNextTask);
                            if (isMatchedAutoNextTask)
                            {
                                matchedEndChildAutoNextTask = autoNextTask;
                                break;
                            }
                        }

                        if (matchedEndChildAutoNextTask != null)
                        {
                            EventBus.Publish(new ChildWorkflowEndedEvent
                            {
                                TaskInfo = command.TaskInfo,
                            });
                        }

                        #endregion
                    }

                    if (_workflowData.WorkflowType == WorkflowType.AutoByDefaultOrganization)
                    {
                        Guid assignedUserId = Guid.Empty;
                        Guid createdTaskTypeId = Guid.Empty;

                        assignedUserId = GetAssignedUserIdByDefaultOrganization(command, _taskTypeList, out createdTaskTypeId);
                        if (createdTaskTypeId != Guid.Empty && assignedUserId != Guid.Empty)
                        {
                            Guid newTaskId = Guid.NewGuid();
                            CreateTask(newTaskId, command, createdTaskTypeId, assignedUserId, true);
                        }
                    }
                    else
                    {
                        // Trước tiên, xét theo AutoNextTask trước cái đã
                        if (autoNextTasks.Count > 0)
                        {
                            #region Tìm những AutoNextTask thỏa điều kiện

                            try
                            {
                                List<AutoNextTaskData> autoCreateNextTasks = autoNextTasks.Where(x => x.AutoAction == AutoNextAction.CreateNextTask).ToList();
                                foreach (AutoNextTaskData autoNextTask in autoCreateNextTasks)
                                {
                                    bool isMatchedAutoNextTask = IsMatchedConditionAutoNextTask(command, _taskTypeList, autoNextTask);
                                    if (isMatchedAutoNextTask)
                                    {
                                        matchedAutoNextTasks.Add(autoNextTask);
                                    }
                                }
                            }
                            catch (Exception ex)
                            {
                                AutoNextTaskErrorLogEntity autoNextTaskErrorLogEntity = new AutoNextTaskErrorLogEntity();
                                autoNextTaskErrorLogEntity.IsNew = true;
                                autoNextTaskErrorLogEntity.Id = Guid.NewGuid();
                                autoNextTaskErrorLogEntity.RequestTicketId = command.RequestTicketId;
                                autoNextTaskErrorLogEntity.TaskId = command.TaskId;
                                autoNextTaskErrorLogEntity.ErrorMessage = T["Không tạo được tác vụ tiếp theo do đã xảy ra lỗi: {0}", ex.Message];
                                command.ErrorMessage = autoNextTaskErrorLogEntity.ErrorMessage;

                                Repository.Save(autoNextTaskErrorLogEntity);
                            }

                            #endregion

                            if (matchedAutoNextTasks.Count() > 0)
                            {
                                foreach (var autoNextTask in matchedAutoNextTasks)
                                {
                                    try
                                    {
                                        TaskTypeData matchedTaskType = null;

                                        #region Lấy TaskType trong WORKFLOW

                                        Guid firstTaskTypeId = Guid.Empty; int taskTypeIndex = 0;
                                        if (autoNextTask.NextTaskId.IsNotNullOrEmpty())
                                        {
                                            matchedTaskType = _taskTypeList.Where(tt => tt.Id == autoNextTask.NextTaskId.Value).FirstOrDefault();
                                        }
                                        else
                                        {
                                            if (string.IsNullOrEmpty(autoNextTask.NextTaskFormula))
                                            {
                                                autoNextTask.NextTaskFormula = "first";
                                            }

                                            if (Guid.TryParse(autoNextTask.NextTaskFormula, out firstTaskTypeId))
                                            {
                                                matchedTaskType = _taskTypeList.Where(tt => tt.Id == firstTaskTypeId).FirstOrDefault();
                                            }
                                            else if (autoNextTask.NextTaskFormula.ToLower() == "first")
                                            {
                                                matchedTaskType = _taskTypeList.FirstOrDefault();
                                            }
                                            else if (autoNextTask.NextTaskFormula.ToLower() == "last")
                                            {
                                                matchedTaskType = _taskTypeList.LastOrDefault();
                                            }
                                            else if (int.TryParse(autoNextTask.NextTaskFormula, out taskTypeIndex))
                                            {
                                                // NextTaskFormula là STT TaskType trong Workflow nên phải -1
                                                taskTypeIndex -= 1;
                                                if (taskTypeIndex >= 0 && taskTypeIndex < _taskTypeList.Count)
                                                {
                                                    matchedTaskType = _taskTypeList[taskTypeIndex];
                                                }
                                            }
                                        }

                                        #endregion

                                        #region Lấy TaskGroup (Nếu có)

                                        Guid taskGroupId = Guid.Empty;
                                        if (autoNextTask.NextTaskId.IsNotNullOrEmpty())
                                        {
                                            var taskGroup = QueryExecutor.ExecuteOne(new GetWorkflowTaskTypeGroupByIdQuery { Id = autoNextTask.NextTaskId.Value });
                                            if (taskGroup != null)
                                            {
                                                taskGroupId = taskGroup.Id;
                                            }
                                        }

                                        #endregion

                                        if (matchedTaskType != null)
                                        {
                                            List<Guid> assignedUserIds = GetAssignedUserIds(command, autoNextTask.AssignedUserPathSelector, matchedTaskType);
                                            TriggerAutoNextTasks(command, matchedTaskType, autoNextTask, assignedUserIds, _taskTypeListWorkingOrders);
                                        }

                                        if (taskGroupId != Guid.Empty)
                                        {
                                            var taskListInTaskTypeGroup = QueryExecutor.ExecuteMany(new GetWorkflowTaskTypeByWorkflowAndTaskGroupIdQuery { WorkflowId = command.WorkflowId, TaskGroupId = taskGroupId });
                                            foreach (var task in taskListInTaskTypeGroup)
                                            {
                                                if (task.AssignedUserPathSelectorId.IsNotNullOrEmpty())
                                                {
                                                    matchedTaskType = _taskTypeList.Where(tt => tt.Id == task.TaskTypeId).FirstOrDefault();
                                                    List<Guid> assignedUserIds = GetAssignedUserIds(command, task.AssignedUserPathSelector, matchedTaskType);
                                                    TriggerAutoNextTasks(command, matchedTaskType, autoNextTask, assignedUserIds, _taskTypeListWorkingOrders);
                                                }
                                            }
                                        }
                                    }
                                    catch (Exception ex)
                                    {
                                        string errorMessage = T["Không tạo được tác vụ tiếp theo do đã xảy ra lỗi: {0}", ex.Message];
                                        command.ErrorMessage = errorMessage;

                                        AutoNextTaskErrorLogEntity autoNextTaskErrorLogEntity = new AutoNextTaskErrorLogEntity();
                                        autoNextTaskErrorLogEntity.IsNew = true;
                                        autoNextTaskErrorLogEntity.Id = Guid.NewGuid();
                                        autoNextTaskErrorLogEntity.RequestTicketId = command.RequestTicketId;
                                        autoNextTaskErrorLogEntity.TaskId = command.TaskId;
                                        autoNextTaskErrorLogEntity.AutoNextTaskId = autoNextTask.Id;
                                        autoNextTaskErrorLogEntity.ErrorMessage = errorMessage;

                                        Repository.Save(autoNextTaskErrorLogEntity);

                                        if (command.FromMailMessage != null)
                                        {
                                            List<string> errorMessages = new List<string>();
                                            errorMessages.Add(errorMessage);
                                            errorMessages.Add(ex.StackTrace);
                                            UpdateErrorReplyMessage(command.FromMailMessage, command.TaskInfo, errorMessages);
                                        }
                                    }
                                }
                            }
                        }
                        // Sau đó, nếu không có AutoNextTask và Workflow được cấu hình chạy "Tự động theo cấu hình rồi đến phòng ban chuyên xử lý"
                        // => Xét tiếp thêm "Phòng ban chuyên xử lý"
                        else if (_workflowData.WorkflowType == WorkflowType.AutoNextTaskThenDefaultOrganization)
                        {
                            Guid assignedUserId = Guid.Empty;
                            Guid createdTaskTypeId = Guid.Empty;

                            assignedUserId = GetAssignedUserIdByDefaultOrganization(command, _taskTypeList, out createdTaskTypeId);
                            if (createdTaskTypeId != Guid.Empty && assignedUserId != Guid.Empty)
                            {
                                Guid newTaskId = Guid.NewGuid();
                                CreateTask(newTaskId, command, createdTaskTypeId, assignedUserId, true);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Error(ex);
                command.ErrorMessage = T["Lỗi tạo Tác vụ Tự động: {0} {1}", ex.Message, ex.StackTrace];

                AutoNextTaskErrorLogEntity autoNextTaskErrorLogEntity = new AutoNextTaskErrorLogEntity();
                autoNextTaskErrorLogEntity.IsNew = true;
                autoNextTaskErrorLogEntity.Id = Guid.NewGuid();
                autoNextTaskErrorLogEntity.RequestTicketId = command.RequestTicketId;
                autoNextTaskErrorLogEntity.TaskId = command.TaskId;
                autoNextTaskErrorLogEntity.ErrorMessage = T["Lỗi tạo Tác vụ Tự động: {0}.<br/>{1}", ex.Message, ex.StackTrace];

                Repository.Save(autoNextTaskErrorLogEntity);
            }
        }

        private Guid GetAssignedUserIdByDefaultOrganization(CreateTasksByAutoNextTasksCommand command, List<TaskTypeData> _taskTypeList, out Guid taskTypeId)
        {
            taskTypeId = Guid.Empty;

            int currentTaskTypeIndex = -1;
            if (command.TaskTypeId.IsNotNullOrEmpty())
            {
                currentTaskTypeIndex = _taskTypeList.FindIndex(tt => tt.Id == command.TaskTypeId.Value);
            }
            if (currentTaskTypeIndex < _taskTypeList.Count - 1)
            {
                var taskType = _taskTypeList[currentTaskTypeIndex + 1];
                if (taskType != null)
                {
                    taskTypeId = taskType.Id;

                    var notDoneTasks = new List<TaskInfo>();
                    if (command.PhaseId.IsNotNullOrEmpty())
                    {
                        notDoneTasks = QueryExecutor.ExecuteMany(new GetTaskQuery { PhaseId = command.PhaseId.Value, TaskTypeId = taskType.Id }).Where(t => t.Status != Enums.TaskStatus.Done).ToList();
                    }

                    if (notDoneTasks.Count == 0)
                    {
                        var serviceType = QueryExecutor.ExecuteOne(new GetServiceTypeByIdQuery(command.ServiceTypeId));

                        Guid? defaultOrgId = null;

                        #region Lấy Assignment Org Priority

                        // Ưu tiên lấy DefaultOrgId của ServiceType
                        if (serviceType.DefaultOrganizationId.IsNotNullOrEmpty())
                        {
                            defaultOrgId = serviceType.DefaultOrganizationId.Value;
                        }
                        else
                        {
                            var defaultOrg = QueryExecutor.ExecuteMany(new GetTaskTypeAssignmentPriorityOrganizationQuery { TaskTypeId = taskType.Id }).FirstOrDefault();
                            if (defaultOrg != null)
                            {
                                defaultOrgId = defaultOrg.Id;
                            }
                        }

                        #endregion

                        if (defaultOrgId.IsNotNullOrEmpty())
                        {
                            var assignedUserIds = QueryExecutor.Execute(new GetUserAssignmentByRoutingQuery
                            {
                                AssignmentType = UserRoutingAssignmentType.Task,
                                OrganizationId = defaultOrgId.Value,
                                RequestTicketId = command.RequestTicketId,
                                TaskTypeId = taskType.Id
                            }).Many;
                            if (assignedUserIds.Any())
                            {
                                Guid assignedUserId = assignedUserIds.FirstOrDefault();
                                return assignedUserId;
                            }
                        }
                    }
                }
            }
            return Guid.Empty;
        }

        private bool IsMatchedConditionAutoNextTask(CreateTasksByAutoNextTasksCommand command, List<TaskTypeData> _taskTypeList, AutoNextTaskData autoNextTask)
        {
            // Nếu sự kiện Không đúng với Sự kiện của AutoNextAction
            // => Bỏ qua
            if (command.AutoTriggeredEvent.HasValue && command.AutoTriggeredEvent != autoNextTask.TriggeredEvent)
            {
                return false;
            }

            bool isMatched = false;

            Func<string, string, DataRow, string> Parser = (table, field, dr) =>
            {
                if (table == "dbo.Task" && field == "Status")
                {
                    TaskStatus? ts = (TaskStatus?)(dr[table + "." + field] as int?);
                    return ts.HasValue ? ts.Value.ToString() : "";
                }
                if (table == "dbo.Task" && field == "CompleteReason")
                {
                    TaskCompleteReason? ts = (TaskCompleteReason?)(dr[table + "." + field] as int?);
                    return ts.HasValue ? ts.Value.ToString() : "";
                }
                if (table == "dbo.RequestTicket" && field == "DifficultyDegree")
                {
                    Difficulty? ts = (Difficulty?)(dr[table + "." + field] as int?);
                    return ts.HasValue ? ts.Value.GetText() : "";
                }
                return dr[table + "." + field].ToString();
            };

            Guid contentReferenceObjectId = command.RequestTicketId;
            string linkEntityParamsType = "RequestTicket";
            string storedProcedureHandle = "GetDataSetFromTicketKey";
            if (command.TaskTypeId.IsNotNullOrEmpty() && command.TaskId.IsNotNullOrEmpty())
            {
                contentReferenceObjectId = command.TaskId.Value;
                linkEntityParamsType = "Task";
                storedProcedureHandle = "GetDataSetFromTaskKey";
            }

            bool dynamicfieldCondition = true;
            if (autoNextTask.TriggeredEvent.HasValue && autoNextTask.TriggeredEvent.Value == AutoTriggeredEvent.RequestTicketUpdated)
            {
                // Tại sao mặc định khi Trigger là RequestTicketUpdated thì mặc định là False?
                // => Bởi vì, nếu sự kiện RequestTicketUpdated thì phải xét thêm là DynamicFieldCondition có CHANGED hay không?
                // CHANGED có nghĩa là Value sau KHÁC Value trước.
                // Nếu không bắt CHANGED, thì hok lẽ mỗi lần lưu phiếu lại tạo 1 Task?
                dynamicfieldCondition = false;
            }

            if (autoNextTask.DynamicFieldConditionId.IsNotNullOrEmpty())
            {
                // Nếu là Sự kiện Cập nhật Phiếu
                // => Kiểm tra Giá trị của DynamicField trước và sau có thay đổi hay không?
                // Nếu có thay đổi, và giá trị mới đúng như cấu hình trên AutoNextAction
                if (autoNextTask.TriggeredEvent.HasValue && autoNextTask.TriggeredEvent.Value == AutoTriggeredEvent.RequestTicketUpdated)
                {
                    string oldDynamicFieldValue = string.Empty;
                    if (command.RequestTicketUpdatedEvent != null && command.RequestTicketUpdatedEvent.OldDynamicFieldValues != null)
                    {
                        if (command.RequestTicketUpdatedEvent.OldDynamicFieldValues.ContainsKey(autoNextTask.DynamicFieldConditionId.Value))
                        {
                            oldDynamicFieldValue = command.RequestTicketUpdatedEvent.OldDynamicFieldValues[autoNextTask.DynamicFieldConditionId.Value];
                        }
                    }

                    string newDynamicFieldValue = string.Empty;
                    if (command.RequestTicketUpdatedEvent != null && command.RequestTicketUpdatedEvent.NewDynamicFieldValues != null)
                    {
                        if (command.RequestTicketUpdatedEvent.NewDynamicFieldValues.ContainsKey(autoNextTask.DynamicFieldConditionId.Value))
                        {
                            newDynamicFieldValue = command.RequestTicketUpdatedEvent.NewDynamicFieldValues[autoNextTask.DynamicFieldConditionId.Value];
                        }
                    }

                    if (newDynamicFieldValue.IsNotNullOrEmpty() && !newDynamicFieldValue.IsEqualIgnoreCase(oldDynamicFieldValue))
                    {
                        if (newDynamicFieldValue.IsEqualIgnoreCase(autoNextTask.DynamicFieldConditionValue))
                        {
                            dynamicfieldCondition = true;
                        }
                    }

                    //Nếu như có check CRUD theo bảng định tuyến và có dynamicField có Value là phù hợp thì dynamicfieldCondition = true ko cần xét value trước sau có khác hay ko
                    if (autoNextTask.TaskAssignmentRouting)
                    {
                        if (newDynamicFieldValue.IsEqualIgnoreCase(autoNextTask.DynamicFieldConditionValue))
                        {
                            dynamicfieldCondition = true;
                        }
                    }
                }
                else
                {
                    var resultquery = (from rt in EntitySet.Get<RequestTicketEntity>()
                                       join dfv in EntitySet.Get<DynamicFieldValueEntity>() on rt.DynamicFormValueId equals dfv.DynamicFormValueId
                                       join dfd in EntitySet.Get<DynamicFieldDefinitionEntity>() on dfv.DynamicFieldId equals dfd.Id
                                       where rt.Id == command.RequestTicketId && dfd.Id == autoNextTask.DynamicFieldConditionId.Value
                                       select dfv).FirstOrDefault();

                    if (resultquery != null && resultquery.Value.IsNotNullOrEmpty() && resultquery.Value.IsEqualIgnoreCase(autoNextTask.DynamicFieldConditionValue))
                    {
                        dynamicfieldCondition = true;
                    }
                    else
                    {
                        dynamicfieldCondition = false;
                    }
                }
            }

            if (dynamicfieldCondition)
            {
                if (autoNextTask.AutoCondition.IsNotNullOrEmpty())
                {
                    string autoCondition = autoNextTask.AutoCondition;
                    if (autoNextTask.AutoCondition.IsNullOrEmpty())
                    {
                        autoCondition = autoNextTask.EventCondition;
                    }

                    Container.One<ICommandExecutor>().Execute(new ParseAutoNextTaskConditionCommand
                    {
                        ReferenceObjectId = contentReferenceObjectId,
                        LinkEntityParamsType = linkEntityParamsType,
                        StoredProcedureHandle = storedProcedureHandle,
                        ConditionTemplate = autoCondition,

                        Parser = Parser,
                        Handle = (conditionContent, dr) =>
                        {
                            conditionContent = conditionContent.Replace("{{RequestTicketId}}", command.RequestTicketId.ToString());
                            if (command.TaskId.IsNotNullOrEmpty())
                            {
                                conditionContent = conditionContent.Replace("{{TaskId}}", command.TaskId.Value.ToString());
                            }

                            bool evaluated = Container.One<IQueryExecutor>().ExecuteOne(new ExecuteEvaluateAutoNextTaskConditionQuery { ConditionContent = conditionContent });
                            if (evaluated)
                            {
                                isMatched = true;
                            }
                        }
                    });
                }
                else
                {
                    isMatched = true;
                }
            }

            // Xét tiếp Điều kiện FreeConditionalStatement
            if (isMatched)
            {
                if (autoNextTask.FreeConditionalStatement.IsNotNullOrEmpty())
                {
                    List<EvaluateComputeItem> evaluateComputeItems = new List<EvaluateComputeItem>();
                    
                    string requestTicketConditionPattern = "{{RequestTicket}}.";
                    if (autoNextTask.FreeConditionalStatement.Contains(requestTicketConditionPattern, StringComparison.OrdinalIgnoreCase))
                    {
                        // 1. Xét các Điều kiện trên các Property của RequestTicket
                        Type requestTicketDataType = typeof(RequestTicketData);
                        var requestTicketProperties = requestTicketDataType.GetProperties();
                        foreach (var requestTicketProperty in requestTicketProperties)
                        {
                            string ticketPropertyConditionPattern = "{{RequestTicket}}.{{" + requestTicketProperty.Name + "}}";
                            if (autoNextTask.FreeConditionalStatement.Contains(ticketPropertyConditionPattern, StringComparison.OrdinalIgnoreCase))
                            {
                                if (!evaluateComputeItems.Exists(eci => eci.Name.IsEqualIgnoreCase(ticketPropertyConditionPattern)))
                                {
                                    object propertyValue = propertyValue = requestTicketProperty.GetValue(command.RequestTicketData, null);
                                    evaluateComputeItems.Add(new EvaluateComputeItem
                                    {
                                        Name = ticketPropertyConditionPattern,
                                        Type = requestTicketProperty.PropertyType.BaseType(),
                                        Value = propertyValue
                                    });
                                }
                            }
                        }

                        // 2. Xét các Điều kiện trên các Dynamic Field của RequestTicket
                        if (command.RequestTicketData.DynamicFormValueId.IsNotNullOrEmpty())
                        {
                            var dynamicFieldValueList = QueryExecutor.Execute(new GetDynamicFieldValueInfoQuery { DynamicFormValueId = command.RequestTicketData.DynamicFormValueId.Value }).Many.ToList();
                            foreach (var dynamicFieldValue in dynamicFieldValueList)
                            {
                                string ticketFieldConditionPattern = "{{RequestTicket}}.{{" + dynamicFieldValue.Name + "}}";
                                if (autoNextTask.FreeConditionalStatement.Contains(ticketFieldConditionPattern, StringComparison.OrdinalIgnoreCase))
                                {
                                    if (!evaluateComputeItems.Exists(eci => eci.Name.IsEqualIgnoreCase(ticketFieldConditionPattern)))
                                    {
                                        Type dynamicFieldType = dynamicFieldValue.DataType.ToType().BaseType();
                                        object propertyValue = null;
                                        if (dynamicFieldValue.Value.IsNotNullOrEmpty())
                                        {
                                            try
                                            {
                                                propertyValue = dynamicFieldType.GetTypeConverter(string.IsNullOrEmpty(dynamicFieldValue.Value)).ConvertFromString(null, CultureInfo.CurrentCulture, dynamicFieldValue.Value);
                                            }
                                            catch
                                            {
                                                propertyValue = null;
                                            }
                                        }
                                        evaluateComputeItems.Add(new EvaluateComputeItem
                                        {
                                            Name = ticketFieldConditionPattern,
                                            Type = dynamicFieldType,
                                            Value = propertyValue
                                        });
                                    }
                                }
                            }
                        }
                    }

                    if (_taskTypeList != null && _taskTypeList.Count > 0)
                    {
                        Type taskInfoType = typeof(TaskInfo);
                        var taskInfoProperties = taskInfoType.GetProperties();

                        foreach (var taskType in _taskTypeList)
                        {
                            string taskTypeConditionPattern = "{{" + taskType.TaskType + "}}.";
                            if (autoNextTask.FreeConditionalStatement.Contains(taskTypeConditionPattern, StringComparison.OrdinalIgnoreCase))
                            {
                                TaskInfo taskInfo = QueryExecutor.ExecuteOne(new GetLatestTasksByTaskTypeQuery { RequestTicketId = command.RequestTicketId, TaskTypeId = taskType.Id });

                                // 3. Xét các Điều kiện trên các Property của Task
                                foreach (var taskInfoProperty in taskInfoProperties)
                                {
                                    string taskTypePropertyConditionPattern = "{{" + taskType.TaskType + "}}.{{" + taskInfoProperty.Name + "}}";
                                    if (autoNextTask.FreeConditionalStatement.Contains(taskTypePropertyConditionPattern, StringComparison.OrdinalIgnoreCase))
                                    {
                                        if (!evaluateComputeItems.Exists(eci => eci.Name.IsEqualIgnoreCase(taskTypePropertyConditionPattern)))
                                        {
                                            object propertyValue = null;
                                            if (taskInfo != null)
                                            {
                                                propertyValue = taskInfoProperty.GetValue(taskInfo, null);
                                            }
                                            evaluateComputeItems.Add(new EvaluateComputeItem
                                            {
                                                Name = taskTypePropertyConditionPattern,
                                                Type = taskInfoProperty.PropertyType.BaseType(),
                                                Value = propertyValue
                                            });
                                        }
                                    }
                                }

                                // 4. Xét tiếp các Điều kiện trên các Dynamic Field của Task
                                if (taskInfo != null)
                                {
                                    if (taskInfo.DynamicFormValueId.IsNotNullOrEmpty())
                                    {
                                        var dynamicFieldValueList = QueryExecutor.Execute(new GetDynamicFieldValueInfoQuery { DynamicFormValueId = taskInfo.DynamicFormValueId.Value }).Many.ToList();
                                        foreach (var dynamicFieldValue in dynamicFieldValueList)
                                        {
                                            string taskFieldConditionPattern = "{{" + taskType.TaskType + "}}.{{" + dynamicFieldValue.Name + "}}";
                                            if (autoNextTask.FreeConditionalStatement.Contains(taskFieldConditionPattern, StringComparison.OrdinalIgnoreCase))
                                            {
                                                if (!evaluateComputeItems.Exists(eci => eci.Name.IsEqualIgnoreCase(taskFieldConditionPattern)))
                                                {
                                                    Type dynamicFieldType = dynamicFieldValue.DataType.ToType().BaseType();
                                                    object propertyValue = null;
                                                    if (dynamicFieldValue.Value.IsNotNullOrEmpty())
                                                    {
                                                        try
                                                        {
                                                            propertyValue = dynamicFieldType.GetTypeConverter(string.IsNullOrEmpty(dynamicFieldValue.Value)).ConvertFromString(null, CultureInfo.CurrentCulture, dynamicFieldValue.Value);
                                                        }
                                                        catch
                                                        {
                                                            propertyValue = null;
                                                        }
                                                    }
                                                    evaluateComputeItems.Add(new EvaluateComputeItem
                                                    {
                                                        Name = taskFieldConditionPattern,
                                                        Type = dynamicFieldType,
                                                        Value = propertyValue
                                                    });
                                                }
                                            }
                                        }
                                    }
                                }
                                else
                                {
                                    if (taskType.DynamicFormId.IsNotNullOrEmpty())
                                    {
                                        var dynamicFieldList = QueryExecutor.ExecuteMany(new GetDynamicFieldByFormIdQuery { FormId = taskType.DynamicFormId.Value });
                                        foreach (var dynamicField in dynamicFieldList)
                                        {
                                            string taskFieldConditionPattern = "{{" + taskType.TaskType + "}}.{{" + dynamicField.Name + "}}";
                                            if (autoNextTask.FreeConditionalStatement.Contains(taskFieldConditionPattern, StringComparison.OrdinalIgnoreCase))
                                            {
                                                if (!evaluateComputeItems.Exists(eci => eci.Name.IsEqualIgnoreCase(taskFieldConditionPattern)))
                                                {
                                                    Type dynamicFieldType = dynamicField.DataType.ToType().BaseType();
                                                    evaluateComputeItems.Add(new EvaluateComputeItem
                                                    {
                                                        Name = taskFieldConditionPattern,
                                                        Type = dynamicFieldType,
                                                        Value = null
                                                    });
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }

                    isMatched = autoNextTask.FreeConditionalStatement.EvaluateBooleanExpressionByDataTableCompute(evaluateComputeItems);
                }
            }

            return isMatched;
        }

        private List<Guid> GetAssignedUserIds(CreateTasksByAutoNextTasksCommand command, string inputUserPathSelector, TaskTypeData matchedTaskType)
        {
            List<Guid> assignedUserIds = new List<Guid>();

            Dictionary<string, string> staticParams = new Dictionary<string, string>();
            staticParams.Add("{{TicketCreator}}", command.RequestTicketCreatorId.IsNullOrEmpty() ? string.Empty : command.RequestTicketCreatorId.Value.ToString());
            staticParams.Add("{{TicketOwner}}", command.RequestTicketOwnerId.IsNullOrEmpty() ? string.Empty : command.RequestTicketOwnerId.Value.ToString());
            staticParams.Add("{{TaskCreator}}", command.TaskCreatorId.IsNullOrEmpty() ? string.Empty : command.TaskCreatorId.Value.ToString());
            staticParams.Add("{{TaskOwner}}", command.TaskOwnerId.IsNullOrEmpty() ? string.Empty : command.TaskOwnerId.Value.ToString());

            // Kiểm tra xem có phải đang dùng "Bảng định tuyến trên phiếu" hay không?
            if (inputUserPathSelector.IsEqualIgnoreCase("{{TicketTaskAssignmentRouting}}"))
            {
                if (TicketAssignmentRoutingDynamicDefinedTableSchemaId != Guid.Empty && TicketAssignmentRoutingTaskTypeColumnId != Guid.Empty && TicketAssignmentRoutingAssignedUserColumnId != Guid.Empty)
                {
                    if (command.RequestTicketData != null && command.RequestTicketData.DynamicFormValueId.IsNotNullOrEmpty())
                    {
                        var ticketDynamicFieldValueList = QueryExecutor.Execute(new GetDynamicFieldValueInfoQuery { DynamicFormValueId = command.RequestTicketData.DynamicFormValueId.Value }).Many.ToList();
                        foreach (var ticketDynamicFieldValue in ticketDynamicFieldValueList)
                        {
                            if (ticketDynamicFieldValue.DynamicDefinedTableSchemaId.IsNotNullOrEmpty() && ticketDynamicFieldValue.DynamicDefinedTableSchemaId.Value == TicketAssignmentRoutingDynamicDefinedTableSchemaId)
                            {
                                var dataType = ticketDynamicFieldValue.DataType.ToType();

                                var entityInstance = (IUserDefinedTableGridData)Activator.CreateInstance(dataType);
                                if (entityInstance != null)
                                {
                                    entityInstance.Id = ticketDynamicFieldValue.Id.Value;
                                    entityInstance.DynamicDefinedTableSchemaId = ticketDynamicFieldValue.DynamicDefinedTableSchemaId.Value;
                                    entityInstance.Load();

                                    List<DynamicDefinedTableCellValueBase> listItems = entityInstance.GetBaseListItems();
                                    var rowNumberList = (from d in listItems
                                                         group d by new { d.RowNumber } into tempData
                                                         select new
                                                         {
                                                             RowNumber = tempData.Key.RowNumber
                                                         }).OrderBy(x => x.RowNumber).ToList();
                                    
                                    foreach (var rowNumberItem in rowNumberList)
                                    {
                                        var taskTypeCellValue = (from x in listItems
                                                                 where x.RowNumber == rowNumberItem.RowNumber
                                                                 && x.DynamicDefinedTableColumnId == TicketAssignmentRoutingTaskTypeColumnId
                                                                 select x).FirstOrDefault();
                                        
                                        if (taskTypeCellValue != null && matchedTaskType.Id.ToString().IsEqualIgnoreCase(taskTypeCellValue.Value))
                                        {
                                            var assignedUserCellValue = (from x in listItems
                                                                         where x.RowNumber == rowNumberItem.RowNumber
                                                                         && x.DynamicDefinedTableColumnId == TicketAssignmentRoutingAssignedUserColumnId
                                                                         select x).FirstOrDefault();
                                            
                                            Guid assignedUserId = Guid.Empty;
                                            if (Guid.TryParse(assignedUserCellValue.Value, out assignedUserId))
                                            {
                                                assignedUserIds.Add(assignedUserId);
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            else if (inputUserPathSelector.IsNotNullOrEmpty() && staticParams.ContainsKey(inputUserPathSelector))
            {
                var assignedUserIdString = staticParams[inputUserPathSelector];
                if (!string.IsNullOrEmpty(assignedUserIdString))
                {
                    var assignedUserId = new Guid(assignedUserIdString);
                    assignedUserIds.Add(assignedUserId);
                }
            }
            else
            {
                string originOrgPathSelector = string.Empty;
                string userPathSelector = string.Empty;

                if (inputUserPathSelector.IsNotNullOrEmpty())
                {
                    string assignedUserPathSelector = inputUserPathSelector;
                    assignedUserPathSelector = assignedUserPathSelector.Replace("{{RequestTicketId}}", command.RequestTicketId.ToString());
                    if (command.TaskId.IsNotNullOrEmpty())
                    {
                        assignedUserPathSelector = assignedUserPathSelector.Replace("{{TaskId}}", command.TaskId.Value.ToString());
                    }

                    string[] userSelectorPaths = assignedUserPathSelector.Split('|');
                    if (userSelectorPaths.Count() > 1)
                    {
                        originOrgPathSelector = userSelectorPaths[0];
                        userPathSelector = userSelectorPaths[1];
                    }
                    else
                    {
                        userPathSelector = userSelectorPaths[0];
                    }
                }

                Guid originOrgId = Guid.Empty;

                if (originOrgPathSelector.IsNullOrEmpty())
                {
                    // PathSelector Empty = Lấy "Phòng ban chuyên xử lý" của TASKTYPE
                    var serviceType = QueryExecutor.ExecuteOne(new GetServiceTypeByIdQuery(command.ServiceTypeId));
                    if (serviceType.DefaultOrganizationId.IsNotNullOrEmpty())
                    {
                        originOrgId = serviceType.DefaultOrganizationId.Value;
                    }
                    else
                    {
                        var defaultOrg = QueryExecutor.ExecuteMany(new GetTaskTypeAssignmentPriorityOrganizationQuery { TaskTypeId = matchedTaskType.Id }).FirstOrDefault();
                        if (defaultOrg != null)
                        {
                            originOrgId = defaultOrg.Id;
                        }
                    }
                }
                else
                {
                    // Nếu Parse content trả về GUID => Static OriginOrgId
                    if (!Guid.TryParse(originOrgPathSelector, out originOrgId))
                    {
                        var originOrgIds = QueryExecutor.ExecuteMany(new GetOriginOrgIdQuery { RequestTicketId = command.RequestTicketId, TaskId = command.TaskId, UserPathSelector = originOrgPathSelector, StaticParams = staticParams });
                        if (originOrgIds != null && originOrgIds.Count() > 0)
                        {
                            originOrgId = originOrgIds.FirstOrDefault();
                        }
                    }
                }

                if (originOrgId != Guid.Empty)
                {
                    // PathSelector Empty = Lấy theo bảng phân bổ "UserTaskAssignmentRouting"
                    if (userPathSelector.IsNullOrEmpty())
                    {
                        assignedUserIds = QueryExecutor.Execute(new GetUserAssignmentByRoutingQuery
                        {
                            AssignmentType = UserRoutingAssignmentType.Task,
                            OrganizationId = originOrgId,
                            RequestTicketId = command.RequestTicketId,
                            TaskTypeId = matchedTaskType.Id
                        }).Many.ToList();
                    }
                    else
                    {
                        assignedUserIds = QueryExecutor.ExecuteMany(new GetIdsFromSelectorPathQuery { OriginId = originOrgId, Path = userPathSelector }).ToList();
                    }
                }
                else
                {
                    assignedUserIds = QueryExecutor.ExecuteMany(new GetIdsFromSelectorPathQuery { OriginId = originOrgId, Path = userPathSelector }).ToList();
                }
            }

            List<Guid> approvedUserIds = new List<Guid>();
            if (assignedUserIds.Count > 0)
            {
                approvedUserIds = (from m in EntitySet.Get<MembershipEntity>()
                                   join u in EntitySet.Get<UserEntity>() on m.Id equals u.Id
                                   where assignedUserIds.Contains(u.Id)
                                   && m.IsApproved
                                   select u.Id).ToList();
            }

            return approvedUserIds;
        }

        private void TriggerAutoNextTasks(CreateTasksByAutoNextTasksCommand command, TaskTypeData matchedTaskType, AutoNextTaskData autoNextTask, List<Guid> assignedUserIds, Dictionary<Guid, int> _taskTypeListWorkingOrders)
        {
            if (autoNextTask.TaskAssignmentRouting)
            {
                var delEntitys = new List<IEntity>();
                //Xóa hết các tác vụ được tạo tự động trên bảng định tuyến
                var getTasks = QueryExecutor.ExecuteMany(new GetTaskByRequestTicketIdQuery(command.RequestTicketId)).ToList();
                foreach(var item in getTasks.Where(x => x.TaskTypeId == autoNextTask.NextTaskId && x.CreatedBy == AutomaticTaskConstants.AutoTaskCreatorUserId))
                {
                    var taskEntity = EntitySet.Get<TaskEntity>(item.Id);
                    delEntitys.Add(taskEntity);
                }
                Repository.Delete(delEntitys);
            }

            if (assignedUserIds.Count > 0)
            {
                var currentStepTasks = new List<TaskInfo>();
                if (command.PhaseId.IsNotNullOrEmpty())
                {
                    currentStepTasks = QueryExecutor.ExecuteMany(new GetTaskQuery { PhaseId = command.PhaseId.Value, TaskTypeId = matchedTaskType.Id }).ToList();
                }

                var workflowTaskType = QueryExecutor.ExecuteOne(new GetWorkflowTaskTypeByWorkflowAndTaskTypeIdQuery { WorkflowId = command.WorkflowId, TaskTypeId = matchedTaskType.Id });
                bool isForwarded = true;
                if (workflowTaskType.AssignedConditionCount.HasValue && workflowTaskType.AssignedConditionCount.Value > 0)
                {
                    isForwarded = false;
                }

                // Nếu cấu hình cho tạo nhiều Task
                // => Cứ tạo thoải mái cho tất cả UserId có được
                if (autoNextTask.MultiTaskTriggered)
                {
                    foreach (var assignedUserId in assignedUserIds)
                    {
                        if (assignedUserId != Guid.Empty)
                        {
                            Guid newTaskId = Guid.NewGuid();
                            CreateTask(newTaskId, command, matchedTaskType.Id, assignedUserId, isForwarded);
                            if (!isForwarded)
                            {
                                Webaby.Container.One<ICommandExecutor>().Execute(new SetAssignedConditionCountCommand { TaskId = newTaskId, AssignedConditionCount = 1 });
                            }
                        }
                    }
                }
                // Nếu cấu hình KHÔNG cho tạo nhiều Task
                else
                {
                    // Kiểm tra xem còn Task nào chưa Done không?
                    // => Chưa có thì mới được tạo
                    int notDoneTaskCount = currentStepTasks.Where(t => t.Status != Enums.TaskStatus.Done).Count();
                    if (notDoneTaskCount == 0)
                    {
                        var assignedUserId = assignedUserIds.FirstOrDefault();
                        if (assignedUserId != Guid.Empty)
                        {
                            Guid newTaskId = Guid.NewGuid();
                            CreateTask(newTaskId, command, matchedTaskType.Id, assignedUserId, isForwarded);
                            if (!isForwarded)
                            {
                                Webaby.Container.One<ICommandExecutor>().Execute(new SetAssignedConditionCountCommand { TaskId = newTaskId, AssignedConditionCount = 1 });
                            }
                        }
                    }
                    else
                    {
                        // Kiểm tra luồng AssignedConditionCount
                        if (workflowTaskType.AssignedConditionCount.HasValue && workflowTaskType.AssignedConditionCount.Value > 0)
                        {
                            var notAssignedTask = currentStepTasks.Where(t => t.Status == TaskStatus.Todo).FirstOrDefault();
                            // Trường hợp đã tạo Task trước đó, thì tăng AssignedConditionCount lên
                            if (notAssignedTask != null)
                            {
                                int assignedConditionCount = 1;
                                if (notAssignedTask.AssignedConditionCount.HasValue)
                                {
                                    assignedConditionCount = notAssignedTask.AssignedConditionCount.Value;
                                }
                                assignedConditionCount++;

                                if (assignedConditionCount >= workflowTaskType.AssignedConditionCount.Value)
                                {
                                    // Chuyển trạng thái Task => "Chuyển xử lý"
                                    var taskFb = Webaby.Container.One<IQueryExecutor>().Execute(new GetTaskFeedbackQuery { TaskId = notAssignedTask.Id }).One;
                                    TaskFeedbackArguments taskFeedbackArguments = new TaskFeedbackArguments
                                    {
                                        Id = taskFb.Id,
                                        TaskId = notAssignedTask.Id,
                                        TaskTypeId = matchedTaskType.Id,
                                        PreviousAssignedTo = notAssignedTask.AssignedTo,
                                        PreviousStatus = notAssignedTask.Status,
                                        Status = TaskStatus.Forwarded,
                                        CompletedType = matchedTaskType.CompletedType,
                                        OwnedByOrganizationId = notAssignedTask.OwnedByOrganizationId,
                                        AssignedTo = notAssignedTask.AssignedTo,
                                        TaskBusinessResultId = notAssignedTask.TaskBusinessResultId,
                                        Descriptions = taskFb.Descriptions,
                                        UpdateFromApi = true
                                    };
                                    NameValueCollection taskFormValues = new NameValueCollection();

                                    Webaby.Container.One<ITaskCommand>().UpdateTaskFeedback(taskFeedbackArguments, taskFormValues, null);
                                }
                                else
                                {
                                    // Tăng AssignedConditionCount lên
                                    Webaby.Container.One<ICommandExecutor>().Execute(new SetAssignedConditionCountCommand { TaskId = notAssignedTask.Id, AssignedConditionCount = assignedConditionCount });
                                }
                            }

                            // Nếu chưa tạo task trước đó, thì tạo Task mới với Status là "New" (Todo)
                            // Nhưng cũng phải kiểm tra xem có Task nào đã tạo và đã đổi Status hay chưa?
                            var notDoneTask = currentStepTasks.Where(t => t.Status != TaskStatus.Done).FirstOrDefault();
                            if (notDoneTask == null)
                            {
                                var assignedUserId = assignedUserIds.FirstOrDefault();
                                Guid newTaskId = Guid.NewGuid();
                                CreateTask(newTaskId, command, matchedTaskType.Id, assignedUserId, false);
                                Webaby.Container.One<ICommandExecutor>().Execute(new SetAssignedConditionCountCommand { TaskId = newTaskId, AssignedConditionCount = 1 });
                            }
                        }
                    }
                }
            }
            else
            {
                string errorMessage = T["Không tìm được người được phân công \"{0}\" để nhận tác vụ \"{1}. {2}\".", autoNextTask.AssignedUserPathSelectorName, _taskTypeListWorkingOrders[matchedTaskType.Id], matchedTaskType.TaskType];
                command.ErrorMessage = errorMessage;

                AutoNextTaskErrorLogEntity autoNextTaskErrorLogEntity = new AutoNextTaskErrorLogEntity();
                autoNextTaskErrorLogEntity.IsNew = true;
                autoNextTaskErrorLogEntity.Id = Guid.NewGuid();
                autoNextTaskErrorLogEntity.RequestTicketId = command.RequestTicketId;
                autoNextTaskErrorLogEntity.TaskId = command.TaskId;
                autoNextTaskErrorLogEntity.AutoNextTaskId = autoNextTask.Id;
                autoNextTaskErrorLogEntity.ErrorMessage = errorMessage;

                Repository.Save(autoNextTaskErrorLogEntity);

                if (command.FromMailMessage != null)
                {
                    List<string> errorMessages = new List<string>();
                    errorMessages.Add(errorMessage);
                    UpdateErrorReplyMessage(command.FromMailMessage, command.TaskInfo, errorMessages);
                }
            }
        }

        public void UpdateErrorReplyMessage(MailMessage mailMessage, TaskInfo taskInfo, List<string> errorMessages)
        {
            CreateEditMailCommand createEditMailCommand = new CreateEditMailCommand();
            createEditMailCommand.Id = Guid.NewGuid();
            createEditMailCommand.Status = MailStatus.Draft;
            createEditMailCommand.SendDateTime = DateTime.Now;
            createEditMailCommand.ReferenceObjectId = taskInfo.Id;
            createEditMailCommand.ReferenceObjectType = "Task";

            var configString = SendByEmail;
            var configs = string.IsNullOrEmpty(configString) ? new List<SendByEmail>() : JsonConvert.DeserializeObject<List<SendByEmail>>(configString);
            var mailType = configs.Where(x => x.Type == "Email nội bộ");
            if (mailType.Any() && !string.IsNullOrWhiteSpace(mailType.First().From))
            {
                createEditMailCommand.From = mailType.First().From;
            }
            else
            {
                createEditMailCommand.From = mailMessage.To.FirstOrDefault();
            }

            createEditMailCommand.To = mailMessage.From;
            createEditMailCommand.Title = mailMessage.Subject;

            var taskType = QueryExecutor.ExecuteOne(new GetTaskTypeByIdQuery { Id = taskInfo.TaskTypeId });
            if (taskType != null)
            {
                string mailBody = string.Format("<h4>{0}<br/>{1}</h4>", T["Tác vụ \"{0}\" đã được cập nhật thành công.", taskType.TaskType], T["Tuy nhiên, hệ thống đã không thể tự động tạo Tác vụ tiếp theo vì:"]);
                mailBody += "<ul>";
                foreach (var errorMessage in errorMessages)
                {
                    mailBody += string.Format("<li style='color: #f4516c'>{0}</li>", errorMessage.Replace("\r\n", "<br/>"));
                }
                mailBody += "</ul><br/><br/>";
                mailBody += "=============================================================================================";
                mailBody += "<br/><br/>" + mailMessage.HtmlBody;

                createEditMailCommand.Body = mailBody;

                CommandExecutor.Execute(createEditMailCommand);
            }
        }

        private void CreateTask(Guid newTaskId, CreateTasksByAutoNextTasksCommand command, Guid taskTypeId, Guid assignedUserId, bool isForwarded)
        {
            Guid? parentTaskId = null; Guid? triggerTaskId = null;
            if (command.TaskInfo != null)
            {
                parentTaskId = command.TaskInfo.ParentTaskId;
                triggerTaskId = command.TaskInfo.Id;
            }

            var createTaskArguments = new CreateTaskArguments
            {
                TaskId = newTaskId,
                SMS = false,
                SendEmail = true,
                TicketId = command.RequestTicketData.Id,
                TaskType = taskTypeId,
                Description = null,
                AssignTo = assignedUserId,

                ParentTaskId = parentTaskId,
                TriggerTaskId = triggerTaskId,

                CreatedBy = AutomaticTaskConstants.AutoTaskCreatorUserId,
                PlannedDate = command.RequestTicketData.PlannedDate
            };
            //using (var trans = LocalTransactionManager.CreateLocalTransaction())
            {
                var createTaskResult = TaskCommand.CreateTask(createTaskArguments, isForwarded);
                if (createTaskResult.IsSuccess)
                {
                    TaskCommand.HandleHttpTask(createTaskArguments.TaskId.Value);
                }
                else
                {
                    command.ErrorMessage = createTaskResult.ErrorMessage;

                    AutoNextTaskErrorLogEntity autoNextTaskErrorLogEntity = new AutoNextTaskErrorLogEntity();
                    autoNextTaskErrorLogEntity.IsNew = true;
                    autoNextTaskErrorLogEntity.Id = Guid.NewGuid();
                    autoNextTaskErrorLogEntity.RequestTicketId = command.RequestTicketId;
                    autoNextTaskErrorLogEntity.TaskId = command.TaskId;
                    autoNextTaskErrorLogEntity.ErrorMessage = createTaskResult.ErrorMessage;

                    Repository.Save(autoNextTaskErrorLogEntity);
                }
                //trans.Commit();
            }
        }
    }
}
﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TinyCRM.DigitalChannel.Queries;
using TinyCRM.DigitalChannel;
using Webaby;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;

namespace TinyCRM.DigitalChannelMessageTemplate.Queries
{
    public class GetDigitalChannelMessageTemplateByIdQuery : QueryBase<DigitalChannelMessageTemplateData>
    {
        public Guid Id { get; set; }
    }

    internal class GetDigitalChannelMessageTemplateByIdQueryHandler : QueryHandlerBase<GetDigitalChannelMessageTemplateByIdQuery, DigitalChannelMessageTemplateData>
    {
        public GetDigitalChannelMessageTemplateByIdQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<DigitalChannelMessageTemplateData>> ExecuteAsync(GetDigitalChannelMessageTemplateByIdQuery query)
        {
            var entity = await EntitySet.GetAsync<DigitalChannelMessageTemplateEntity>();
            var filtered = entity.Where(x => x.Id == query.Id);
            return QueryResult.Create(filtered, x => Mapper.Map<DigitalChannelMessageTemplateData>(x));
        }
    }
}
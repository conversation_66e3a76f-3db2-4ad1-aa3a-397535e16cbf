﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;
using Webaby.Localization;
using AutoMapper;

namespace TinyCRM.Callback.Queries
{
    public class GetCallbackSettingByIdQuery : QueryBase<CallbackSettingsData>
    {
        public Guid Id { get; set; }
    }

    internal class GetCallbackSettingByIdQueryHandler : QueryHandlerBase<GetCallbackSettingByIdQuery, CallbackSettingsData>
    {
        public GetCallbackSettingByIdQueryHandler(IServiceProvider serviceProvider)
            : base(serviceProvider) { }

        public override async Task<QueryResult<CallbackSettingsData>> ExecuteAsync(GetCallbackSettingByIdQuery query)
        {
            var callbackSetting = await EntitySet.GetAsync<CallbackSettingsEntity>(query.Id);
            if (callbackSetting == null) throw new InvalidOperationException(T[$"Không tìm  thấy Callback Setting có id '{query.Id}'"]);
            return new QueryResult<CallbackSettingsData>(Mapper.Map<CallbackSettingsData>(callbackSetting));
        }
    }
}
﻿using AutoMapper;
using System;
using Webaby;

namespace TinyCRM.ContentTemplate.Command
{
    public class CreateEditAlternativeNotiChannelContentTemplateCommand : CommandBase
    {
        public Guid Id { get; set; }

        public Guid ContentTemplateId { get; set; }

         public Guid? TaskTypeId { get; set; }

        public Guid? ServiceTypeId { get; set; }

        public Guid? Level1Id { get; set; }

        public Guid? Level2Id { get; set; }

        public Guid? Level3Id { get; set; }

        public Guid? Level4Id { get; set; }

        public Guid NotificationChannelSettingId { get; set; }

        public int Order { get; set; }
    }

    internal class CreateEditAlternativeNotiChannelContentTemplateCommandHandler : CommandHandlerBase<CreateEditAlternativeNotiChannelContentTemplateCommand>
    {
        public CreateEditAlternativeNotiChannelContentTemplateCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task ExecuteAsync(CreateEditAlternativeNotiChannelContentTemplateCommand command)
        {
            var entity = await EntitySet.GetAsync<AlternativeNotiChannelContentTemplateEntity>(command.Id);
            if (entity == null)
            {
                entity = new AlternativeNotiChannelContentTemplateEntity();
            }
            Mapper.Map(command, entity);
            await Repository.SaveAsync(entity);
        }
    }
}

﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;

namespace TinyCRM.SMSLog.Queries
{
    public class GetSMSLogListQuery : QueryBase<SMSLogListItem>
    {
        public string PhoneNumber { get; set; }
        public Guid? MaKH { get; set; }
    }

    internal class GetSMSLogListQueryHandler : QueryHandlerBase<GetSMSLogListQuery, SMSLogListItem>
    {
        public GetSMSLogListQueryHandler(IEntitySet entitySet, IRepository repository, IText text, IMapper mapper)
            : base(entitySet, repository, text, mapper) { }

        public override async Task<QueryResult<SMSLogListItem>> ExecuteAsync(GetSMSLogListQuery query)
        {
            var cmd = EntitySet.CreateDbCommand();
            DbParameterHelper.AddNullableGuid(cmd, "@MaKH", query.MaKH);
            DbParameterHelper.AddNullableString(cmd, "@PhoneNumber", string.IsNullOrEmpty(query.PhoneNumber) ? string.Empty : query.PhoneNumber.Trim());
            cmd.CommandText = "GetTopSMSByPhone";
            cmd.CommandType = CommandType.StoredProcedure;

            var mainQuery = await EntitySet.ExecuteReadCommandAsync<SMSLogListItem>(cmd);
            return new QueryResult<SMSLogListItem>(mainQuery);
        }
    }
}
﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;

namespace TinyCRM.Campaign.Queries
{
    public class GetCampaignAssignmentByIdQuery : QueryBase<CampaignAssignmentData>
    {
        public Guid Id { get; set; }
        public Boolean IncludeDeleted { get; set; }
        public GetCampaignAssignmentByIdQuery(Guid id, Boolean includeDeleted = false)
        {
            Id = id;
            IncludeDeleted = includeDeleted;
        }
    }

    internal class GetCampaignAssignmentByIdQueryHandler : QueryHandlerBase<GetCampaignAssignmentByIdQuery, CampaignAssignmentData>
    {
        public GetCampaignAssignmentByIdQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<CampaignAssignmentData>> ExecuteAsync(GetCampaignAssignmentByIdQuery query)
        {
            var campaignAssignmentQuery = await EntitySet.GetAsync<CampaignAssignmentEntity>(query.IncludeDeleted);
            var result = campaignAssignmentQuery.FirstOrDefault(x => x.Id == query.Id);
            return new QueryResult<CampaignAssignmentData>(Mapper.Map<CampaignAssignmentData>(result));
        }
    }
}

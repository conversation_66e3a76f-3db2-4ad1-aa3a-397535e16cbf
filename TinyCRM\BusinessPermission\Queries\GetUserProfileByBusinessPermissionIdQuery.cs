﻿using AutoMapper;
using System;
using System.Linq;
using Webaby;
using Webaby.Core.UserAccount;
using Webaby.Core.UserAccount.Queries;
using Webaby.Data;
using Webaby.Localization;
using Webaby.Security;

namespace TinyCRM.BusinessPermission
{
    public class GetUserProfileByBusinessPermissionIdQuery : QueryBase<ApplicationUser>
    {
        public Guid BusinessPermissionId { get; set; }
    }

    internal class GetUserProfileByBusinessPermissionIdQueryHandler : QueryHandlerBase<GetUserProfileByBusinessPermissionIdQuery, ApplicationUser>
    {
        public GetUserProfileByBusinessPermissionIdQueryHandler(IServiceProvider serviceProvider)
            : base(serviceProvider) { }
        public override async Task<QueryResult<ApplicationUser>> ExecuteAsync(GetUserProfileByBusinessPermissionIdQuery query)
        {
            var userProfileEntities = from up in EntitySet.Get<AspNetUserEntity>()
                                      join uir in EntitySet.Get<AspNetUserRoleEntity>() on up.Id equals uir.UserId
                                      join rb in EntitySet.Get<RoleBusinessPermissionEntity>() on uir.RoleId equals rb.RoleId
                                      where rb.BusinessPermissionId == query.BusinessPermissionId && up.IsApproved
                                      select up;
            var mapped = userProfileEntities.Select(x => Mapper.Map<ApplicationUser>(x));
            return QueryResult.Create(mapped);
        }
    }
}
﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;

namespace TinyCRM.Campaign.Queries
{
    public class GetCampaignWorkByIdQuery : QueryBase<CampaignWorkData>
    {
        public Guid Id { get; set; }

        public GetCampaignWorkByIdQuery(Guid id)
        {
            Id = id;
        }
    }

    internal class GetCampaignWorkByIdQueryHandler : QueryHandlerBase<GetCampaignWorkByIdQuery, CampaignWorkData>
    {
        public GetCampaignWorkByIdQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<CampaignWorkData>> ExecuteAsync(GetCampaignWorkByIdQuery query)
        {
            var campaignWorkQuery = await EntitySet.GetAsync<CampaignWorkEntity>();
            var result = campaignWorkQuery.FirstOrDefault(x => x.Id == query.Id);
            return new QueryResult<CampaignWorkData>(Mapper.Map<CampaignWorkData>(result));
        }
    }
}

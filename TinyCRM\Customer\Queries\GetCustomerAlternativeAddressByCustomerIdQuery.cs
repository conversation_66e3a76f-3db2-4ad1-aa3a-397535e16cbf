﻿using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;
using TinyCRM.Geolocation;
using Webaby;

namespace TinyCRM.Customer.Queries
{
    public class GetCustomerAlternativeAddressByCustomerIdQuery : QueryBase<CustomerAlternativeAddressData>
    {
        public GetCustomerAlternativeAddressByCustomerIdQuery(Guid id)
        {
            Id = id;
        }

        public Guid Id { get; private set; }
    }

    internal class GetCustomerAlternativeAddressByCustomerIdQueryHandler :
        QueryHandlerBase<GetCustomerAlternativeAddressByCustomerIdQuery, CustomerAlternativeAddressData>
    {
        public GetCustomerAlternativeAddressByCustomerIdQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<CustomerAlternativeAddressData>> ExecuteAsync(GetCustomerAlternativeAddressByCustomerIdQuery query)
        {
            var cusAlts = await (from cusAlt in EntitySet.Get<CustomerAlternativeAddressEntity>()
                          join ward in EntitySet.Get<GeolocationEntity>() on cusAlt.WardId equals ward.Id into wardN
                          from wardX in wardN.DefaultIfEmpty()
                          join district in EntitySet.Get<GeolocationEntity>() on cusAlt.DistrictId equals district.Id into
                              districtN
                          from districtX in districtN.DefaultIfEmpty()
                          join province in EntitySet.Get<GeolocationEntity>() on cusAlt.ProvinceId equals province.Id into
                              provinceN
                          from provinceX in provinceN.DefaultIfEmpty()
                          join classification in EntitySet.Get<ClassificationEntity>() on cusAlt.ClassificationId equals classification.Id into 
                              classificationN
                          from classificationX in classificationN.DefaultIfEmpty()
                          where cusAlt.CustomerId == query.Id
                          select new CustomerAlternativeAddressData
                          {
                              Id = cusAlt.Id,
                              Name = cusAlt.Name,
                              AddressNumber = cusAlt.AddressNumber,
                              AddressStreet = cusAlt.AddressStreet,
                              ProvinceId = cusAlt.ProvinceId,
                              ProvinceName = provinceX.Name,
                              DistrictId = cusAlt.DistrictId,
                              DistrictName = districtX.Name,
                              WardId = cusAlt.WardId,
                              WardName = wardX.Name,
                              Phone = cusAlt.Phone,
                              FullAddress = cusAlt.FullAddress,
                              Code = cusAlt.Code,
                              ClassificationId = cusAlt.ClassificationId,
                              ClassificationCode = classificationX.Code,
                          }).ToListAsync();
            return new QueryResult<CustomerAlternativeAddressData>(cusAlts);
        }
    }
}

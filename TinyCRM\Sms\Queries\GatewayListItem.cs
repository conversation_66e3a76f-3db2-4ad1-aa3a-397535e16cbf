﻿using System;

namespace TinyCRM.Sms.Queries
{
    public class GatewayListItem
    {
        public Guid Id { get; set; }

        public string Name { get; set; }

        public string Endpoint { get; set; }

        public string PhonerNumberPrefix { get; set; }

        public Guid GatewayIntergrationId { get; set; }

        public bool IsDefault { get; set; }

        public int TotalCount { get; set; }

        //public string GetGatewayIntergrationName()
        //{
        //    return Webaby.Container.One<ISmsBroker>().GetGatewayIntergrationName(this.GatewayIntergrationId);
        //}
    }
}

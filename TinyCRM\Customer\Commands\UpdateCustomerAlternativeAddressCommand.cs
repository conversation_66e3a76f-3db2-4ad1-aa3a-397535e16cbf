﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using AutoMapper;
using Webaby;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.Customer.Commands
{
    public class UpdateCustomerAlternativeAddressCommand : CommandBase
    {
        public Guid Id { get; set; }

        public string Name { get; set; }

        public Guid CustomerId { get; set; }

        public string AddressNumber { get; set; }

        public string AddressStreet { get; set; }

        public Guid? RegionId { get; set; }

        public Guid? AreaId { get; set; }

        public Guid? ProvinceId { get; set; }

        public Guid? DistrictId { get; set; }

        public Guid? WardId { get; set; }
    }

    internal class UpdateCustomerAlternativeAddressCommandHandler : CommandHandlerBase<InsertCustomerAlternativeAddressCommand>
    {
        public UpdateCustomerAlternativeAddressCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task ExecuteAsync(InsertCustomerAlternativeAddressCommand command)
        {
            var entity = await EntitySet.GetAsync<CustomerAlternativeAddressEntity>(command.Id);
            if (entity == null)
            {
                throw new InvalidOperationException(T["Không tìm thấy thông tin điểm lẻ/chi nhánh"]);
            }
            Mapper.Map(command, entity);
            await Repository.SaveAsync(entity);
        }
    }
}

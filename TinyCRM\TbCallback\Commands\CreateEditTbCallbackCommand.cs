﻿using Webaby.Data;
using AutoMapper;
using Webaby.Localization;
using Webaby;
using System;
using System.Threading.Tasks;
using TinyCRM.NotificationCase;
using TinyCRM.TbCallback;

namespace TinyCRM.TbCallback.Commands
{
    public class CreateEditTbCallbackCommand : CommandBase
    {
        public Guid Id { get; set; }
        public DateTime CallbackDate { get; set; }
        public DateTime NextReminderTime { get; set; }
        public string Notes { get; set; }
        public TbCallbackSatus Status { get; set; }
        public Guid ReferenceObjectId { get; set; }
    }

    internal class CreateEditTbCallbackCommandHandler : CommandHandlerBase<CreateEditTbCallbackCommand>
    {
        public CreateEditTbCallbackCommandHandler(
            IText text,
            IMapper mapper,
            IRepository repository,
            IEntitySet entitySet,
            IQueryExecutor queryExecutor,
            ICommandExecutor commandExecutor,
            ILocalTransactionManager transactionManager,
            IEventBus eventBus
        ) : base(text, mapper, repository, entitySet, queryExecutor, commandExecutor, transactionManager, eventBus) { }

        public override async Task ExecuteAsync(CreateEditTbCallbackCommand command)
        {
            bool isNewCallback = false;
            DateTime? oldNextReminderTime = null;
            var entity = await EntitySet.GetAsync<TbCallbackEntity>(command.Id);
            if (entity != null)
            {
                oldNextReminderTime = entity.NextReminderTime;
            }
            else
            {
                entity = new TbCallbackEntity();
                isNewCallback = true;
            }
            Mapper.Map(command, entity);
            await Repository.SaveAsync(entity);

            // inject service theo contract name
            //if (isNewCallback)
            //{
            //    Container.One<INotificationCaseService>("tbcallback.createedit.ticketowner").CreateWithId(command.Id);
            //}
            //else
            //{
            //    if (command.Status != TbCallbackSatus.Scheduled)
            //    {
            //        Container.One<INotificationCaseService>("tbcallback.createedit.ticketowner").Close(command.Id);
            //    }
            //    else
            //    {
            //        if (oldNextReminderTime.HasValue && command.NextReminderTime > oldNextReminderTime.Value)
            //        {
            //            Container.One<INotificationCaseService>("tbcallback.createedit.ticketowner").Close(command.Id);
            //            Container.One<INotificationCaseService>("tbcallback.createedit.ticketowner").CreateWithId(command.Id);
            //        }
            //    }
            //}
        }
    }
}
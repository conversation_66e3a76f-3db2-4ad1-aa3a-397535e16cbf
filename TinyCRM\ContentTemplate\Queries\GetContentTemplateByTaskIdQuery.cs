﻿using System;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using Webaby;
using Webaby.Data;
using Webaby.Localization;
using TinyCRM.Phase;

namespace TinyCRM.ContentTemplate.Queries
{
    public class GetContentTemplateByTaskIdQuery : QueryBase<ContentTemplateData>
    {
        public Guid TaskId { get; set; }
    }

    public class GetContentTemplateByTaskIdQueryHandler : QueryHandlerBase<GetContentTemplateByTaskIdQuery, ContentTemplateData>
    {
        public GetContentTemplateByTaskIdQueryHandler(IServiceProvider serviceProvider)
            : base(serviceProvider) { }

        public override async Task<QueryResult<ContentTemplateData>> ExecuteAsync(GetContentTemplateByTaskIdQuery query)
        {
            var contentTemplate = EntitySet.Get<ContentTemplateEntity>();
            var contentTemplateTaskType = EntitySet.Get<ContentTemplateTaskTypeEntity>();
            var task = EntitySet.Get<TaskEntity>();
            var resultQuery = (from ct in contentTemplate
                               join cttt in contentTemplateTaskType on ct.Id equals cttt.ContentTemplateId
                               join t in task on cttt.TaskTypeId equals t.TaskTypeId
                               where t.Id == query.TaskId
                               select ct);

            return QueryResult.Create(resultQuery, x => Mapper.Map<ContentTemplateData>(x));
        }
    }
}

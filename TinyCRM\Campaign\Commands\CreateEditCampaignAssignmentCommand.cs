﻿using System;
using System.Threading.Tasks;
using TinyCRM.Enums;
using Webaby;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;

namespace TinyCRM.Campaign.Commands
{
    public class CreateEditCampaignAssignmentCommand : CommandBase
    {
        public Guid Id { get; set; }

        public Guid WorkId { get; set; }

        public Guid? OwnerId { get; set; }

        public OwnerType OwnerType { get; set; }

        public DateTime? FinishedDate { get; set; }

        public Guid? ResultId { get; set; }

        public string ResultObjectType { get; set; }

        public AssignmentStatus Status { get; set; }

        public Guid CreatedBy { get; set; }

        public DateTime CreatedDate { get; set; }
    }

    internal class CreateEditCampaignAssignmentCommandHandler : CommandHandlerBase<CreateEditCampaignAssignmentCommand>
    {
        public CreateEditCampaignAssignmentCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task ExecuteAsync(CreateEditCampaignAssignmentCommand command)
        {
            var entity = await EntitySet.GetAsync<CampaignAssignmentEntity>(command.Id);
            if (entity == null)
            {
                entity = new CampaignAssignmentEntity();
            }
            entity.Id = command.Id;
            entity.WorkId = command.WorkId;
            entity.OwnerId = command.OwnerId;
            entity.OwnerType = command.OwnerType;
            entity.ResultId = command.ResultId;
            entity.ResultObjectType = command.ResultObjectType;
            entity.FinishedDate = command.FinishedDate;
            entity.Status = command.Status;
            entity.CreatedBy = command.CreatedBy;
            entity.CreatedDate = command.CreatedDate;

            await Repository.SaveAsync(entity);
        }
    }
}

﻿using Webaby.Data;
using AutoMapper;
using Webaby.Localization;
using Webaby;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace TinyCRM.Campaign.Queries
{
    public class GetSurveyFeedbackFromCampaignWorkQuery : QueryBase<Guid>
    {
        public Guid CampaignWorkId { get; set; }
    }

    internal class GetSurveyFeedbackFromCampaignWorkQueryHandler : QueryHandlerBase<GetSurveyFeedbackFromCampaignWorkQuery, Guid>
    {
        public GetSurveyFeedbackFromCampaignWorkQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<Guid>> ExecuteAsync(GetSurveyFeedbackFromCampaignWorkQuery query)
        {
            var workList = await EntitySet.GetAsync<CampaignWorkEntity>();
            var assignList = await EntitySet.GetAsync<CampaignAssignmentEntity>();
            var feedbackList = await EntitySet.GetAsync<Survey.SurveyFeedbackEntity>();
            var last_ca = (from w in workList
                           join ca in assignList on w.CurrentAssignmentId equals ca.Id
                           where w.Id == query.CampaignWorkId
                           orderby ca.CreatedDate descending
                           select ca).FirstOrDefault();
            if (last_ca != null)
            {
                var result = feedbackList.Where(x => x.Id == last_ca.ResultId);
                if (result.Any())
                {
                    return QueryResult.Create(result.Select(x => x.Id));
                }
            }
            return QueryResult.Create(new List<Guid>().AsEnumerable());
        }
    }
}

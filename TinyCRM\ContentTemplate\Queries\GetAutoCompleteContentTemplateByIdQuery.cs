﻿using System;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;

namespace TinyCRM.ContentTemplate.Queries
{
    public class GetAutoCompleteContentTemplateByIdQuery : QueryBase<AutoCompleteContentTemplateData>
    {
        public Guid Id { get; set; }
    }

    internal class GetAutoCompleteContentTemplateByIdQueryHandler : QueryHandlerBase<GetAutoCompleteContentTemplateByIdQuery, AutoCompleteContentTemplateData>
    {
        public GetAutoCompleteContentTemplateByIdQueryHandler(IServiceProvider serviceProvider)
            : base(serviceProvider) { }

        public override async Task<QueryResult<AutoCompleteContentTemplateData>> ExecuteAsync(GetAutoCompleteContentTemplateByIdQuery query)
        {
            var autoCompleteEntity = await EntitySet.GetAsync<AutoCompleteContentTemplateEntity>(query.Id);
            return new QueryResult<AutoCompleteContentTemplateData>(Mapper.Map<AutoCompleteContentTemplateData>(autoCompleteEntity));
        }
    }
}

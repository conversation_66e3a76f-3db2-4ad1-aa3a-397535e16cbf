﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Webaby;

namespace TinyCRM.Outbound.Campaign.Queries
{
    public class GetCampaignParameterListQuery : QueryBase<CampaignParameterData>
    {
        public Guid CampaignId { get; set; }
    }

    internal class GetCampaignParameterListQueryHandler : QueryHandlerBase<GetCampaignParameterListQuery, CampaignParameterData>
    {
        public override QueryResult<CampaignParameterData> Execute(GetCampaignParameterListQuery query)
        {
            var campaignParameterQuery = EntitySet.Get<CampaignParameterEntity>().Where(cp => cp.CampaignId == query.CampaignId);
            return QueryResult.Create(campaignParameterQuery, CampaignParameterData.FromEntity);
        }
    }
}
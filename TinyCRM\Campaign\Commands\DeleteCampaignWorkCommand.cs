﻿using System;
using Webaby;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;

namespace TinyCRM.Campaign.Commands
{
    public class DeleteCampaignWorkCommand : CommandBase
    {
        public Guid Id { get; set; }
    }

    internal class DeleteCampaignWorkCommandHandler : CommandHandlerBase<DeleteCampaignWorkCommand>
    {
        public DeleteCampaignWorkCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task ExecuteAsync(DeleteCampaignWorkCommand command)
        {
            var entity = await EntitySet.GetAsync<CampaignWorkEntity>(command.Id);
            if (entity != null)
            {
                await Repository.DeleteAsync(entity);
            }
        }
    }
}

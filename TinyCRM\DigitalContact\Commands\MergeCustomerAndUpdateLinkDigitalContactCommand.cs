﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using TinyCRM.Customer;
using Webaby;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;

namespace TinyCRM.DigitalContact.Commands
{
    public class MergeCustomerAndUpdateLinkDigitalContactCommand : CommandBase
    {
        public string CustomerName { get; set; }
        public Guid CustomerIdActive { get; set; }
        public Guid CustomerIdInActive { get; set; }
        public List<string> ListDiagitalContactLink { get; set; }
    }

    internal class MergeCustomerAndUpdateLinkDigitalContactCommandHandler : CommandHandlerBase<MergeCustomerAndUpdateLinkDigitalContactCommand>
    {
        public MergeCustomerAndUpdateLinkDigitalContactCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task ExecuteAsync(MergeCustomerAndUpdateLinkDigitalContactCommand command)
        {
            var listUpdateDigitalContact = new List<DigitalContactEntity>();

            foreach (var item in command.ListDiagitalContactLink)
            {
                var digitalContactTypeId = new Guid(item.Split(';')[0]);
                var userId = item.Split(';')[1];
                var ceDigitalContact = (await EntitySet.GetAsync<DigitalContactEntity>()).FirstOrDefault(x => x.UserId == userId && x.DigitalContactTypeId == digitalContactTypeId);
                if (ceDigitalContact != null)
                {
                    ceDigitalContact.Name = command.CustomerName;
                    ceDigitalContact.CustomerId = command.CustomerIdActive;
                }
                else
                {
                    ceDigitalContact = new DigitalContactEntity();
                    ceDigitalContact.Id = Guid.NewGuid();
                    ceDigitalContact.Name = command.CustomerName;
                    ceDigitalContact.DigitalContactTypeId = digitalContactTypeId;
                    ceDigitalContact.UserId = userId;
                    ceDigitalContact.CustomerId = command.CustomerIdActive;
                }
                //Cập nhật DigitalContact link với Customer mới
                listUpdateDigitalContact.Add(ceDigitalContact);
                var markAnonymous = (await EntitySet.GetAsync<DigitalContactEntity>()).FirstOrDefault(x => x.CustomerId == command.CustomerIdActive && x.DigitalContactTypeId == digitalContactTypeId);
                if (markAnonymous != null)
                {
                    //DigitalContact cũ sẽ thành ẩn danh
                    markAnonymous.CustomerId = null;
                    markAnonymous.Name = null;
                    listUpdateDigitalContact.Add(markAnonymous);
                }
            }
            await Repository.SaveAsync(listUpdateDigitalContact);

            //Update các Prospect của Customer in active có trong campaign thành customer active

            var cmd = EntitySet.CreateDbCommand();
            cmd.CommandTimeout = 90;
            cmd.Parameters.AddRange(new[]
            {
                DbParameterHelper.AddNullableGuid(cmd, "@CustomerIdActive", command.CustomerIdActive),
                DbParameterHelper.AddNullableGuid(cmd, "@CustomerIdInActive", command.CustomerIdInActive)
            });

            cmd.CommandText = "dbo.MergeCustomerAndUpdateLinkDigitalContact";
            cmd.CommandType = CommandType.StoredProcedure;
            await EntitySet.ExecuteNonQueryAsync(cmd);
        }
    }
}
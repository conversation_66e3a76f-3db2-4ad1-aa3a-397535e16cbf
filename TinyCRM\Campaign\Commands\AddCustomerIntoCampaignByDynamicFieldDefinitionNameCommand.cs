﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;

namespace TinyCRM.Campaign.Commands
{
    public class AddCustomerIntoCampaignByDynamicFieldDefinitionNameCommand : CommandBase
    {
        public string DynamicFieldDefinitionName { get; set; }

        public Guid CampaignId { get; set; }

        public Guid DynamicFormId { get; set; }

        public Guid UserId { get; set; }
    }

    internal class AddCustomerIntoCampaignByDynamicFieldDefinitionNameCommandHandler : CommandHandlerBase<AddCustomerIntoCampaignByDynamicFieldDefinitionNameCommand>
    {
        public AddCustomerIntoCampaignByDynamicFieldDefinitionNameCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task ExecuteAsync(AddCustomerIntoCampaignByDynamicFieldDefinitionNameCommand command)
        {
            var cmd = EntitySet.CreateDbCommand();
            cmd.CommandType = CommandType.StoredProcedure;
            cmd.Parameters.Add(DbParameterHelper.AddNullableString(cmd, "@DynamicFieldDefinitionName", command.DynamicFieldDefinitionName));
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@CampaignId", command.CampaignId));
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@DynamicFormId", command.DynamicFormId));
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@CreatedBy", command.UserId));
            cmd.CommandText = "dbo.AddCustomerIntoCampaignByDynamicFieldDefinitionName";

            await EntitySet.ExecuteNonQueryAsync(cmd);
        }
    }
}
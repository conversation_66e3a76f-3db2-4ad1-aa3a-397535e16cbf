﻿using Microsoft.Data.SqlClient;
using Newtonsoft.Json;
using System;
using System.Data;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;
using Webaby.Localization;
using Webaby.Validation.PhoneNumber;

namespace TinyCRM.Customer.Queries
{
    public class GetCustomerDuplicateQuery : QueryBase<CustomerData>
    {
        public Guid? Id { get; set; }

        public int Type { get; set; }

        public bool? IsBackendCustomer { get; set; }

        public string Phone1 { get; set; }

        public string Phone2 { get; set; }

        public string Phone3 { get; set; }

        public string Email { get; set; }

        public string FacebookId { get; set; }
    }

    internal class GetCustomerDuplicateQueryHandler : QueryHandlerBase<GetCustomerDuplicateQuery, CustomerData>
    {
        public string CustomerDedupColumns { get; set; }
        IText _text { get; set; }
        public GetCustomerDuplicateQueryHandler(IServiceProvider serviceProvider, IText text) : base(serviceProvider) { _text = text; }

        public override async Task<QueryResult<CustomerData>> ExecuteAsync(GetCustomerDuplicateQuery query)
        {
            DataTable paramValues = new DataTable("StringString");
            paramValues.Columns.Add("Value1", typeof(string));
            paramValues.Columns.Add("Value2", typeof(string));

            string phone1 = PhoneNumberValidator.Input(query.Phone1, _text).Output;
            string phone2 = PhoneNumberValidator.Input(query.Phone2, _text).Output;
            string phone3 = PhoneNumberValidator.Input(query.Phone3, _text).Output;

            paramValues.Rows.Add("Phone1", string.IsNullOrEmpty(phone1) ? string.Empty : phone1.Trim());
            paramValues.Rows.Add("Phone2", string.IsNullOrEmpty(phone2) ? string.Empty : phone2.Trim());
            paramValues.Rows.Add("Phone3", string.IsNullOrEmpty(phone3) ? string.Empty : phone3.Trim());
            paramValues.Rows.Add("Email", string.IsNullOrEmpty(query.Email) ? string.Empty : query.Email.Trim());
            paramValues.Rows.Add("FacebookId", string.IsNullOrEmpty(query.FacebookId) ? string.Empty : query.FacebookId.Trim());

            var customerDedupConfiguration = string.IsNullOrEmpty(CustomerDedupColumns) ? new CustomerDedupConfiguration() : JsonConvert.DeserializeObject<CustomerDedupConfiguration>(CustomerDedupColumns);
            DataSet dedupColumns = CustomerDedupDefinition.GetFromJsonString(CustomerDedupColumns);

            var cmd = EntitySet.CreateDbCommand();
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@Id", query.Id));
            cmd.Parameters.Add(DbParameterHelper.AddNullableInt(cmd, "@Type", query.Type));
            cmd.Parameters.Add(DbParameterHelper.NewNullableBooleanParameter(cmd, "@IsBackendCustomer", query.IsBackendCustomer));
            var paramValuesParam = cmd.CreateParameter();
            paramValuesParam.ParameterName = "@ParamValues";
            paramValuesParam.Value = paramValues;
            paramValuesParam.DbType = System.Data.DbType.Object;
            cmd.Parameters.Add(paramValuesParam);

            cmd.Parameters.AddRange(new[]
            {
                new SqlParameter("@CustomerDedupColumns", SqlDbType.Structured)
                {
                    Value = dedupColumns.Tables["DedupSingle"]
                },
            });     
            
            cmd.CommandType = CommandType.StoredProcedure;
            cmd.CommandText = "dbo.GetCustomerDuplicate";
            var mainQuery = await EntitySet.ExecuteReadCommandAsync<CustomerData>(cmd);
            return new QueryResult<CustomerData>(mainQuery);
        }
    }
}
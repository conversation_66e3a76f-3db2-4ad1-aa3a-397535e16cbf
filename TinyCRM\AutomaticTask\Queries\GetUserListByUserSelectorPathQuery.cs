﻿using Webaby.Data;
using AutoMapper;
using Webaby.Localization;
using Webaby;
using System.Threading.Tasks;
using System;
using System.Collections.Generic;
using System.Linq;

namespace TinyCRM.AutomaticTask.Queries
{
    public class GetUserListByUserSelectorPathQuery : QueryBase<Guid>
    {
        public Guid RequestTicketId { get; set; }
        public Guid? TaskId { get; set; }
        public string UserSelectorPath { get; set; }
        public SelectorPathStaticParam StaticParam { get; set; }
    }

    internal class GetUserListByUserSelectorPathQueryHandler : QueryHandlerBase<GetUserListByUserSelectorPathQuery, Guid>
    {        
        IQueryExecutor _queryExecutor { get; set; }

        public GetUserListByUserSelectorPathQueryHandler(IServiceProvider serviceProvider, IQueryExecutor queryExecutor)
            : base(serviceProvider) { _queryExecutor = queryExecutor; }

        public override async Task<QueryResult<Guid>> ExecuteAsync(GetUserListByUserSelectorPathQuery query)
        {
            Dictionary<string, string> staticParams = new Dictionary<string, string>();
            staticParams.Add("{{TicketCreator}}", query.StaticParam.RequestTicketCreatorId.ToString());
            staticParams.Add("{{TicketOwner}}", query.StaticParam.RequestTicketOwnerId.IsNullOrEmpty() ? string.Empty : query.StaticParam.RequestTicketOwnerId.Value.ToString());
            staticParams.Add("{{TaskCreator}}", query.StaticParam.TaskCreatorId.IsNullOrEmpty() ? string.Empty : query.StaticParam.TaskCreatorId.Value.ToString());
            staticParams.Add("{{TaskOwner}}", query.StaticParam.TaskAssigneeId.IsNullOrEmpty() ? string.Empty : query.StaticParam.TaskAssigneeId.Value.ToString());
            if (staticParams.ContainsKey(query.UserSelectorPath))
            {
                var userIdString = staticParams[query.UserSelectorPath];
                if (!string.IsNullOrEmpty(userIdString))
                {
                    var userId = new Guid(userIdString);
                    return QueryResult.Create(new List<Guid>() { userId });
                }
            }
            else
            {
                query.UserSelectorPath = query.UserSelectorPath.Replace("{{RequestTicketId}}", query.RequestTicketId.ToString());
                string originOrgPathSelector = string.Empty, userPathSelector = string.Empty;
                string[] userSelectorPaths = query.UserSelectorPath.Split('|');
                if (userSelectorPaths.Count() > 1)
                {
                    originOrgPathSelector = userSelectorPaths[0];
                    userPathSelector = userSelectorPaths[1];
                }
                else
                {
                    userPathSelector = userSelectorPaths[0];
                }
                Guid originOrgId = Guid.Empty;
                if (originOrgPathSelector.IsNotNullOrEmpty())
                {
                    if (!Guid.TryParse(originOrgPathSelector, out originOrgId))
                    {
                        var firstOriginOrgIds = await _queryExecutor.ExecuteManyAsync(new GetOriginOrgIdQuery { RequestTicketId = query.RequestTicketId, TaskId = query.TaskId, UserPathSelector = originOrgPathSelector, StaticParams = staticParams });
                        if (firstOriginOrgIds != null && firstOriginOrgIds.Count() > 0)
                        {
                            originOrgId = firstOriginOrgIds.FirstOrDefault();
                        }
                    }
                }
                return await _queryExecutor.ExecuteAsync(new GetIdsFromSelectorPathQuery { OriginId = originOrgId, Path = userPathSelector });
            }
            return QueryResult.Create(new List<Guid>());
        }
    }

    public class SelectorPathStaticParam
    {
        public Guid RequestTicketCreatorId { get; set; }
        public Guid? RequestTicketOwnerId { get; set; }
        public Guid? TaskCreatorId { get; set; }
        public Guid? TaskAssigneeId { get; set; }
    }
}
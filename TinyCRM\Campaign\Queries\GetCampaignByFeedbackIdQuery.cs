﻿using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TinyCRM.Outbound.Campaign;
using TinyCRM.Outbound.Prospect;
using TinyCRM.Survey;
using Webaby;
using Webaby.Core.UserAccount;
using Webaby.Data;

namespace TinyCRM.Campaign.Queries
{
    public class GetCampaignByFeedbackIdQuery : QueryBase<CampaignData>
    {
        public GetCampaignByFeedbackIdQuery(Guid Id)
        {
            SurveyFeedbackId = Id;
        }
        public Guid SurveyFeedbackId { get; set; }
    }

    internal class GetCampaignByFeedbackIdQueryHandler : QueryHandlerBase<GetCampaignByFeedbackIdQuery, CampaignData>
    {
        public GetCampaignByFeedbackIdQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<CampaignData>> ExecuteAsync(GetCampaignByFeedbackIdQuery query)
        {
            var campaign = await (from p in EntitySet.Get<ProspectEntity>()
                            join sf in EntitySet.Get<SurveyFeedbackEntity>() on p.ReferenceResultId equals sf.Id
                            join c in EntitySet.Get<CampaignEntity>() on p.CampaignId  equals c.Id
                            where sf.Id == query.SurveyFeedbackId
                            select c
                            ).ToListAsync();
            var result = Mapper.Map<List<CampaignData>>(campaign);
            return QueryResult.Create(result);
        }
    }
}

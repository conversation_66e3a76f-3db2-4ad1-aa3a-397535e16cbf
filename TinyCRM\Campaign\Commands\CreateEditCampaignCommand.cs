﻿using System;
using TinyCRM.Enums;
using TinyCRM.Outbound.Campaign;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;
using Webaby;

namespace TinyCRM.Campaign.Commands
{
    public class CreateEditCampaignCommand : CommandBase
    {
        public Guid Id { get; set; }

        public string Name { get; set; }

        public CampaignType Type { get; set; }

        public DateTime? StartDate { get; set; }

        public DateTime? EndDate { get; set; }

        public CampaignStatus Status { get; set; }

        public Guid? ResultCodeSuiteId { get; set; }
    }

    internal class CreateEditCampaignCommandHandler : CommandHandlerBase<CreateEditCampaignCommand>
    {
        public CreateEditCampaignCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task ExecuteAsync(CreateEditCampaignCommand command)
        {
            var entity = await EntitySet.GetAsync<CampaignEntity>(command.Id);
            if (entity == null)
            {
                entity = new CampaignEntity();
            }
            entity.Id = command.Id;
            entity.CampaignName = command.Name;
            entity.CampaignType = command.Type;
            entity.StartDate = command.StartDate;
            entity.EndDate = command.EndDate;
            entity.Status = command.Status;
            entity.ResultCodeSuiteId = command.ResultCodeSuiteId;
            await Repository.SaveAsync(entity);
        }
    }
}

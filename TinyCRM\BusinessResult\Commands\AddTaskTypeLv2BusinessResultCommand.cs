﻿using AutoMapper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TinyCRM.TaskType;
using Webaby;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.BusinessResult.Commands
{
    public class AddTaskTypeLv2BusinessResultCommand : CommandBase
    {
        public Guid TaskTypeId { get; set; }

        public List<Guid> BusinessResultList { get; set; }
        public Guid NodeParentId { get; set; }
    }

    internal class AddTaskTypeLv2BusinessResultCommandHandler : CommandHandlerBase<AddTaskTypeLv2BusinessResultCommand>
    {
        public AddTaskTypeLv2BusinessResultCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task ExecuteAsync(AddTaskTypeLv2BusinessResultCommand command)
        {
            List<IEntity> deletedEntites = new List<IEntity>();
            var listchild = (from br in EntitySet.Get<BusinessResultEntity>()
                             join ttbr in EntitySet.Get<BusinessResultReferenceEntity>() on br.Id equals ttbr.BusinessResultId
                             where ttbr.ReferenceObjectId == command.TaskTypeId && br.ParentId == command.NodeParentId
                             select ttbr
                             ).ToList();
            deletedEntites.AddRange(listchild);
            await Repository.DeleteAsync(deletedEntites);

            if (command.BusinessResultList != null)
            {
                List<IEntity> savedEntites = new List<IEntity>();
                foreach (var businessResultId in command.BusinessResultList)
                {
                    BusinessResultReferenceEntity taskTypeBusinessResultEntity = new BusinessResultReferenceEntity();
                    taskTypeBusinessResultEntity.ReferenceObjectId = command.TaskTypeId;
                    taskTypeBusinessResultEntity.BusinessResultId = businessResultId;
                    savedEntites.Add(taskTypeBusinessResultEntity);
                }

                await Repository.SaveAsync(savedEntites);
            }
        }
    }
}
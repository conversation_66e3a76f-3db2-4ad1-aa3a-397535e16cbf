﻿using System.Data;
using System.Data.SqlClient;
using Webaby;
using TinyCRM.Customer.Queries;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;
using Microsoft.Extensions.Configuration;
using Microsoft.Data.SqlClient;

namespace TinyCRM.Customer.Commands
{
    public class DedupBackendAndTempCustomersCommand : CommandBase
    {
    }

    internal class DedupBackendAndTempCustomersCommandHandler : CommandHandlerBase<DedupBackendAndTempCustomersCommand>
    {
        IConfiguration _configuration { get; set; }
        
        public string CustomerDedupColumns { get { return _configuration.GetValue<string>("customer.dedup.definitions"); } }

        public DedupBackendAndTempCustomersCommandHandler(IServiceProvider serviceProvider, IConfiguration configuration) : base(serviceProvider) { _configuration = configuration; }

        public override async Task ExecuteAsync(DedupBackendAndTempCustomersCommand command)
        {
            DataSet dedupColumns = CustomerDedupDefinition.GetFromJsonString(CustomerDedupColumns);

            var cmd = EntitySet.CreateDbCommand();
            cmd.Parameters.AddRange(new[]
            {
                new SqlParameter("@CustomerDedupColumns", SqlDbType.Structured)
                {
                    Value = dedupColumns.Tables["DedupSingle"]
                },
            });
            cmd.CommandType = CommandType.StoredProcedure;
            cmd.CommandText = "dbo.DedupBackendAndTempCustomers";
            await EntitySet.ExecuteNonQueryAsync(cmd);
        }
    }
}
﻿using Microsoft.EntityFrameworkCore;
using System.Linq;
using TinyCRM.Behavior;
using TinyCRM.Behavior.Queries;
using TinyCRM.Enums;
using Webaby;

namespace TinyCRM.Customer.Queries
{
    public class GetClassificationQuery : QueryBase<ClassificationData>
    {
        public CustomerType? Type { get; set; }
    }

    internal class GetClassificationQueryHandler : QueryHandlerBase<GetClassificationQuery, ClassificationData>
    {
        public GetClassificationQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<ClassificationData>> ExecuteAsync(GetClassificationQuery query)
        {
            var classificationEntities = await EntitySet.GetAsync<ClassificationEntity>();
            if (query.Type.HasValue)
            {
                classificationEntities = classificationEntities.Where(x => x.Type == query.Type);
            }
            return new QueryResult<ClassificationData>(Mapper.Map<ClassificationData>(classificationEntities));            
        }
    }
}

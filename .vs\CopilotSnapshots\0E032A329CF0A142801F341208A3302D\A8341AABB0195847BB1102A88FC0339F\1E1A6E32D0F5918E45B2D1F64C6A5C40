﻿using System;
using System.Linq;
using Webaby;

namespace TinyCRM.Outbound.CallResult.Queries
{
    public class GetCallStrategyListQuery : QueryBase<CallStrategyData>
    {
        public Guid? CallResultId { get; set; }
    }

    internal class GetCallStrategyListQueryHandler : QueryHandlerBase<GetCallStrategyListQuery, CallStrategyData>
    {
        public override QueryResult<CallStrategyData> Execute(GetCallStrategyListQuery query)
        {
            var callStrategyQuery = EntitySet.Get<CallResultCallPlanStrategyEntity>();
            if (query.CallResultId.HasValue)
            {
                callStrategyQuery = callStrategyQuery.Where(t => t.CallResultId == query.CallResultId);
            }
            return QueryResult.Create(callStrategyQuery, query.Pagination, CallStrategyData.FromEntity);
        }
    }
}
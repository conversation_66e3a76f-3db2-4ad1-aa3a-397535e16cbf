﻿using Webaby.Data;
using AutoMapper;
using Webaby.Localization;
using Webaby;
using System.Threading.Tasks;

namespace TinyCRM.AutomaticTask.Queries
{
    public class GetAllUserSelectorPathQuery : QueryBase<UserPathSelectorData>
    {
    }

    internal class GetAllUserSelectorPathQueryHandler : QueryHandlerBase<GetAllUserSelectorPathQuery, UserPathSelectorData>
    {
        public GetAllUserSelectorPathQueryHandler(IServiceProvider serviceProvider)
            : base(serviceProvider) { }

        public override async Task<QueryResult<UserPathSelectorData>> ExecuteAsync(GetAllUserSelectorPathQuery query)
        {
            var entity = await EntitySet.GetAsync<UserPathSelectorEntity>();
            var mapped = entity.Select(x => Mapper.Map<UserPathSelectorData>(x));
            return QueryResult.Create(mapped);
        }
    }
}

﻿using System.Data;
using System.Data.SqlClient;
using Webaby;
using Webaby.Data;

namespace TinyCRM.Customer.Queries
{
    public class SearchInboundCustomerAlternativeAddressesQuery : QueryBase<CustomerAlternativeAddressItem>
    {
        public string PhoneNumber
        {
            get; set;
        }

        public string Email
        {
            get; set;
        }

        public string FacebookId
        {
            get; set;
        }
    }

    internal class SearchInboundCustomerAlternativeAddressesQueryHandler : QueryHandlerBase<SearchInboundCustomerAlternativeAddressesQuery, CustomerAlternativeAddressItem>
    {
        public SearchInboundCustomerAlternativeAddressesQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<CustomerAlternativeAddressItem>> ExecuteAsync(SearchInboundCustomerAlternativeAddressesQuery query)
        {
            var cmd = EntitySet.CreateDbCommand();
            cmd.Parameters.AddRange(new[]
            {
                DbParameterHelper.AddNullableString(cmd ,"@PhoneNumber", query.PhoneNumber.IsNullOrEmpty() ? string.Empty : query.PhoneNumber),
                DbParameterHelper.AddNullableString(cmd, "@Email", query.Email.IsNullOrEmpty() ? string.Empty : query.Email),
                DbParameterHelper.AddNullableString(cmd, "@FacebookId", query.FacebookId.IsNullOrEmpty() ? string.Empty : query.FacebookId),
            });

            cmd.CommandText = "SearchInboundCustomerAlternativeAddresses";
            cmd.CommandType = CommandType.StoredProcedure;
            var mainQuery = await EntitySet.ExecuteReadCommandAsync<CustomerAlternativeAddressItem>(cmd);
            return new QueryResult<CustomerAlternativeAddressItem>(mainQuery);
        }
    }
}

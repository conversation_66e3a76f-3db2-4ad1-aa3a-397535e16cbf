﻿using System;
using Webaby;
using System.Linq;
using Webaby.Core.DynamicForm;
using Webaby.Core.File;
using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.Outbound.Campaign.Queries
{
    public class GetCampaignByDynamicFormIdQuery : QueryBase<CampaignData>
    {
        public Guid DynamicFormId { get; set; }
    }

    internal class GetCampaignByDynamicFormIdQueryHandler : QueryHandlerBase<GetCampaignByDynamicFormIdQuery, CampaignData>
    {
        public GetCampaignByDynamicFormIdQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override QueryResult<CampaignData> Execute(GetCampaignByDynamicFormIdQuery query)
        {
            var campaign = EntitySet.Get<CampaignEntity>().Where(x => x.DynamicFormId == query.DynamicFormId).FirstOrDefault();
            var campaignData = CampaignData.FromEntity(campaign);
            return new QueryResult<CampaignData>(campaignData);
        }
    }
}
﻿using AutoMapper;
using System;
using System.Linq;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.Building.Commands
{
    public class CreateEditBuildingCommand : CommandBase
    {
        public Guid Id { get; set; }

        public string Name { get; set; }

        public string Code { get; set; }

        public string Address { get; set; }

        public string Phone { get; set; }

        public string Email { get; set; }

        public string Investor { get; set; }

        public int? ProjectArea { get; set; }

        public int? ConstructionArea { get; set; }

        public int? NumsOfFloor { get; set; }

        public int? NumsOfBasementFloor { get; set; }

        public int? NumsOfElevator { get; set; }
    }

    internal class CreateEditBuildingCommandHandler : CommandHandlerBase<CreateEditBuildingCommand>
    {
        public CreateEditBuildingCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task ExecuteAsync(CreateEditBuildingCommand command)
        {
            var entity = await EntitySet.GetAsync<BuildingEntity>(command.Id);
            if (entity == null)
            {
                entity = new BuildingEntity();
            }
            Mapper.Map(command, entity);
            await Repository.SaveAsync(entity);
        }
    }
}

﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TinyCRM.Callback;
using TinyCRM.Callback.Queries;
using Webaby;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;

namespace TinyCRM.Callback.Queries
{
    public class CallbackSettingListQuery : QueryBase<CallbackSettingsData>
    {
        public Guid? Id { get; set; }
        public string Name { get; set; }
    }

    internal class CallbackSettingListQueryHandler : QueryHandlerBase<CallbackSettingListQuery, CallbackSettingsData>
    {
        public CallbackSettingListQueryHandler(IServiceProvider serviceProvider)
            : base(serviceProvider) { }

        public override async Task<QueryResult<CallbackSettingsData>> ExecuteAsync(CallbackSettingListQuery query)
        {
            var callbackSettingQuery = EntitySet.Get<CallbackSettingsEntity>();
            if (query.Id.HasValue)
            {
                callbackSettingQuery = callbackSettingQuery.Where(x => x.Id == query.Id);
            }
            if (!string.IsNullOrWhiteSpace(query.Name))
            {
                callbackSettingQuery = callbackSettingQuery.Where(x => x.Name.Contains(query.Name));
            }
            return QueryResult.Create(callbackSettingQuery, query.Pagination, x => Mapper.Map<CallbackSettingsData>(x));
        }
    }
}
﻿using AutoMapper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TinyCRM.TaskType;
using Webaby;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.BusinessResult.Commands
{
    public class AddTaskTypeBusinessResultCommand : CommandBase
    {
        public Guid TaskTypeId { get; set; }

        public List<Guid> BusinessResultList { get; set; }

        public string ReferenceType { get; set; }
    }

    internal class AddTaskTypeBusinessResultCommandHandler : CommandHandlerBase<AddTaskTypeBusinessResultCommand>
    {
        public AddTaskTypeBusinessResultCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task ExecuteAsync(AddTaskTypeBusinessResultCommand command)
        {
            List<IEntity> savedEntites = new List<IEntity>();
            foreach (var businessResultId in command.BusinessResultList)
            {
                BusinessResultReferenceEntity taskTypeBusinessResultEntity = new BusinessResultReferenceEntity();
                taskTypeBusinessResultEntity.ReferenceObjectId = command.TaskTypeId;
                taskTypeBusinessResultEntity.BusinessResultId = businessResultId;
                taskTypeBusinessResultEntity.ReferenceType = command.ReferenceType;

                savedEntites.Add(taskTypeBusinessResultEntity);
            }

            await Repository.SaveAsync(savedEntites);
        }
    }
}
﻿using System;
using System.Linq;
using Webaby;

namespace TinyCRM.Outbound.Brand.Queries
{
    public class GetBrandListByCompanyQuery : QueryBase<BrandData>
    {
        public Guid CompanyId
        {
            get;
            set;
        }
    }

    internal class GetBrandListByProvinceQueryHandler : QueryHandlerBase<GetBrandListByCompanyQuery, BrandData>
    {
        public override QueryResult<BrandData> Execute(GetBrandListByCompanyQuery query)
        {
            var brandQuery = EntitySet.Get<BrandEntity>();
            brandQuery = brandQuery.Where(b => b.CompanyId == query.CompanyId);

            return QueryResult.Create(brandQuery, BrandData.FromEntity);
        }
    }
}
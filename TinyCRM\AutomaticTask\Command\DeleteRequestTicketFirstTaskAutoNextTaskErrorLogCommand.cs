﻿using System;
using System.Data;
using System.Data.SqlClient;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;

namespace TinyCRM.AutomaticTask.Command
{
    public class DeleteRequestTicketFirstTaskAutoNextTaskErrorLogCommand : CommandBase
    {
        public Guid RequestTicketId { get; set; }

        public Guid ExecutedUserId { get; set; }
    }

    internal class DeleteRequestTicketFirstTaskAutoNextTaskErrorLogCommandHandler : CommandHandlerBase<DeleteRequestTicketFirstTaskAutoNextTaskErrorLogCommand>
    {
        public DeleteRequestTicketFirstTaskAutoNextTaskErrorLogCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task ExecuteAsync(DeleteRequestTicketFirstTaskAutoNextTaskErrorLogCommand command)
        {
            var cmd = EntitySet.CreateDbCommand();
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@RequestTicketId", command.RequestTicketId));
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@ExecutedUserId", command.ExecutedUserId));            
            cmd.CommandText = "dbo.DeleteRequestTicketFirstTaskAutoNextTaskErrorLogs";
            cmd.CommandType = CommandType.StoredProcedure;
            await EntitySet.ExecuteNonQueryAsync(cmd);
        }
    }
}
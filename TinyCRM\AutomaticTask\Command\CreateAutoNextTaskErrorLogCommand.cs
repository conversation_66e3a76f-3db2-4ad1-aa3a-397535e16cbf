﻿using AutoMapper;
using System;
using Webaby;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.AutomaticTask.Command
{
    public class CreateAutoNextTaskErrorLogCommand : CommandBase
    {
        public Guid Id { get; set; }

        public Guid RequestTicketId { get; set; }

        public Guid? TaskId { get; set; }

        public Guid? AutoNextTaskId { get; set; }

        public string ErrorMessage { get; set; }
    }

    internal class CreateAutoNextTaskErrorLogCommandHandler : CommandHandlerBase<CreateAutoNextTaskErrorLogCommand>
    {
        public CreateAutoNextTaskErrorLogCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }
        public override async Task ExecuteAsync(CreateAutoNextTaskErrorLogCommand command)
        {
            var entity = new AutoNextTaskErrorLogEntity();            
            Mapper.Map(command, entity);

            await Repository.SaveAsync(entity);
        }
    }
}
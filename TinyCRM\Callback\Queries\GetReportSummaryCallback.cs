﻿using System;
using System.Data;
using System.Data.SqlClient;
using System.Threading.Tasks;
using AutoMapper;
using TinyCRM.Report;
using Webaby;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.Callback.Queries
{
    public class GetReportSummaryCallbackQuery : QueryBase<ReportSummaryCallbackData>
    {
        public DateTime? FromDate { get; set; }

        public DateTime? ToDate { get; set; }

        public string AgentId { get; set; }
    }

    internal class GetReportSummaryCallbackQueryHandler : QueryHandlerBase<GetReportSummaryCallbackQuery, ReportSummaryCallbackData>
    {
        public GetReportSummaryCallbackQueryHandler(IServiceProvider serviceProvider)
            : base(serviceProvider) { }

        public override async Task<QueryResult<ReportSummaryCallbackData>> ExecuteAsync(GetReportSummaryCallbackQuery query)
        {
            var cmd = EntitySet.CreateDbCommand();
            cmd.Parameters.Add(DbParameterHelper.NewNullableDateTimeParameter(cmd, "@FromDate", query.FromDate));
            cmd.Parameters.Add(DbParameterHelper.NewNullableDateTimeParameter(cmd, "@ToDate", query.ToDate));
            cmd.Parameters.Add(DbParameterHelper.AddNullableString(cmd, "@AgentID", query.AgentId.IsNullOrEmpty() ? string.Empty : query.AgentId));

            cmd.CommandText = "dbo.GetReportSummaryCallback";
            cmd.CommandType = CommandType.StoredProcedure;
            var mainQuery = await EntitySet.ExecuteReadCommandAsync<ReportSummaryCallbackData>(cmd);
            return new QueryResult<ReportSummaryCallbackData>(mainQuery);
        }
    }
}
﻿using System;
using System.Data;
using System.Data.SqlClient;
using Webaby;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;
using System.Threading.Tasks;

namespace TinyCRM.DigitalChannel.Queries
{
    public class GetDigitalContactTypeListByCampaignQuery : QueryBase<DigitalContactTypeData>
    {
        public Guid CampaignId { get; set; }
    }

    internal class GetDigitalContactTypeListByCampaignQueryHandler : QueryHandlerBase<GetDigitalContactTypeListByCampaignQuery, DigitalContactTypeData>
    {
        public GetDigitalContactTypeListByCampaignQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<DigitalContactTypeData>> ExecuteAsync(GetDigitalContactTypeListByCampaignQuery query)
        {
            var cmd = EntitySet.CreateDbCommand();
            cmd.CommandType = CommandType.Text;
            cmd.CommandText = @"SELECT	DISTINCT dct.*
                                        FROM	dbo.Campaign c
		                                        JOIN dbo.DigitalPushCode dpc ON dpc.Id = c.DigitalPushCodeId
		                                        JOIN dbo.DigitalPushCodeOnChannel dpcc ON dpcc.DigitalPushCodeId = dpc.Id AND dpcc.PriorityIndex > 0
		                                        JOIN dbo.DigitalChannel dch ON dch.Id = dpcc.DigitalChannelId
		                                        JOIN dbo.DigitalContactType dct ON dct.Id = ISNULL(dpcc.DigitalContactTypeId, dch.DigitalContactTypeId)
                                        WHERE	c.Id = @CampaignId
                                        ORDER BY dct.PriorityOrder ";
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@CampaignId", query.CampaignId));
            var mainQuery = await EntitySet.ExecuteReadCommandAsync<DigitalContactTypeData>(cmd);
            return new QueryResult<DigitalContactTypeData>(mainQuery);
        }
    }
}
﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using Webaby;
using Webaby.Data;

namespace TinyCRM.DigitalContact.Commands
{
    public class AddCustomerDigitalContactToCampaignCommand : CommandBase
    {
        public Guid CampaignId { get; set; }

        public bool? NotInCampaign { get; set; }

        public string Code { get; set; }

        public string Email { get; set; }

        public string DataSource { get; set; }

        public Guid? ProvinceId { get; set; }

        public Guid? SelectedCampaignId { get; set; }

        public List<Guid> ResultCodeIds { get; set; }

        public Guid? DigitalContactTypeId { get; set; }

        public List<Guid> ExcludeCampaignIds { get; set; }

        public string UID { get; set; }

        public Guid? ImportSessionId { get; set; }

        public bool? IsLinked { get; set; }

        public Guid? UserId { get; set; }

        public List<Guid> ContactItemSelectedList { get; set; }

        public int? AssignedNumber { get; set; }
    }

    internal class AddCustomerDigitalContactToCampaignCommandHandler : CommandHandlerBase<AddCustomerDigitalContactToCampaignCommand>
    {
        public AddCustomerDigitalContactToCampaignCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task ExecuteAsync(AddCustomerDigitalContactToCampaignCommand command)
        {
            var cmd = EntitySet.CreateDbCommand();
            cmd.Parameters.AddRange(new[]
            {
                DbParameterHelper.AddNullableGuid(cmd, "@CampaignId", command.CampaignId),
                DbParameterHelper.NewNullableBooleanParameter(cmd, "@NotInCampaign", command.NotInCampaign),

                DbParameterHelper.AddNullableString(cmd,"@Code", command.Code),
                DbParameterHelper.AddNullableString(cmd,"@Email", command.Email),
                DbParameterHelper.AddNullableGuid(cmd,"@ProvinceId", command.ProvinceId),
                DbParameterHelper.AddNullableString(cmd, "@DataSource", command.DataSource.IsNullOrEmpty() ? string.Empty : command.DataSource),

                DbParameterHelper.AddNullableString(cmd, "@UID", command.UID),
                DbParameterHelper.AddNullableGuid(cmd,"@SelectedCampaignId", command.SelectedCampaignId, true),
                DbParameterHelper.NewIdListParameter("@ResultCodeIds", command.ResultCodeIds),
                DbParameterHelper.NewIdListParameter("@ExcludeCampaignIds", command.ExcludeCampaignIds),
                DbParameterHelper.AddNullableGuid(cmd,"@ImportSessionId", command.ImportSessionId),
                DbParameterHelper.AddNullableGuid(cmd,"@DigitalContactTypeId", command.DigitalContactTypeId),

                DbParameterHelper.NewNullableBooleanParameter(cmd, "@IsLinked", command.IsLinked),

                DbParameterHelper.NewIdListParameter("@ContactItemSelectedList", command.ContactItemSelectedList),
                DbParameterHelper.AddNullableInt(cmd,"@AssignedNumber", command.AssignedNumber),
                DbParameterHelper.AddNullableGuid(cmd,"@UserId", command.UserId),

            });

            cmd.CommandText = "dbo.DigitalCampaign_AddCustomerDigitalContactToCampaign";
            cmd.CommandType = CommandType.StoredProcedure;

            await EntitySet.ExecuteNonQueryAsync(cmd);
        }
    }
}
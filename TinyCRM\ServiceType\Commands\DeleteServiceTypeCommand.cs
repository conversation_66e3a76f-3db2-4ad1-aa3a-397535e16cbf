﻿using AutoMapper;
using System;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Threading.Tasks;
using TinyCRM.EntityLink;
using TinyCRM.ServiceType.Events;
using Webaby;
//using TinyCRM.ServiceType.Events;
using Webaby.Data;
using Webaby.Localization;
using Webaby.Web;

namespace TinyCRM.ServiceType.Commands
{
    public class DeleteServiceTypeCommand : CommandBase
    {
        public Guid ServiceTypeId { get; set; }
    }

    internal class DeleteServiceTypeCommandHandler : CommandHandlerBase<DeleteServiceTypeCommand>
    {
        public DeleteServiceTypeCommandHandler(
            IText text,
            IMapper mapper,
            IRepository repository,
            IEntitySet entitySet,
            IQueryExecutor queryExecutor,
            ICommandExecutor commandExecutor,
            ILocalTransactionManager transactionManager,
            IEventBus eventBus
        ) : base(text, mapper, repository, entitySet, queryExecutor, commandExecutor, transactionManager, eventBus) { }

        public override async Task ExecuteAsync(DeleteServiceTypeCommand command)
        {
            EventBus.Publish(new ServiceTypeDeleteEvent
            {
                ServiceTypeId = command.ServiceTypeId
            });

            await Repository.DeleteAsync<ServiceTypeEntity>(command.ServiceTypeId);
            //Delete Entitylink with servicetype
            var deleteEntityLink = (await EntitySet.GetAsync<EntityLinkEntity>()).Where(x=>x.ToEntityId == command.ServiceTypeId);
            await Repository.DeleteAsync(deleteEntityLink);
        }
    }
}
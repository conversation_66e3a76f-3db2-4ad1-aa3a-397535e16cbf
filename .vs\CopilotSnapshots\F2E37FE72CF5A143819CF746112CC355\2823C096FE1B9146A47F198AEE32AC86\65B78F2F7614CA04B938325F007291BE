﻿using System;
using System.Data;
using System.Data.SqlClient;
using Webaby;
using Webaby.Data;
using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Webaby.Localization;

namespace TinyCRM.Outbound.Campaign.Queries
{
    public class GetAllCampaignQuery : QueryBase<CampaignData>
    {
    }

    public class GetAllCampaignQueryHandler : QueryHandlerBase<GetAllCampaignQuery, CampaignData>
    {
        public GetAllCampaignQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override QueryResult<CampaignData> Execute(GetAllCampaignQuery query)
        {
            var result = EntitySet.Get<CampaignEntity>();
            return QueryResult.Create(result, CampaignData.FromEntity);
        }
    }
}
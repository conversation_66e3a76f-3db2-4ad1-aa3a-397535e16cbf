﻿using Webaby.Data;
using AutoMapper;
using Webaby.Localization;
using Webaby;
using System;
using System.Data;
using System.Threading.Tasks;

namespace TinyCRM.Campaign.Queries
{
    public class GetAgentCampaignAssignmentSumaryInfoQuery : QueryBase<CampaignAssignmentSumaryInfo>
    {
        public Guid CampaignId { get; set; }
        public Guid UserId { get; set; }
    }

    internal class GetAgentCampaignAssignmentSumaryInfoQueryHandler : QueryHandlerBase<GetAgentCampaignAssignmentSumaryInfoQuery, CampaignAssignmentSumaryInfo>
    {
        public GetAgentCampaignAssignmentSumaryInfoQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<CampaignAssignmentSumaryInfo>> ExecuteAsync(GetAgentCampaignAssignmentSumaryInfoQuery query)
        {
            var cmd = EntitySet.CreateDbCommand();
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@CampaignId", query.CampaignId));
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@UserId", query.UserId));

            cmd.CommandText = @"SELECT COUNT(ca.Id) TotalCount,
                                SUM(CASE WHEN ca.Status = 3 THEN 1 ELSE 0 END) DoneCount
                                FROM dbo.Campaign c
                                    LEFT JOIN dbo.CampaignWork cw ON cw.CampaignId = c.Id AND cw.Deleted=0
                                    LEFT JOIN dbo.CampaignAssignment ca ON ca.Id = cw.CurrentAssignmentId AND ca.Deleted=0 AND ca.OwnerId = @UserId
                                WHERE c.Deleted=0
                                    AND c.Id = @CampaignId";
            cmd.CommandType = CommandType.Text;

            var mainQuery = await EntitySet.ExecuteReadCommandAsync<CampaignAssignmentSumaryInfo>(cmd);
            return new QueryResult<CampaignAssignmentSumaryInfo>(mainQuery);
        }
    }
}
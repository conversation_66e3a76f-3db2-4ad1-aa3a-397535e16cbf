﻿using System;
using System.Data;
using System.Data.SqlClient;
using Webaby;
using Webaby.Data;
using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Webaby.Localization;

namespace TinyCRM.Outbound.Campaign.Queries
{
    public class GetAllCampaignQuery : QueryBase<CampaignData>
    {
    }

    public class GetAllCampaignQueryHandler : QueryHandlerBase<GetAllCampaignQuery, CampaignData>
    {
        public GetAllCampaignQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<CampaignData>> ExecuteAsync(GetAllCampaignQuery query)
        {
            var result = await EntitySet.Get<CampaignEntity>().ToListAsync();
            var mapped = result.Select(x => Mapper.Map<CampaignData>(x));
            return QueryResult.Create(mapped);
        }
    }
}
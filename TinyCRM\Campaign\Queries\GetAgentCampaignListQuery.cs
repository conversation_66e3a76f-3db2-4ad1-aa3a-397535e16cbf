﻿using System;
using System.Data;
using System.Data.SqlClient;
using Webaby;
using Webaby.Core.UserAccount;
using Webaby.Data;

namespace TinyCRM.Campaign.Queries
{
    public class GetAgentCampaignListQuery : QueryBase<AgentCampaignListItem>
    {
        public string CampaignSearch { get; set; }

        public Guid WorkerId { get; set; }
    }

    internal class GetAgentCampaignListQueryHandler : QueryHandlerBase<GetAgentCampaignListQuery, AgentCampaignListItem>
    {
        public GetAgentCampaignListQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<AgentCampaignListItem>> ExecuteAsync(GetAgentCampaignListQuery query)
        {
            var cmd = EntitySet.CreateDbCommand();
            cmd.Parameters.AddRange(new[]
            {
                DbParameterHelper.AddNullableGuid(cmd, "@WorkerId", query.WorkerId),
                DbParameterHelper.AddNullableString(cmd ,"@CampaignSearch", query.CampaignSearch.IsNullOrEmpty() ? string.Empty : query.CampaignSearch),
                DbParameterHelper.AddNullableInt(cmd ,"@StartRow", query.Pagination.StartRow),
                DbParameterHelper.AddNullableInt(cmd ,"@EndRow", query.Pagination.EndRow)
            });

            cmd.CommandText = "GetAgentCampaignList";
            cmd.CommandType = CommandType.StoredProcedure;

            var mainQuery = await EntitySet.ExecuteReadCommandAsync<AgentCampaignListItem>(cmd);

            return new QueryResult<AgentCampaignListItem>(mainQuery);
        }
    }
}
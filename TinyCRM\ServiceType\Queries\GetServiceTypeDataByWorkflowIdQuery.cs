﻿using Webaby.Data;
using AutoMapper;
using Webaby.Localization;
using Webaby;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TinyCRM.ServiceCategory;

namespace TinyCRM.ServiceType.Queries
{
    public class GetServiceTypeDataByWorkflowIdQuery : QueryBase<ServiceTypeData>
    {
        public Guid WorkflowId { get; set; }
    }

    internal class GetServiceTypeDataByWorkflowIdQueryHandler : QueryHandlerBase<GetServiceTypeDataByWorkflowIdQuery, ServiceTypeData>
    {
        public GetServiceTypeDataByWorkflowIdQueryHandler(IEntitySet entitySet, IRepository repository, IText text, IMapper mapper)
            : base(entitySet, repository, text, mapper) { }

        public override async Task<QueryResult<ServiceTypeData>> ExecuteAsync(GetServiceTypeDataByWorkflowIdQuery query)
        {
            var stList = (await EntitySet.GetAsync<ServiceTypeEntity>()).Where(x => x.WorkflowId == query.WorkflowId);
            var lv1List = await EntitySet.GetAsync<ServiceCategoryEntity>();
            var tempQuery = (from st in stList
                             join lv1 in lv1List on st.Level1Id equals lv1.Id
                             join lv2 in lv1List on st.Level2Id equals lv2.Id into _tempLevel2
                             from lv2 in _tempLevel2.DefaultIfEmpty()
                             join lv3 in lv1List on st.Level3Id equals lv3.Id into _tempLevel3
                             from lv3 in _tempLevel3.DefaultIfEmpty()
                             join lv4 in lv1List on st.Level4Id equals lv4.Id into _tempLevel4
                             from lv4 in _tempLevel4.DefaultIfEmpty()
                             select new { ServiceType = st, Level1 = lv1, Level2 = lv2, Level3 = lv3, Level4 = lv4 }).ToList();

            List<ServiceTypeData> mainQuery = tempQuery.Select(x =>
            {
                ServiceTypeData serviceTypeData = Mapper.Map<ServiceTypeData>(x.ServiceType);
                serviceTypeData.Level1Name = x.Level1.Name;
                serviceTypeData.Level1Order = x.Level1.Order;
                if (x.Level2 != null)
                {
                    serviceTypeData.Level2Name = x.Level2.Name;
                    serviceTypeData.Level2Order = x.Level2.Order;
                }
                if (x.Level3 != null)
                {
                    serviceTypeData.Level3Name = x.Level3.Name;
                    serviceTypeData.Level3Order = x.Level3.Order;
                }
                if (x.Level4 != null)
                {
                    serviceTypeData.Level4Name = x.Level4.Name;
                    serviceTypeData.Level4Order = x.Level4.Order;
                }
                return serviceTypeData;
            }).ToList();
            return new QueryResult<ServiceTypeData>(mainQuery);
        }
    }
}
﻿using System;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using Webaby;
using Webaby.Data;
using Webaby.Localization;
using TinyCRM.Enums;

namespace TinyCRM.Customer.Queries
{
    public class GetCustomersByClassificationQuery : QueryBase<CustomerData>
    {
        public Guid ClassificationId { get; set; }
    }

    internal class GetCustomersByClassificationQueryHandler : QueryHandlerBase<GetCustomersByClassificationQuery, CustomerData>
    {
        public GetCustomersByClassificationQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<CustomerData>> ExecuteAsync(GetCustomersByClassificationQuery query)
        {
            var customerEntity = (await EntitySet.GetAsync<CustomerEntity>()).Where(x => x.SourceClassificationId == query.ClassificationId);
            return QueryResult.Create(customerEntity, Mapper.Map<CustomerData>);
        }
    }
}

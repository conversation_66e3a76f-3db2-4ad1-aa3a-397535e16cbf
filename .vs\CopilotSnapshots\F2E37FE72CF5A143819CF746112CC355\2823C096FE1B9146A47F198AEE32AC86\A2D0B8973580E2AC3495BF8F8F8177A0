﻿using System;
using Webaby;
using System.Linq;
using TinyCRM.Outbound.Prospect;
using TinyCRM.Outbound.ProspectAssignment;
using Webaby.Core.File;
using Webaby.Core.File.Queries;
using System.Collections.Generic;
using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.Outbound.Campaign.Queries
{
    public class GetCampaignByIdQuery : QueryBase<CampaignData>
    {
        public Guid Id { get; set; }
    }

    internal class GetCampaignByIdQueryHandler : QueryHandlerBase<GetCampaignByIdQuery, CampaignData>
    {
        public GetCampaignByIdQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override QueryResult<CampaignData> Execute(GetCampaignByIdQuery query)
        {
            var campaign = EntitySet.Get<CampaignEntity>(query.Id);
            if (campaign == null)
            {
                throw new InvalidOperationException(T["Không tìm  thấy chiến dịch có id '{0}'", query.Id]);
            }
            var campaignData = CampaignData.FromEntity(campaign);
            campaignData.EmailAttachmentFileList = new List<FileData>();            
            var files = (from f in EntitySet.Get<FileEntity>() 
                         where f.ReferenceObjectType == "Campaign" 
                         && f.ReferenceObjectId == campaign.Id 
                         select f);

            foreach (var file in files)
            {
                campaignData.EmailAttachmentFileList.Add(FileData.FromEntity(file));
            }

            campaignData.FileEditList = new List<FileData>();
            var editfiles = (from f in EntitySet.Get<FileEntity>()
                             where f.ReferenceObjectType == "Campaign.EditFiles"
                             && f.ReferenceObjectId == campaign.Id
                             select f);

            foreach (var editfile in editfiles)
            {
                campaignData.FileEditList.Add(FileData.FromEntity(editfile));
            }

            return new QueryResult<CampaignData>(campaignData);
        }
    }

    public class GetCampaignByProspectAssignmentQuery : QueryBase<CampaignData>
    {
        public Guid ProspectAssignmentId
        {
            get;
            set;
        }

        internal class Handler : QueryHandlerBase<GetCampaignByProspectAssignmentQuery, CampaignData>
        {
            public override QueryResult<CampaignData> Execute(GetCampaignByProspectAssignmentQuery query)
            {
                var result = from c in EntitySet.Get<CampaignEntity>()
                             join p in EntitySet.Get<ProspectEntity>() on c.Id equals p.CampaignId
                             join pa in EntitySet.Get<ProspectAssignmentEntity>() on p.Id equals pa.ProspectId
                             where pa.Id == query.ProspectAssignmentId
                             select c;
                return QueryResult.Create(result, CampaignData.FromEntity);
            }
        }
    }
}
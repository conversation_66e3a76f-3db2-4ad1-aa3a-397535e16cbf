﻿using System;
using System.Linq;
using Webaby;

namespace TinyCRM.Outbound.CallResult.Queries
{
    public class GetCallResultListQuery : QueryBase<CallResultData>
    {
        public Guid? ResultCodeSuiteId { get; set; }
    }

    internal class GetCallResultListQueryHandler : QueryHandlerBase<GetCallResultListQuery, CallResultData>
    {
        public override QueryResult<CallResultData> Execute(GetCallResultListQuery query)
        {
            var callResultQuery = EntitySet.Get<CallResultEntity>().Where(cr => cr.IsDeleted == false);

            if (query.ResultCodeSuiteId.IsNotNullOrEmpty())
            {
                callResultQuery = callResultQuery.Where(cr => cr.ResultCodeSuiteId == query.ResultCodeSuiteId);
            }

            callResultQuery = (from c in callResultQuery
                               orderby c.Code
                               select c);

            return QueryResult.Create(callResultQuery, CallResultData.FromEntity);
        }
    }
}
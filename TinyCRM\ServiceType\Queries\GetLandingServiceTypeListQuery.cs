﻿using System;
using System.Data;
using System.Data.SqlClient;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;

namespace TinyCRM.ServiceType.Queries
{
    public class GetLandingServiceTypeListQuery : QueryBase<LandingServiceTypeData>
    {
    }

    internal class GetLandingServiceTypeListQueryHandler : QueryHandlerBase<GetLandingServiceTypeListQuery, LandingServiceTypeData>
    {
        public GetLandingServiceTypeListQueryHandler(IEntitySet entitySet, IRepository repository, IText text, IMapper mapper)
            : base(entitySet, repository, text, mapper) { }

        public override async Task<QueryResult<LandingServiceTypeData>> ExecuteAsync(GetLandingServiceTypeListQuery query)
        {
            var cmd = EntitySet.CreateDbCommand();
            cmd.CommandText = "dbo.GetLandingServiceTypeList";
            cmd.CommandType = CommandType.StoredProcedure;
            var mainQuery = await EntitySet.ExecuteReadCommandAsync<LandingServiceTypeData>(cmd);
            return new QueryResult<LandingServiceTypeData>(mainQuery);
        }
    }
}
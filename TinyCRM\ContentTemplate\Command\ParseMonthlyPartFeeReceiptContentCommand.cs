﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text.RegularExpressions;
using TinyCRM.ContentTemplate.Queries;
using TinyCRM.FeeCategory;
using TinyCRM.MonthlyPartFee.Events;
using TinyCRM.Survey;
using Webaby;
using Webaby.Data;
using Microsoft.Data.SqlClient;

namespace TinyCRM.ContentTemplate.Command
{
    public class ParseMonthlyPartFeeReceiptContentCommand : CommandBase
    {
        public StaticContentTemplate ContentTemplate { get; set; }

        public List<Guid> KeyList { get; set; }
        public string StoredProcedureHandle { get; set; }

        public Action<string, string> ResultCallbackHandleFunc { get; set; }
    }

    internal class ParseMonthlyPartFeeReceiptContentCommandHandler : CommandHandlerBase<ParseMonthlyPartFeeReceiptContentCommand>
    {
        public ParseMonthlyPartFeeReceiptContentCommandHandler(IServiceProvider serviceProvider, ILogger<ParseMonthlyPartFeeReceiptContentCommandHandler> logger, IConfiguration configuration) : base(serviceProvider)
        {
            _logger = logger;
        }

        public string ContentTemplateLinkEntityParams { get { return _configuration.GetValue<string>("content.template.link.entity.params"); } }

        IConfiguration _configuration { get; set; }
        ILogger<ParseMonthlyPartFeeReceiptContentCommandHandler> _logger { get; set; }

        public override async Task ExecuteAsync(ParseMonthlyPartFeeReceiptContentCommand command)
        {
            string titleResult = command.ContentTemplate.Title;
            string contentResult = command.ContentTemplate.ContentTemplate;

            string feeCategoryTemplate = command.ContentTemplate.Title;

            var paramPattern = new Regex(@"{{(?'link'[^{}]*)}}").Matches(string.Format("{0} {1}", string.Join(" ", command.ContentTemplate.Title), string.Join(" ", command.ContentTemplate.ContentTemplate)));
            List<string> paramListInContent = new List<string>();
            foreach (var item in paramPattern)
            {
                if (item.ToString() == "{{link}}")
                {
                    continue;
                }
                paramListInContent.Add(item.ToString());
            }
            paramListInContent = paramListInContent.Distinct().ToList();
            var paramList = paramListInContent.Select(x => x).ToList();

            var setting = ContentTemplateLinkEntityParams;
            var genericSetting = string.IsNullOrEmpty(setting) ? new List<ContentTemplateLinkEntityParams>() : JsonConvert.DeserializeObject<List<ContentTemplateLinkEntityParams>>(setting);
            var contentTemplateLinkEntityParams = genericSetting.SelectMany(x => x.Fields).ToList();
            var dynamicFormParamSet = (await QueryExecutor.ExecuteAsync(new GetDynamicFormContentTemplateLinkEntityParamsQuery())).Many.ToList();
            contentTemplateLinkEntityParams.AddRange(dynamicFormParamSet.SelectMany(x => x.Fields).ToList());
            var paramListDetail = paramList.Distinct().Select(x =>
            {
                x = x.Trim('{', '}');
                var linkObj = contentTemplateLinkEntityParams.FirstOrDefault(xi => xi.DisplayName == x);
                if (linkObj == null)
                {
                    var idx = x.LastIndexOf('.');
                    if (idx == -1)
                    {
                        return null;
                    }
                    linkObj = new ContentTemplateLinkEntityParams.Field
                    {
                        DisplayName = x,
                        RootEntity = x.Substring(0, idx),
                        RootField = x.Substring(idx + 1)
                    };
                }
                return new
                {
                    Text = "{{" + x + "}}",
                    Table = linkObj.RootEntity,
                    Field = linkObj.RootField
                };
            }).Where(x => x != null);
            DataTable stringstringParams = new DataTable("StringString");
            stringstringParams.Columns.Add("Value1", typeof(string));
            stringstringParams.Columns.Add("Value2", typeof(string));
            paramListDetail.ToList().ForEach(x => stringstringParams.Rows.Add(x.Table, x.Field));

            var cmd = new SqlCommand();
            cmd.Parameters.AddRange(new[]
            {
                    DbParameterHelper.NewIdListParameter("@KeyList", command.KeyList),
                    new SqlParameter("@ParamSet", SqlDbType.Structured)
                    {
                        Value = stringstringParams
                    }
                });

            cmd.CommandText = command.StoredProcedureHandle;
            cmd.CommandType = CommandType.StoredProcedure;
            var data = (await EntitySet.ExecuteReadCommandAsync(cmd)).Tables[0];

            foreach (DataRow dr in data.Rows)
            {
                paramListDetail.Where(x => paramListInContent.Contains(x.Text)).ToList().ForEach(m =>
                {
                    titleResult = titleResult.Replace(m.Text, dr.GetString(m.Table + "." + m.Field).Trim());
                    contentResult = contentResult.Replace(m.Text, dr.GetString(m.Table + "." + m.Field).Trim());
                });

                command.ResultCallbackHandleFunc.Invoke(titleResult, contentResult);
            }
        }
    }
}
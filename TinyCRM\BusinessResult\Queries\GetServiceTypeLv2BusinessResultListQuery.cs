﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using TinyCRM.TaskType;
using Webaby;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.BusinessResult.Queries
{
    public class GetServiceTypeLv2BusinessResultListQuery : QueryBase<ViewModelResult>
    {
        public Guid TaskTypeId { get; set; }
        public Guid Id { get; set; }
    }

    public class ViewModelResult
    {
        public Guid TaskTypeId { get; set; }
        public List<Lve2Result> BusinessResultList { get; set; }
        public Guid NodeParentId { get; set; }
    }

    public class Lve2Result
    {
        public Guid Id { get; set; }
        public string Name { get; set; }
        public Guid BusinessResultId { get; set; }
        public bool ischeck { get; set; }
    }

    public class GetServiceTypeLv2BusinessResultListQueryHandler : QueryHandlerBase<GetServiceTypeLv2BusinessResultListQuery, ViewModelResult>
    {
        public GetServiceTypeLv2BusinessResultListQueryHandler(IServiceProvider serviceProvider)
            : base(serviceProvider) { }

        public override async Task<QueryResult<ViewModelResult>> ExecuteAsync(GetServiceTypeLv2BusinessResultListQuery query)
        {
            var result = new ViewModelResult();
            result.TaskTypeId = query.TaskTypeId;
            result.NodeParentId = query.Id;
            result.BusinessResultList = new List<Lve2Result>();
            var allBr = (await EntitySet.GetAsync<BusinessResultEntity>()).ToList();
            var allRef = (await EntitySet.GetAsync<BusinessResultReferenceEntity>()).ToList();
            var entityList = (from br in allBr
                              join ttbr in allRef on br.Id equals ttbr.BusinessResultId
                              where ttbr.ReferenceObjectId == query.TaskTypeId && br.ParentId == query.Id
                              orderby br.DisplayOrder
                              select br).ToList();
            var getallnodechild = (from br in allBr
                                   where br.ParentId == query.Id
                                   orderby br.DisplayOrder
                                   select br).ToList();
            foreach(var item in getallnodechild)
            {
                var model = new Lve2Result();
                model.Id = item.Id;
                model.Name = item.Name;
                model.ischeck = entityList.Any(x => x.Id == item.Id);
                result.BusinessResultList.Add(model);
            }

            return new QueryResult<ViewModelResult>(result);
        }
    }
}
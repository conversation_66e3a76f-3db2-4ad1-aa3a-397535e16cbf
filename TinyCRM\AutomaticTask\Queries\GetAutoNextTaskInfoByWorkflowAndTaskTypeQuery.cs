﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;

namespace TinyCRM.AutomaticTask.Queries
{
    public class GetAutoNextTaskInfoByWorkflowAndTaskTypeQuery : QueryBase<AutoNextTaskInfo>
    {
        public Guid WorkflowId { get; set; }
        public Guid TasktypeId { get; set; }
    }

    internal class GetAutoNextTaskInfoByWorkflowAndTaskTypeQueryHandler : QueryHandlerBase<GetAutoNextTaskInfoByWorkflowAndTaskTypeQuery, AutoNextTaskInfo>
    {
        public GetAutoNextTaskInfoByWorkflowAndTaskTypeQueryHandler(IServiceProvider serviceProvider)
            : base(serviceProvider) { }

        public override async Task<QueryResult<AutoNextTaskInfo>> ExecuteAsync(GetAutoNextTaskInfoByWorkflowAndTaskTypeQuery query)
        {
            var cmd = EntitySet.CreateDbCommand();
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@workflowId", query.WorkflowId));
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@tasktypeId", query.TasktypeId));
            cmd.CommandText = "dbo.GetAutoNextTaskInfoByWorkflowAndTaskType";
            cmd.CommandType = CommandType.StoredProcedure;
            var mainQuery = await EntitySet.ExecuteReadCommandAsync<AutoNextTaskInfo>(cmd);
            return new QueryResult<AutoNextTaskInfo>(mainQuery);
        }
    }
}

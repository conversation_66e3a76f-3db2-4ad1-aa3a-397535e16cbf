﻿using Microsoft.EntityFrameworkCore;
using System.Linq;
using System.Threading.Tasks;
using Webaby;

namespace TinyCRM.Customer.Queries
{
    public class GetCustomerFieldConfigurationByFunctionNameQuery : QueryBase<CustomerFieldConfigurationData>
    {
        public string FunctionName { get; set; }
    }

    internal class GetCustomerFieldConfigurationByFunctionNameQueryHandler : QueryHandlerBase<GetCustomerFieldConfigurationByFunctionNameQuery, CustomerFieldConfigurationData>
    {
        public GetCustomerFieldConfigurationByFunctionNameQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<CustomerFieldConfigurationData>> ExecuteAsync(GetCustomerFieldConfigurationByFunctionNameQuery query)
        {
            var customerFieldConfigurationEntities = await (from cusConfig in EntitySet.Get<CustomerFieldConfigurationEntity>()
                                                     where cusConfig.FunctionName == query.FunctionName
                                                     select cusConfig).ToListAsync();
            var mapper = Mapper.Map<List<CustomerFieldConfigurationData>>(customerFieldConfigurationEntities);
            return new QueryResult<CustomerFieldConfigurationData>(mapper);            
        }
    }
}

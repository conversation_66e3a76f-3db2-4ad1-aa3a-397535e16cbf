﻿using Webaby.Data;
using AutoMapper;
using Webaby.Localization;
using Webaby;
using System;
using System.Linq;
using System.Threading.Tasks;
using TinyCRM.ServiceCategory;

namespace TinyCRM.ServiceType.Queries
{
    public class GetLandingServiceTypeByServiceTypeIdQuery : QueryBase<LandingServiceTypeData>
    {
        public Guid ServiceTypeId { get; set; }
    }

    internal class GetLandingServiceTypeByServiceTypeIdQueryHandler : QueryHandlerBase<GetLandingServiceTypeByServiceTypeIdQuery, LandingServiceTypeData>
    {
        public GetLandingServiceTypeByServiceTypeIdQueryHandler(IEntitySet entitySet, IRepository repository, IText text, IMapper mapper)
            : base(entitySet, repository, text, mapper) { }

        public override async Task<QueryResult<LandingServiceTypeData>> ExecuteAsync(GetLandingServiceTypeByServiceTypeIdQuery query)
        {
            var stList = await EntitySet.GetAsync<ServiceTypeEntity>();
            var lstList = await EntitySet.GetAsync<LandingServiceTypeEntity>();
            var catList = await EntitySet.GetAsync<ServiceCategoryEntity>();
            var result = from st in stList
                         join lst in lstList on st.Id equals lst.ServiceTypeId
                         join l1 in catList on st.Level1Id equals l1.Id
                         join l2 in catList on st.Level2Id equals l2.Id into _l2
                         from l2 in _l2.DefaultIfEmpty()
                         join l3 in catList on st.Level3Id equals l3.Id into _l3
                         from l3 in _l3.DefaultIfEmpty()
                         join l4 in catList on st.Level4Id equals l4.Id into _l4
                         from l4 in _l4.DefaultIfEmpty()
                         where st.Id == query.ServiceTypeId
                         select new LandingServiceTypeData
                         {
                             Id = lst.Id,
                             DisplayName = lst.DisplayName,
                             DisplayOrder = lst.DisplayOrder,
                             ServiceTypeId = st.Id,
                             Level1Id = l1.Id,
                             Level1Name = l1.Name,
                             Level1Order = l1.Order,
                             Level2Id = l2 != null ? l2.Id : (Guid?)null,
                             Level2Name = l2 != null ? l2.Name : null,
                             Level2Order = l2 != null ? l2.Order : (int?)null,
                             Level3Id = l3 != null ? l3.Id : (Guid?)null,
                             Level3Name = l3 != null ? l3.Name : null,
                             Level3Order = l3 != null ? l3.Order : (int?)null,
                             Level4Id = l4 != null ? l4.Id : (Guid?)null,
                             Level4Name = l4 != null ? l4.Name : null,
                             Level4Order = l4 != null ? l4.Order : (int?)null
                         };
            return QueryResult.Create(result);
        }
    }
}
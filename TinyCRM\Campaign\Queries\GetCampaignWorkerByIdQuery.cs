﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;

namespace TinyCRM.Campaign.Queries
{
    public class GetCampaignWorkerByIdQuery : QueryBase<CampaignWorkerData>
    {
        public Guid Id { get; set; }

        public GetCampaignWorkerByIdQuery(Guid id)
        {
            Id = id;
        }
    }

    internal class GetCampaignWorkerByIdQueryHandler : QueryHandlerBase<GetCampaignWorkerByIdQuery, CampaignWorkerData>
    {
        public GetCampaignWorkerByIdQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<CampaignWorkerData>> ExecuteAsync(GetCampaignWorkerByIdQuery query)
        {
            var workerQuery = await EntitySet.GetAsync<CampaignWorkerEntity>();
            var result = workerQuery.FirstOrDefault(x => x.Id == query.Id);
            return new QueryResult<CampaignWorkerData>(Mapper.Map<CampaignWorkerData>(result));
        }
    }
}

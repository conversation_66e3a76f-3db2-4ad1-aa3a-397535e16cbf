﻿using AutoMapper;
using Microsoft.Extensions.DependencyInjection;
using System;
using Webaby;
using Webaby.Data;
using Webaby.Localization;
using Webaby.Security;
using Webaby.Web;

namespace TinyCRM.CustomerContext.Events
{
    public class CustomerContextEvent : Event, IServerEvent
    {
        private readonly IText _text;
        public IServiceProvider ServiceProvider { get; set; }
        public CustomerContextEvent(IServiceProvider serviceProvider)
        {

            ServiceProvider = serviceProvider;
            _text = serviceProvider.GetRequiredService<IText>();
        }

        public string Token { get; set; }

        public string Title { get; set; }

        public string Content { get; set; }

        public CustomerContextType ContextType { get; set; }

        public ContextFrom ContextFrom { get; set; }

        public DateTime CreatedDate { get; set; }

        public string DisplayCreatedDate
        {
            get
            {
                if (CreatedDate.Date == DateTime.Now.Date)
                {
                    return _text["Hôm nay, {0}", CreatedDate.ToString("HH:mm")];
                }
                if (CreatedDate.Date == DateTime.Now.Date.AddDays(-1))
                {
                    return _text["Hôm qua, {0}", CreatedDate.ToString("HH:mm")];
                }
                return CreatedDate.ToString("dd/MM/yyyy HH:mm");
            }
        }

        public string CreatedByName { get; set; }
    }
}
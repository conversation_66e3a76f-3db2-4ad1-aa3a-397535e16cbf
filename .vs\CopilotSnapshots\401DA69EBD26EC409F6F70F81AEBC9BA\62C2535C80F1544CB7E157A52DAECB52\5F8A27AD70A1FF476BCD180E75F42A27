﻿using System;
using System.ComponentModel.Composition;
using System.Threading;
using System.Web;
using System.Web.Mvc;
using TinyCRM.RequestTicket.Commands;
using TinyCRM.Survey;
using Webaby;

namespace TinyCRM.RequestTicket.Events
{
    public class RequestTicketClosedEvent : Event
    {
        public Guid RequestTicketId
        {
            get; set;
        }

        public Guid CustomerId
        {
            get; set;
        }

        public Guid ServiceTypeId
        {
            get; set;
        }

        public Guid CreatedBy
        {
            get; set;
        }

        public Guid? OwnerId
        {
            get; set;
        }

        public Guid? OwnedByOrganizationId
        {
            get; set;
        }

        public DateTime CreatedDate
        {
            get; set;
        }

        public string PhoneNumber
        {
            get; set;
        }

        public string Email
        {
            get; set;
        }

        public Guid? TicketBusinessResultId { get; set; }
    }

    public class RequestTicketClosedEventHandler : IEventHandler<RequestTicketClosedEvent>
    {
        [Import]
        public ICommandExecutor CommandExecutor { get; set; }

        [Import]
        public ILogger Logger { get; set; }

        public void Handle(RequestTicketClosedEvent @event)
        {
            if (@event.CustomerId != Guid.Empty)
            {
                var createTicketAutomaticSurveyFeedbacksCommand = new CreateTicketAutomaticSurveyFeedbacksCommand
                {
                    RequestTicketId = @event.RequestTicketId,
                    CustomerId = @event.CustomerId,
                    ServiceTypeId = @event.ServiceTypeId,
                    CreatedBy = @event.CreatedBy,
                    OwnerId = @event.OwnerId,
                    OwnedByOrganizationId = @event.OwnedByOrganizationId,
                    CreatedDate = @event.CreatedDate,
                    PhoneNumber = @event.PhoneNumber,
                    Email = @event.Email,
                    SurveyEvent = SurveyEvent.TicketClosed
                };

                var thread = new Thread(() =>
                {
                    try
                    {
                        CommandExecutor.Execute(createTicketAutomaticSurveyFeedbacksCommand);
                    }
                    catch (Exception ex)
                    {
                        Logger.Error(ex.Message);
                    }
                });

                thread.Start();
            }
        }
    }
}
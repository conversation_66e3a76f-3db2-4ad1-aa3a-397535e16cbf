﻿using System;
using System.Data;
using System.Data.SqlClient;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;

namespace TinyCRM.ServiceType.Queries
{
    public class GetServiceTypeByDynamicFieldNameQuery : QueryBase<ServiceTypeAndDynamicFieldData>
    {
        public string DynamicFieldDefinitionName { get; set; }

        public string ExcludeServiceType { get; set; }

        public string Condition { get; set; }
    }

    internal class GetServiceTypeByDynamicFieldNameQueryHandler : QueryHandlerBase<GetServiceTypeByDynamicFieldNameQuery, ServiceTypeAndDynamicFieldData>
    {
        public GetServiceTypeByDynamicFieldNameQueryHandler(IEntitySet entitySet, IRepository repository, IText text, IMapper mapper)
            : base(entitySet, repository, text, mapper) { }

        public override async Task<QueryResult<ServiceTypeAndDynamicFieldData>> ExecuteAsync(GetServiceTypeByDynamicFieldNameQuery query)
        {
            var cmd = EntitySet.CreateDbCommand();
            DbParameterHelper.AddNullableString(cmd, "@DynamicFieldDefinitionName", query.DynamicFieldDefinitionName);
            DbParameterHelper.AddNullableString(cmd, "@ExcludeServiceType", query.ExcludeServiceType);
            DbParameterHelper.AddNullableString(cmd, "@Condition", query.Condition);
            cmd.CommandText = "dbo.GetServiceTypeByDynamicFieldName";
            cmd.CommandType = CommandType.StoredProcedure;
            var mainQuery = await EntitySet.ExecuteReadCommandAsync<ServiceTypeAndDynamicFieldData>(cmd);
            return new QueryResult<ServiceTypeAndDynamicFieldData>(mainQuery);
        }
    }
}

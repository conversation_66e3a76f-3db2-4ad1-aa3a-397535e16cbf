﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Webaby.Data;
using Webaby.Localization;
using Webaby;
using System;
using System.Linq;
using System.Threading.Tasks;
using Webaby.Core.DynamicForm;
using Webaby.Core.File;

namespace TinyCRM.Outbound.Campaign.Queries
{
    public class GetCampaignByDynamicFormIdQuery : QueryBase<CampaignData>
    {
        public Guid DynamicFormId { get; set; }
    }

    internal class GetCampaignByDynamicFormIdQueryHandler : QueryHandlerBase<GetCampaignByDynamicFormIdQuery, CampaignData>
    {
        public GetCampaignByDynamicFormIdQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<CampaignData>> ExecuteAsync(GetCampaignByDynamicFormIdQuery query)
        {
            var campaign = await EntitySet.Get<CampaignEntity>().Where(x => x.DynamicFormId == query.DynamicFormId).FirstOrDefaultAsync();
            var campaignData = Mapper.Map<CampaignData>(campaign);
            return new QueryResult<CampaignData>(campaignData);
        }
    }
}
﻿using System;
using Webaby;

namespace TinyCRM.ContentTemplate.Command
{
    public class DeleteAlternativeNotiChannelContentTemplateCommand : CommandBase
    {
        public Guid Id { get; set; }
    }

    internal class DeleteAlternativeNotiChannelContentTemplateCommandHandler : CommandHandlerBase<DeleteAlternativeNotiChannelContentTemplateCommand>
    {
        public DeleteAlternativeNotiChannelContentTemplateCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }
        public override async Task ExecuteAsync(DeleteAlternativeNotiChannelContentTemplateCommand command)
        {
            await Repository.DeleteAsync<AlternativeNotiChannelContentTemplateEntity>(command.Id);
        }
    }
}

﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using TinyCRM.ExpenseItem;
using Webaby;
using Webaby.Core.DynamicForm;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.ServiceType.Queries
{
    public class ExpenseItemLinkServiceTypeQuery : QueryBase<EntityLinkServiceType>
    {

    }

    public class EntityLinkServiceType
    {
        public Guid ServiceTypeId { get; set; }
        public string ContestName { get; set; }
        public string ContestCode { get; set; }
        public bool IsCompleted { get; set; }
    }

    internal class ExpenseItemLinkServiceTypeQueryHandler : QueryHandlerBase<ExpenseItemLinkServiceTypeQuery, EntityLinkServiceType>
    {
        public ExpenseItemLinkServiceTypeQueryHandler(IEntitySet entitySet, IRepository repository, IText text, IMapper mapper)
            : base(entitySet, repository, text, mapper) { }

        public override async Task<QueryResult<EntityLinkServiceType>> ExecuteAsync(ExpenseItemLinkServiceTypeQuery query)
        {
            var cmd = EntitySet.CreateDbCommand();
            cmd.CommandText = "fwd.SearchExpenseItemLinkServiceType";
            cmd.CommandType = CommandType.StoredProcedure;
            var mainQuery = await EntitySet.ExecuteReadCommandAsync<EntityLinkServiceType>(cmd);
            return new QueryResult<EntityLinkServiceType>(mainQuery);
        }
    }
}

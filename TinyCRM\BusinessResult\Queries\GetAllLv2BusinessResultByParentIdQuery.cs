﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using TinyCRM.TaskType;
using Webaby;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.BusinessResult.Queries
{
    public class GetAllLv2BusinessResultByParentIdQuery : QueryBase<BusinessResultData>
    {
        public Guid ParentId { get; set; }
    }

    public class GetAllLv2BusinessResultByParentIdQueryHandler : QueryHandlerBase<GetAllLv2BusinessResultByParentIdQuery, BusinessResultData>
    {
        public GetAllLv2BusinessResultByParentIdQueryHandler(IServiceProvider serviceProvider)
            : base(serviceProvider) { }

        public override async Task<QueryResult<BusinessResultData>> ExecuteAsync(GetAllLv2BusinessResultByParentIdQuery query)
        {
            var entityList = (await EntitySet.GetAsync<BusinessResultEntity>())
                .Where(br => br.ParentId == query.ParentId)
                .OrderBy(br => br.DisplayOrder);
            return QueryResult.Create(entityList, Mapper.Map<BusinessResultData>);
        }
    }
}
﻿using Webaby.Data;
using AutoMapper;
using Webaby.Localization;
using Webaby;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace TinyCRM.Customer.Queries
{
    public class GetCustomerByFacebookIdQuery : QueryBase<CustomerData>
    {
        public GetCustomerByFacebookIdQuery(string facebookId)
        {
            FacebookId = facebookId;
        }

        public string FacebookId { get; private set; }
    }

    internal class GetCustomerByFacebookIdQueryHandler : QueryHandlerBase<GetCustomerByFacebookIdQuery, CustomerData>
    {
        public GetCustomerByFacebookIdQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<CustomerData>> ExecuteAsync(GetCustomerByFacebookIdQuery query)
        {
            var cmd = EntitySet.CreateDbCommand();
            cmd.Parameters.Add(DbParameterHelper.AddNullableString(cmd, "@facebookId", query.FacebookId));
            cmd.CommandText = "GetCustomerByFacebookId";
            cmd.CommandType = System.Data.CommandType.StoredProcedure;
            var mainQuery = await EntitySet.ExecuteReadCommandAsync<CustomerData>(cmd);
            return new QueryResult<CustomerData>(mainQuery);
        }
    }
}

﻿using System;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;

namespace TinyCRM.Customer.Commands
{
    public class DeleteCustomerAlternativeAddressCommand : CommandBase
    {
        public Guid Id { get; set; }
    }

    internal class DeleteCustomerAlternativeAddressCommandHandler :
        CommandHandlerBase<DeleteCustomerAlternativeAddressCommand>
    {
        public DeleteCustomerAlternativeAddressCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task ExecuteAsync(DeleteCustomerAlternativeAddressCommand command)
        {
            var entity = await EntitySet.GetAsync<CustomerAlternativeAddressEntity>(command.Id);
            if (entity == null)
            {
                throw new InvalidOperationException(T["Không tìm thấy thông tin đổi sản phẩm"]);
            }
            await Repository.DeleteAsync(entity);
        }
    }
}

﻿using System;
using System.Data.SqlClient;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;

namespace TinyCRM.ContentTemplate.Queries
{
    public class SearchAutoCompleteContentTemplateQuery : QueryBase<AutoCompleteContentTemplateData>
    {
        public Guid? ReferenceObjectId { get; set; }

        public Guid? UserId { get; set; }

        public string QueryText { get; set; }

        public bool IncludedPublic { get; set; }
    }

    internal class SearchAutoCompleteContentTemplateQueryHandler : QueryHandlerBase<SearchAutoCompleteContentTemplateQuery, AutoCompleteContentTemplateData>
    {
        public SearchAutoCompleteContentTemplateQueryHandler(IServiceProvider serviceProvider)
            : base(serviceProvider) { }

        public override async Task<QueryResult<AutoCompleteContentTemplateData>> ExecuteAsync(SearchAutoCompleteContentTemplateQuery query)
        {
            int startRow = 0;
            int endRow = 0;
            if (query.Pagination == null)
            {
                startRow = 0;
                endRow = int.MaxValue;
            }
            else
            {
                startRow = query.Pagination.Index * query.Pagination.Size + 1;
                endRow = query.Pagination.Index * query.Pagination.Size + query.Pagination.Size;
            }
            string searchText = string.Empty;
            if (query.QueryText.IsNotNullOrEmpty())
            {
                searchText = query.QueryText.Trim().Replace(" ", "%");
            }
            var cmd = EntitySet.CreateDbCommand();
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@ReferenceObjectId", query.ReferenceObjectId));
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@UserId", query.UserId));
            cmd.Parameters.Add(DbParameterHelper.AddNullableString(cmd, "@QueryText", searchText));
            cmd.Parameters.Add(DbParameterHelper.NewNullableBooleanParameter(cmd, "@IncludedPublic", query.IncludedPublic));
            cmd.Parameters.Add(DbParameterHelper.AddNullableInt(cmd, "@StartRow", startRow));
            cmd.Parameters.Add(DbParameterHelper.AddNullableInt(cmd, "@EndRow", endRow));

            cmd.CommandText = "SearchAutoCompleteContentTemplate";
            cmd.CommandType = System.Data.CommandType.StoredProcedure;
            var mainQuery = await EntitySet.ExecuteReadCommandAsync<AutoCompleteContentTemplateData>(cmd);
            return new QueryResult<AutoCompleteContentTemplateData>(mainQuery);
        }
    }
}

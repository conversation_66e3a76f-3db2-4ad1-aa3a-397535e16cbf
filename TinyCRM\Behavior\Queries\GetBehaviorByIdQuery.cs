﻿using System;
using System.Threading.Tasks;
using AutoMapper;
using Webaby;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.Behavior.Queries
{
    public class GetBehaviorByIdQuery : QueryBase<BehaviorData>
    {
        public Guid Id { get; set; }
    }

    internal class GetBehaviorByIdQueryHandler : QueryHandlerBase<GetBehaviorByIdQuery, BehaviorData>
    {
        public GetBehaviorByIdQueryHandler(IServiceProvider serviceProvider)
            : base(serviceProvider) { }

        public override async Task<QueryResult<BehaviorData>> ExecuteAsync(GetBehaviorByIdQuery query)
        {
            var behaviorEntity = await EntitySet.GetAsync<BehaviorEntity>(query.Id);
            return new QueryResult<BehaviorData>(Mapper.Map<BehaviorData>(behaviorEntity));
        }
    }
}

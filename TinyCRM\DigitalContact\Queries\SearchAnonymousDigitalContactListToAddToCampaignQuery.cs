﻿using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using Webaby.Data;
using Webaby;
using System.Data;
using AutoMapper;
using Webaby.Localization;

namespace TinyCRM.DigitalContact.Queries
{
    public class SearchAnonymousDigitalContactListToAddToCampaignQuery : QueryBase<DigitalContactListItem>
    {
        public Guid CampaignId { get; set; }

        public bool? NotInCampaign { get; set; }

        public Guid? SelectedCampaignId { get; set; }

        public List<Guid> ResultCodeIds { get; set; }

        public List<Guid> ExcludeCampaignIds { get; set; }

        public string UID { get; set; }

        public Guid? DigitalContactTypeId { get; set; }

        public Guid? ImportSessionId { get; set; }
    }

    public class SearchAnonymousDigitalContactListToAddToCampaignQueryHandler : QueryHandlerBase<SearchAnonymousDigitalContactListToAddToCampaignQuery, DigitalContactListItem>
    {
        public SearchAnonymousDigitalContactListToAddToCampaignQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<DigitalContactListItem>> ExecuteAsync(SearchAnonymousDigitalContactListToAddToCampaignQuery query)
        {
            int startRow = query.Pagination.Index * query.Pagination.Size + 1;
            int endRow = query.Pagination.Index * query.Pagination.Size + query.Pagination.Size;
            var cmd = EntitySet.CreateDbCommand();
            cmd.Parameters.AddRange(new[]
            {
                DbParameterHelper.AddNullableGuid(cmd, "@CampaignId", query.CampaignId),
                DbParameterHelper.NewNullableBooleanParameter(cmd, "@NotInCampaign", query.NotInCampaign),
                DbParameterHelper.AddNullableString(cmd, "@UID", query.UID),
                DbParameterHelper.AddNullableGuid(cmd, "@SelectedCampaignId", query.SelectedCampaignId, true),
                DbParameterHelper.NewIdListParameter("@ResultCodeIds", query.ResultCodeIds),
                DbParameterHelper.NewIdListParameter("@ExcludeCampaignIds", query.ExcludeCampaignIds),
                DbParameterHelper.AddNullableGuid(cmd, "@ImportSessionId", query.ImportSessionId),
                DbParameterHelper.AddNullableGuid(cmd, "@DigitalContactTypeId", query.DigitalContactTypeId),
                DbParameterHelper.AddNullableInt(cmd, "@StartRow", startRow),
                DbParameterHelper.AddNullableInt(cmd, "@EndRow", endRow),
            });
            cmd.CommandText = "dbo.DigitalContact_SearchAnonymousDigitalContactToAddToCampaign";
            cmd.CommandType = System.Data.CommandType.StoredProcedure;
            var contactQuery = await EntitySet.ExecuteReadCommandAsync<DigitalContactListItem>(cmd);
            return new QueryResult<DigitalContactListItem>(contactQuery);
        }
    }
}
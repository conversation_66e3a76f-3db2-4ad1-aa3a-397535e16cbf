﻿using AutoMapper;
using System;
using Webaby;
using Webaby.Data;

namespace TinyCRM.Campaign.Queries
{
    public class AgentCampaignListItem
    {
        public Guid CampaignId { get; set; }

        public string CampaignName { get; set; }

        public int TotalAssigned { get; set; }

        public int TotalNew { get; set; }

        public int TotalPending { get; set; }

        public int TotalDone { get; set; }

        public int TotalCount { get; set; }
    }
}
﻿using Webaby.Data;
using AutoMapper;
using Webaby.Localization;
using Webaby;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.Linq;
using TinyCRM.ServiceCategory;
using TinyCRM.ServiceType;
using TinyCRM.ServiceType.Queries;
using TinyCRM.TaskType;

namespace TinyCRM.BusinessResult.Queries
{
    public class GetListDependOnBusinessResultQuery : QueryBase<ListResultData>
    {
        public Guid BusinessResultId { get; set; }
    }
    public class ListConvertData
    {
        public ServiceTypeEntity ServiceType { get; set; }
        public ServiceCategoryEntity Level1 { get; set; }
        public ServiceCategoryEntity Level2 { get; set; }
        public ServiceCategoryEntity Level3 { get; set; }
        public ServiceCategoryEntity Level4 { get; set; }
    }

    public class ListResultData
    {
        public string Name { get; set; }
        public Guid Id { get; set; }
        public string ResultType { get; set; }
    }
    internal class GetListDependOnBusinessResultQueryHandler : QueryHandlerBase<GetListDependOnBusinessResultQuery, ListResultData>
    {
        public GetListDependOnBusinessResultQueryHandler(IServiceProvider serviceProvider)
            : base(serviceProvider) { }

        public override async Task<QueryResult<ListResultData>> ExecuteAsync(GetListDependOnBusinessResultQuery query)
        {
            List<Guid> listguid = new List<Guid>();
            var data = (await EntitySet.GetAsync<BusinessResultReferenceEntity>()).Where(ttbr => ttbr.BusinessResultId == query.BusinessResultId).ToList();
            listguid.AddRange(data.Select(x => x.ReferenceObjectId));

            var allServiceTypes = (await EntitySet.GetAsync<ServiceTypeEntity>()).ToList();
            var allLevel1 = (await EntitySet.GetAsync<ServiceCategoryEntity>()).ToList();
            var allLevel2 = allLevel1;
            var allLevel3 = allLevel1;
            var allLevel4 = allLevel1;

            var tempQuery = (from st in allServiceTypes
                             join lv1 in allLevel1 on st.Level1Id equals lv1.Id
                             join _lv2 in allLevel2 on st.Level2Id equals _lv2.Id into _tempLevel2
                             from lv2 in _tempLevel2.DefaultIfEmpty()
                             join _lv3 in allLevel3 on st.Level3Id equals _lv3.Id into _tempLevel3
                             from lv3 in _tempLevel3.DefaultIfEmpty()
                             join _lv4 in allLevel4 on st.Level4Id equals _lv4.Id into _tempLevel4
                             from lv4 in _tempLevel4.DefaultIfEmpty()
                             select new ListConvertData { ServiceType = st, Level1 = lv1, Level2 = lv2, Level3 = lv3, Level4 = lv4 }).Where(x => listguid.Contains(x.ServiceType.Id)).ToList();
            listguid.RemoveAll(x => tempQuery.Select(s => s.ServiceType.Id).Contains(x));
            List<ServiceTypeData> serviceType = tempQuery.Select(x =>
            {
                ServiceTypeData serviceTypeData = Mapper.Map<ServiceTypeData>(x.ServiceType);

                serviceTypeData.Level1Name = x.Level1.Name;
                serviceTypeData.Level1Order = x.Level1.Order;

                if (x.Level2 != null)
                {
                    serviceTypeData.Level2Name = x.Level2.Name;
                    serviceTypeData.Level2Order = x.Level2.Order;
                }
                if (x.Level3 != null)
                {
                    serviceTypeData.Level3Name = x.Level3.Name;
                    serviceTypeData.Level3Order = x.Level3.Order;
                }
                if (x.Level4 != null)
                {
                    serviceTypeData.Level4Name = x.Level4.Name;
                    serviceTypeData.Level4Order = x.Level4.Order;
                }

                return serviceTypeData;

            }).ToList();
            var listresults = new List<ListResultData>();

            foreach(var item in serviceType)
            {
                var result = new ListResultData();
                result.Name = item.Level1Name + "/" + item.Level2Name + "/" + item.Level3Name + "/" + item.Level4Name;
                result.Id = item.Id;
                result.ResultType = "ServiceType";
                listresults.Add(result);
            }

            var taskType = (await EntitySet.GetAsync<TaskTypeEntity>()).Where(x => listguid.Contains(x.Id));
            foreach (var item_temp2 in taskType)
            {
                var result_temp2 = new ListResultData();
                result_temp2.Name = item_temp2.TaskType;
                result_temp2.Id = item_temp2.Id;
                result_temp2.ResultType = "TaskType";
                listresults.Add(result_temp2);
            }

            return new QueryResult<ListResultData>(listresults);
        }
    }
}
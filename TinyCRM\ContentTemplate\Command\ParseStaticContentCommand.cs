﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Microsoft.Data.SqlClient;
using System.Data;
using System.Data.SqlClient;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using TinyCRM.AutomaticTask.Queries;
using TinyCRM.ContentTemplate.Queries;
using TinyCRM.DynamicDefinedTable.Queries;
using TinyCRM.Phase.Queries;
using TinyCRM.RequestTicket.Queries;
using TinyCRM.ServiceType.Queries;
using TinyCRM.Survey;
using TinyCRM.TaskType.Queries;
using TinyCRM.Workflow.Queries;
using Webaby;
using Webaby.Core.DynamicForm.Queries;
using Webaby.Data;
//using Xipton.Razor;

namespace TinyCRM.ContentTemplate.Command
{
    public class ParseStaticContentCommand : CommandBase
    {
        public Action<List<string>, List<string>, DataRow, List<Guid>> ParseStaticContentHandleFunc { get; set; }

        /// <summary>
        /// <PERSON><PERSON> thể null
        /// </summary>
        public Func<string, string, DataRow, List<Guid>, string, string> StaticContentParserFunc { get; set; }

        public List<StaticContentTemplate> StaticContentTemplates { get; set; }

        public List<Guid> KeyList { get; set; }

        /// <summary>
        /// Có thể thêm vào những param mà trong template content không có
        /// Vẫn giữ cấu trúc {{...}}
        /// </summary>
        public List<string> AdditionalParams { get; set; }

        /// <summary>
        /// 1 type trong app setting của key ContentTemplateLinkEntityParams
        /// có thể null, sử dụng để mapping display param
        /// </summary>
        public string LinkEntityParamsType { get; set; }

        public string StoredProcedureHandle { get; set; }
    }

    internal class ParseStaticContentCommandHandler : CommandHandlerBase<ParseStaticContentCommand>
    {
    
        public ParseStaticContentCommandHandler(IServiceProvider serviceProvider, ILogger<ParseStaticContentCommandHandler> logger, IConfiguration configuration, ICommandExecutor commandExecutor) : base(serviceProvider)
        {
            _logger = logger; _configuration = configuration;
        }

        public string ContentTemplateLinkEntityParams { get { return _configuration.GetValue<string>("content.template.link.entity.params"); } }

        IConfiguration _configuration { get; set; }
        ILogger<ParseStaticContentCommandHandler> _logger { get; set; }
        
        public string EditableContentTemplateLinkEntityParams { get { return _configuration.GetValue<string>("editablecontent.template.link.entity.params"); } }        


        public override async Task ExecuteAsync(ParseStaticContentCommand command)
        {
            //var titleTemplate = command.StaticContentTemplates.Select(sct => sct.Title).ToList();
            //var contentTemplate = command.StaticContentTemplates.Select(sct => sct.ContentTemplate).ToList();
            //double? user_TimezoneOffset = null;
            //if((_configuration.GetValue<string>("user.timezone.offset")).IsNotNullOrEmpty())
            //{
            //    user_TimezoneOffset = double.Parse(_configuration.GetValue<string>("user.timezone.offset"));
            //}

            //#region DisplayOnly Params

            //var paramPattern = new Regex(@"{{(?'link'[^{}]*)}}").Matches(string.Format("{0} {1}", string.Join(" ", titleTemplate), string.Join(" ", contentTemplate)));
            //List<string> paramListInContent = new List<string>();
            //foreach (var item in paramPattern)
            //{
            //    if (item.ToString() == "{{link}}")
            //    {
            //        continue;
            //    }
            //    paramListInContent.Add(item.ToString());
            //}
            //paramListInContent = paramListInContent.Distinct().ToList();
            //var paramList = paramListInContent.Select(x => x).ToList();
            //if (command.AdditionalParams != null)
            //{
            //    paramList.AddRange(command.AdditionalParams);
            //}

            //var setting = ContentTemplateLinkEntityParams;
            //var genericSetting = string.IsNullOrEmpty(setting) ? new List<ContentTemplateLinkEntityParams>() : JsonConvert.DeserializeObject<List<ContentTemplateLinkEntityParams>>(setting);
            //List<ContentTemplateLinkEntityParams.Field> contentTemplateLinkEntityParams = genericSetting.SelectMany(x => x.Fields).ToList();
            //var dynamicFormParamSet = QueryExecutor.Execute(new GetDynamicFormContentTemplateLinkEntityParamsQuery()).Many.ToList();
            //contentTemplateLinkEntityParams.AddRange(dynamicFormParamSet.SelectMany(x => x.Fields).ToList());
            //var paramListDetail = paramList.Distinct().Select(x =>
            //{
            //    x = x.Trim('{', '}');
            //    if (x.Contains("$$hide:"))
            //    {
            //        var removeIndex = x.LastIndexOf("$$hide:");
            //        x = x.Substring(0, removeIndex).Trim();
            //    }
            //    var linkObj = contentTemplateLinkEntityParams.FirstOrDefault(xi => xi.DisplayName == x);
            //    if (linkObj == null)
            //    {
            //        if (x.Contains(" &gt;&gt; ") || x.Contains(" >> "))
            //        {
            //            //Sample: dynamic field value là 1 Entity (Id của entity), value cần lấy sẽ là 1 value cụ thể trong entity đó
            //            //{{Phiếu - Đề nghị thanh toán : Dự án (Project) >> dbo.RequestTicket#Code#0}}
            //            //{{Phiếu - Đề nghị thanh toán : Dự án (Project) >> dbo.RequestTicket#Project#1}}
            //            var first = x.Split(new[] { " &gt;&gt; ", " >> " } , StringSplitOptions.None)[0];
            //            var last = x.Split(new[] { " &gt;&gt; ", " >> " }, StringSplitOptions.None)[1];
            //            var table = "TicketDynamicField";
            //            var entityLinkName = contentTemplateLinkEntityParams.FirstOrDefault(xi => xi.DisplayName == first)?.RootField;
            //            if (entityLinkName == null || last.Split('#').Length != 3 || last.Split('#')[2] != "0" && last.Split('#')[2] != "1")
            //            {
            //                return null;
            //            }
            //            var entityLinkTable = last.Split('#')[0];
            //            var entityLinkField = last.Split('#')[1];
            //            var isDynamicField = last.Split('#')[2];
            //            var alias = entityLinkName + "#" + entityLinkField + "#" + isDynamicField;
            //            linkObj = new ContentTemplateLinkEntityParams.Field
            //            {
            //                DisplayName = x,
            //                RootEntity = table,
            //                RootField = JsonConvert.SerializeObject(new
            //                {
            //                    EntityLinkName = entityLinkName,
            //                    EntityLinkTable = entityLinkTable,
            //                    EntityLinkField = entityLinkField,
            //                    IsDynamicField = isDynamicField,
            //                    Alias = alias
            //                })
            //            };
            //        }
            //        else
            //        {
            //            var idx = x.LastIndexOf('.');
            //            if (idx == -1)
            //            {
            //                return null;
            //            }
            //            linkObj = new ContentTemplateLinkEntityParams.Field
            //            {
            //                DisplayName = x,
            //                RootEntity = x.Substring(0, idx),
            //                RootField = x.Substring(idx + 1)
            //            };
            //        }
            //    }
            //    return new
            //    {
            //        Text = "{{" + x + "}}",
            //        Table = linkObj.RootEntity,
            //        Field = linkObj.RootField,
            //        ViewHint = linkObj.ViewHint
            //    };
            //}).Where(x => x != null);

            //#endregion

            //DataTable stringstringParams = new DataTable("StringString");
            //stringstringParams.Columns.Add("Value1", typeof(string));
            //stringstringParams.Columns.Add("Value2", typeof(string));
            //paramListDetail.ToList().ForEach(x => stringstringParams.Rows.Add(x.Table, x.Field));

            //#region Editable Params

            //var editableParamPattern = new Regex(@"@@(.*?)@@").Matches(string.Format("{0}", string.Join(" ", contentTemplate)));
            //List<string> editableParamListInContent = new List<string>();
            //foreach (var item in editableParamPattern)
            //{
            //    editableParamListInContent.Add(item.ToString());
            //}
            //editableParamListInContent = editableParamListInContent.Distinct().ToList();
            //var editableParamList = editableParamListInContent.Select(x => x).ToList();

            //var editableGenericSetting = string.IsNullOrEmpty(EditableContentTemplateLinkEntityParams) ? new List<ContentTemplateLinkEntityParams>() : JsonConvert.DeserializeObject<List<ContentTemplateLinkEntityParams>>(EditableContentTemplateLinkEntityParams);
            //var editableContentTemplateLinkEntityParams = editableGenericSetting.SelectMany(x => x.Fields).ToList();
            //editableContentTemplateLinkEntityParams.AddRange(dynamicFormParamSet.SelectMany(x => x.Fields).ToList());
            //var editableParamListDetail = editableParamList.Distinct().Select(x =>
            //{
            //    x = x.Trim('@', '@');
            //    var linkObj = editableContentTemplateLinkEntityParams.FirstOrDefault(xi => xi.DisplayName == x);
            //    if (linkObj == null)
            //    {
            //        var idx = x.LastIndexOf('.');
            //        if (idx == -1)
            //        {
            //            return null;
            //        }
            //        linkObj = new ContentTemplateLinkEntityParams.Field
            //        {
            //            DisplayName = x,
            //            RootEntity = x.Substring(0, idx),
            //            RootField = x.Substring(idx + 1)
            //        };
            //    }
            //    return new
            //    {
            //        Text = "@@" + x + "@@",
            //        Table = linkObj.RootEntity,
            //        Field = linkObj.RootField
            //    };
            //}).Where(x => x != null);

            //#endregion

            //editableParamListDetail.ToList().ForEach(x => stringstringParams.Rows.Add(x.Table, x.Field));


            //var cmd = EntitySet.CreateDbCommand();
            //cmd.Parameters.AddRange(new[]
            //{
            //    DbParameterHelper.NewIdListParameter("@KeyList", command.KeyList),
            //    new SqlParameter("@ParamSet", SqlDbType.Structured)
            //    {
            //        Value = stringstringParams
            //    }
            //});

            //cmd.CommandText = command.StoredProcedureHandle;
            //cmd.CommandType = CommandType.StoredProcedure;
            //var data = (await EntitySet.ExecuteReadCommandAsync(cmd)).Tables[0];
            //var fileList = new List<Guid>();

            //foreach (DataRow dr in data.Rows)
            //{
            //    string keyId = dr["KeyId"].ToString();

            //    var titles = titleTemplate.Select(tmp =>
            //    {
            //        var title = tmp ?? "";
            //        paramListDetail.Where(x => paramListInContent.Contains(x.Text)).ToList().ForEach(m =>
            //        {
            //            string val = null;
            //            if (m.Text.Contains(" &gt;&gt; ") || m.Text.Contains(" >> "))
            //            {
            //                var colName = JsonConvert.DeserializeAnonymousType(m.Field, new { Alias = "" })?.Alias;
            //                if (colName.IsNotNullOrEmpty())
            //                {
            //                    val = dr["TicketDynamicField." + colName].ToString();
            //                }
            //            }
            //            else
            //            {
            //                val = command.StaticContentParserFunc != null ? command.StaticContentParserFunc.Invoke(m.Table, m.Field, dr, fileList, "") : dr.GetString(m.Table + "." + m.Field);
            //            }
            //            var isNumeric = decimal.TryParse(val, out decimal n);
            //            val = isNumeric ? n.ToStringWithDelimiter() : val.Trim();
            //            if(m.Table != "TicketDynamicField")
            //            {
            //                //chỉ checkDateTime với trường hợp Date là string và của requestticket - có typecolumn là DateTime
            //                int number;
            //                bool isNumber = int.TryParse(val, out number);
            //                if (!isNumeric)
            //                {
            //                    var isDateTime = DateTime.TryParse(val, out DateTime dt);
            //                    if (isDateTime && user_TimezoneOffset.HasValue)
            //                    {
            //                        val = string.Format("{0}", dt.ToDateTimeOffsetValue(user_TimezoneOffset.Value).ToString("dd/MM/yyyy HH:mm:ss"));
            //                    }
            //                }
            //            }
            //            title = title.Replace(m.Text, val.Trim());
            //        });
            //        return title;
            //    }).ToList();
            //    var contents = contentTemplate.Select(tmp =>
            //    {
            //        var content = tmp ?? "";

            //        foreach (var paramContent in paramListInContent)
            //        {
            //            var rootData = paramContent;
            //            if (paramContent.Contains("$$hide:"))
            //            {
            //                var removeIndex = paramContent.LastIndexOf("$$hide:");
            //                rootData = paramContent.Substring(0, removeIndex).Trim() + "}}";
            //            }
            //            paramListDetail.Where(x => x.Text == rootData).ToList().ForEach(m =>
            //            {
            //                string val = "";
            //                if (m.Text.Contains(" &gt;&gt; ") || m.Text.Contains(" >> "))
            //                {
            //                    var colName = JsonConvert.DeserializeAnonymousType(m.Field, new { Alias = "" })?.Alias;
            //                    if (colName.IsNotNullOrEmpty())
            //                    {
            //                        val = dr["TicketDynamicField." + colName].ToString();
            //                    }
            //                }
            //                else if (m.Text == "{{WorkflowTable}}")
            //                {
            //                    if (Guid.TryParse(dr["dbo.RequestTicket.Id"].ToString(), out var ticketId))
            //                    {
            //                        var ticket = QueryExecutor.Execute(new GetRequestTicketByIdQuery(ticketId)).One;
            //                        ServiceTypeData service = null;
            //                        if (ticket != null)
            //                        {
            //                            service = QueryExecutor.ExecuteOne(new GetServiceTypeByIdQuery(ticket.ServiceTypeId));
            //                        }
            //                        List<TaskTypeData> taskTypeList = new List<TaskTypeData>();
            //                        if (service.WorkflowId.HasValue)
            //                        {
            //                            taskTypeList = QueryExecutor.ExecuteMany(new GetTaskTypeListByWorkflowQuery { WorkflowId = service.WorkflowId.Value }).ToList();
            //                            var isAuto = QueryExecutor.ExecuteOne(new GetWorkflowByIdQuery { Id = service.WorkflowId.Value }).WorkflowType > 0 ? true : false;
            //                            if (isAuto)
            //                            {

            //                                foreach (var item in taskTypeList)
            //                                {
            //                                    var autoNextTasks = QueryExecutor.ExecuteMany(new GetAutoNextTaskByWorkflowAndTaskTypeQuery { WorkflowId = service.WorkflowId.Value, TaskTypeId = item.Id }).ToList();
            //                                    item.hasMultiCondition = autoNextTasks.Count() > 1 ? true : false;
            //                                    item.WorkflowId = service.WorkflowId;
            //                                }
            //                            }
            //                        }
            //                        var phase = QueryExecutor.Execute(new GetPhaseDataQuery { TicketId = ticketId });

            //                        RazorMachine rm = new RazorMachine();
            //                        var template = rm.ExecuteUrl("~/RazorTemplate/PhaseListDetail", new PhaseListData
            //                        {
            //                            PhaseList = phase.Many,
            //                            TaskTypeList = taskTypeList,
            //                            ParentTaskId = null,
            //                            WorkflowId = service.WorkflowId,
            //                            IsEmailView = true,
            //                            TimeZoneOffset = user_TimezoneOffset
            //                        }, null, true);
            //                        val = template.Result;
            //                    }
            //                }
            //                else if (m.Text.StartsWith("{{UserDefinedTable"))
            //                {
            //                    var startindex = paramContent.IndexOf("/") + 1;
            //                    var endindex = paramContent.IndexOf("-") - startindex;
            //                    var nameColumn = "TicketDynamicField." + paramContent.Substring(startindex, endindex);
            //                    if (Guid.TryParse(dr[nameColumn].ToString(), out var tableValueId))
            //                    {
            //                        var recordDataList = Container.One<IQueryExecutor>().Execute(new GetDynamicDefinedTableCellValueListByDynamicFieldValueIdQuery { DynamicFieldValueId = tableValueId }).Many.ToList();
            //                        var groupCol = recordDataList.GroupBy(x => x.DynamicDefinedTableColumnId);
            //                        var tempList = new List<DynamicDefinedTableColumnData>();
            //                        foreach (var col in groupCol)
            //                        {
            //                            var columnId = col.Key;
            //                            var infoCol = Container.One<IQueryExecutor>().ExecuteOne(new GetDynamicDefinedTableColumnByIdQuery { DynamicDefinedTableColumnId = columnId });
            //                            tempList.Add(infoCol);
            //                        }
            //                        var colsOrder = tempList.OrderBy(x => x.ColumnOrder).ToList();
            //                        var hideCols = new List<string>();
            //                        if (paramContent.Contains("$$hide:"))
            //                        {
            //                            var startIndex = paramContent.LastIndexOf("$$hide:") + 7;
            //                            var endIndex = paramContent.LastIndexOf("}}");
            //                            hideCols = paramContent.Substring(startIndex, endIndex - startIndex).Split(',').ToList();
            //                        }
            //                        if (hideCols != null && hideCols.Count > 0)
            //                        {
            //                            colsOrder = colsOrder.Where(x => !hideCols.Any(y => y == x.Name)).ToList();
            //                        }
            //                        var rowData = recordDataList.GroupBy(x => x.RowNumber);
            //                        var listCell = recordDataList;
            //                        foreach (var row in rowData)
            //                        {
            //                            foreach (var col in colsOrder)
            //                            {
            //                                var cellData = row.FirstOrDefault(x => x.DynamicDefinedTableColumnId == col.Id).Value;
            //                                var cellId = row.FirstOrDefault(x => x.DynamicDefinedTableColumnId == col.Id).Id.Value;
            //                                if (col.DataType == "TinyCRM.DynamicForm.Queries.EditorControlDynamicFormData" && cellData.IsNotNullOrEmpty())
            //                                {
            //                                    var dynamicFormId = Guid.Parse(cellData);
            //                                    var dynamicform = Container.One<IQueryExecutor>().ExecuteOne(new GetDynamicFormByIdQuery { Id = dynamicFormId });
            //                                    listCell.FirstOrDefault(x => x.Id == cellId).Value = dynamicform.Name;
            //                                }
            //                            }
            //                        }
            //                        var renderData = new RenderDynamicTableModel { ListCellValue = listCell, ListColumn = colsOrder };
            //                        renderData.TimeZoneOffset = user_TimezoneOffset;
            //                        RazorMachine rm = new RazorMachine();
            //                        var template = rm.ExecuteUrl("~/RazorTemplate/DynamicDefinedTable", renderData, null, true);
            //                        val = template.Result;
            //                    }
            //                }
            //                else
            //                {
            //                    var hidenCols = string.Empty;
            //                    if (paramContent.Contains("$$hide:"))
            //                    {
            //                        var startIndex = paramContent.LastIndexOf("$$hide:") + 7;
            //                        var endIndex = paramContent.LastIndexOf("}}");
            //                        hidenCols = paramContent.Substring(startIndex, endIndex - startIndex);
            //                    }
            //                    val = command.StaticContentParserFunc != null ? command.StaticContentParserFunc.Invoke(m.Table, m.Field, dr, fileList, hidenCols) : dr.GetString(m.Table + "." + m.Field);
            //                }
            //                var isNumeric = decimal.TryParse(val, out decimal n);                            
            //                val = isNumeric ? n.ToStringWithDelimiter() : val.Trim();
            //                if (m.Table != "TicketDynamicField")
            //                {
            //                    //chỉ checkDateTime với trường hợp Date là string và của requestticket - có typecolumn là DateTime
            //                    int number;
            //                    bool isNumber = int.TryParse(val, out number);
            //                    if (!isNumeric)
            //                    {
            //                        var isDateTime = DateTime.TryParse(val, out DateTime dt);
            //                        if (isDateTime && user_TimezoneOffset.HasValue)
            //                        {
            //                            val = string.Format("{0}", dt.ToDateTimeOffsetValue(user_TimezoneOffset.Value).ToString("dd/MM/yyyy HH:mm:ss"));
            //                        }
            //                    }
            //                }

            //                if (m.ViewHint != "Attachment")
            //                {
            //                    content = content.Replace(paramContent, val.Trim());
            //                }
            //                else
            //                {
            //                    content = content.Replace(paramContent, string.Empty);
            //                    if (val.IsNotNullOrEmpty())
            //                    {
            //                        string[] fileIdStr = val.Split(';');
            //                        fileList.AddRange(fileIdStr.Select(x => Guid.Parse(x)));
            //                    }
            //                }
            //            });
            //        }

            //        editableParamListDetail.Where(x => editableParamListInContent.Contains(x.Text)).ToList().ForEach(m =>
            //        {
            //            string val = command.StaticContentParserFunc != null ? command.StaticContentParserFunc.Invoke(m.Table, m.Field, dr, fileList, "") : dr.GetString(m.Table + "." + m.Field);
            //            string valContainer = string.Format("<table style=\"border-collapse: collapse;\"><tbody><tr><td style=\"border: 1px solid #f4f5f8; padding: 5px 5px 5px 10px; text-align: right\">{5}:</td><td name=\"{1}___{2}___{3}___{4}\" style=\"border: 1px solid #f4f5f8; padding: 5px; width: 200px\"></td></tr></tbody></table>", val.Trim(), command.LinkEntityParamsType, keyId, m.Table, m.Field, m.Text.Trim('@'));
            //            content = content.Replace(m.Text, valContainer);
            //        });

            //        return content;
            //    }).ToList();
            //    command.ParseStaticContentHandleFunc.Invoke(titles, contents, dr, fileList);
            //}
        }
    }

    public class StaticContentTemplate
    {
        public string Title { get; set; }

        public string ContentTemplate { get; set; }
    }
}
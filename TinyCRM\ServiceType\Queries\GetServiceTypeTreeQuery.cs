﻿using System;
using System.Data;
using System.Data.SqlClient;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;

namespace TinyCRM.ServiceType.Queries
{
    public class GetServiceTypeTreeQuery : QueryBase<ServiceTypeTreeData>
    {
        public Guid? OrganizationId { get; set; }
    }

    internal class GetServiceTypeTreeQueryHandler : QueryHandlerBase<GetServiceTypeTreeQuery, ServiceTypeTreeData>
    {
        public GetServiceTypeTreeQueryHandler(IEntitySet entitySet, IRepository repository, IText text, IMapper mapper)
            : base(entitySet, repository, text, mapper) { }

        public override async Task<QueryResult<ServiceTypeTreeData>> ExecuteAsync(GetServiceTypeTreeQuery query)
        {
            var cmd = EntitySet.CreateDbCommand();
            DbParameterHelper.AddNullableGuid(cmd, "@organizationId", query.OrganizationId);
            cmd.CommandText = "dbo.GetServiceTypeTree";
            cmd.CommandType = CommandType.StoredProcedure;
            var mainQuery = await EntitySet.ExecuteReadCommandAsync<ServiceTypeTreeData>(cmd);
            return new QueryResult<ServiceTypeTreeData>(mainQuery);
        }
    }
}

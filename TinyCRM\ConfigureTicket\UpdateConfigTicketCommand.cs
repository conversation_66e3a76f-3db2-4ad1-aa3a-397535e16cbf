﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Webaby;
using Webaby.BusinessSetting;
using Webaby.Core.BusinessSettings;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.ConfigureTicket
{
    public class UpdateConfigTicketCommand : CommandBase
    {
        public string Data { get; set; }
        public string Key { get; set; }

        internal class Handler : CommandHandlerBase<UpdateConfigTicketCommand>
        {
            public Handler(IServiceProvider serviceProvider) : base(serviceProvider) { }
            public override async Task ExecuteAsync(UpdateConfigTicketCommand command)
            {
                var result = await EntitySet.Get<BusinessSettingEntity>().Where(x => x.ImportKey == command.Key).ToListAsync();

                var InsertEntity = new BusinessSettingEntity();
                InsertEntity.Id = Guid.NewGuid();
                InsertEntity.ImportKey = command.Key;
                InsertEntity.Value = command.Data;
                InsertEntity.DataType = "System.String";
                InsertEntity.IsReadOnly = true;

                if (result.Count() > 0)
                {
                    var entity = result.ToList()[0];
                    entity.Value = command.Data;
                    await Repository.SaveAsync(entity);
                }

                else if (command.Key == "editticket.nonecustomer.modelpropertylistview")
                {
                    InsertEntity.Name = "EditTicket.NoneCustomer.DynamicModelPropertyList";
                    InsertEntity.DisplayName = "Cấu hình UI Chi Tiết Phiếu - Chỉnh sửa Phiếu Nội Bộ";
                    InsertEntity.Order = 109;
                    await Repository.SaveAsync(InsertEntity);
                }
                else if (command.Key == "createticket.nonecustomer.modelpropertylistview")
                {
                    InsertEntity.Name = "CreateTicket.NoneCustomer.DynamicModelPropertyList";
                    InsertEntity.DisplayName = "Cấu hình UI Chi Tiết Phiếu - Tạo Phiếu Nội Bộ";
                    InsertEntity.Order = 110;
                    await Repository.SaveAsync(InsertEntity);
                }
                else if (command.Key == "editticket.modelpropertylistview")
                {
                    InsertEntity.Name = "Editticket.ModelPropertyList";
                    InsertEntity.DisplayName = "Cấu hình UI Chi Tiết Phiếu - Chỉnh sửa Phiếu Có Khách Hàng";
                    InsertEntity.Order = 111;
                    await Repository.SaveAsync(InsertEntity);
                }
                else if (command.Key == "createticket.modelpropertylistview")
                {
                    InsertEntity.Name = "EditTicket.NoneCustomer.DynamicModelPropertyList";
                    InsertEntity.DisplayName = "Cấu hình UI Chi Tiết Phiếu - Tạo Phiếu Có Khách Hàng";
                    InsertEntity.Order = 112;
                    await Repository.SaveAsync(InsertEntity);
                }
            }
        }
    }
}

﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TinyCRM.TaskType;
using TinyCRM.Workflow;
using Webaby;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;

namespace TinyCRM.BusinessResult.Commands
{
    public class DeleteWorkflowBusinessResultCommand : CommandBase
    {
        public Guid WorkflowId { get; set; }

        public Guid BusinessResultId { get; set; }
    }

    internal class DeleteWorkflowBusinessResultCommandHandler : CommandHandlerBase<DeleteWorkflowBusinessResultCommand>
    {
        public DeleteWorkflowBusinessResultCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task ExecuteAsync(DeleteWorkflowBusinessResultCommand command)
        {
            BusinessResultReferenceEntity workflowBusinessResultEntity = EntitySet.Get<BusinessResultReferenceEntity>().Where(ttbr => ttbr.ReferenceObjectId == command.WorkflowId && ttbr.BusinessResultId == command.BusinessResultId).SingleOrDefault();
            if (workflowBusinessResultEntity != null)
            {
                await Repository.DeleteAsync(workflowBusinessResultEntity);
            }
        }
    }
}
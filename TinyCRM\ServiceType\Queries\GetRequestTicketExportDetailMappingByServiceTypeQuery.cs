﻿using Webaby.Data;
using AutoMapper;
using Webaby.Localization;
using Webaby;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace TinyCRM.ServiceType.Queries
{
    public class GetRequestTicketExportDetailMappingByServiceTypeQuery : QueryBase<RequestTicketExportDetailMappingData>
    {
        public Guid ServiceTypeId { get; set; }
    }

    internal class GetRequestTicketExportDetailMappingByServiceTypeQueryHandler : QueryHandlerBase<GetRequestTicketExportDetailMappingByServiceTypeQuery, RequestTicketExportDetailMappingData>
    {
        public GetRequestTicketExportDetailMappingByServiceTypeQueryHandler(IEntitySet entitySet, IRepository repository, IText text, IMapper mapper)
            : base(entitySet, repository, text, mapper) { }

        public override async Task<QueryResult<RequestTicketExportDetailMappingData>> ExecuteAsync(GetRequestTicketExportDetailMappingByServiceTypeQuery query)
        {
            var r = (await EntitySet.GetAsync<RequestTicketExportDetailMappingEntity>()).Where(st => st.ServiceTypeId == query.ServiceTypeId);
            return QueryResult.Create(r, query.Pagination, Mapper.Map<RequestTicketExportDetailMappingData>);
        }
    }
}
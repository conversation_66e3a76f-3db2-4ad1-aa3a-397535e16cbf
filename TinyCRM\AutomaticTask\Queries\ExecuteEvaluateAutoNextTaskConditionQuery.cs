﻿using AutoMapper;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;
using Webaby.Localization;
using Webaby.Security;

namespace TinyCRM.AutomaticTask.Queries
{
    public class ExecuteEvaluateAutoNextTaskConditionQuery : QueryBase<bool>
    {
        public string ConditionContent { get; set; }
    }

    internal class ExecuteEvaluateAutoNextTaskConditionQueryHandler : QueryHandlerBase<ExecuteEvaluateAutoNextTaskConditionQuery, bool>
    {
        public ExecuteEvaluateAutoNextTaskConditionQueryHandler(IServiceProvider serviceProvider)
            : base(serviceProvider) { }
        public override async Task<QueryResult<bool>> ExecuteAsync(ExecuteEvaluateAutoNextTaskConditionQuery query)
        {
            var cmd = EntitySet.CreateDbCommand(); 
            cmd.Parameters.AddRange(new[]
            {
                DbParameterHelper.AddNullableString(cmd, "@ConditionContent", query.ConditionContent.IsNullOrEmpty() ? string.Empty : query.ConditionContent)
            });

            cmd.CommandText = "dbo.ExecuteEvaluateAutoNextTaskCondition";
            cmd.CommandType = CommandType.StoredProcedure;
            var mainQuery = await EntitySet.ExecuteReadCommandAsync<bool>(cmd);
            return new QueryResult<bool>(mainQuery);
        }
    }
}

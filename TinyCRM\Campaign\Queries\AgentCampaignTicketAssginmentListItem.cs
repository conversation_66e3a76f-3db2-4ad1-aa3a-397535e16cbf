﻿using AutoMapper;
using System;
using TinyCRM.Enums;
using Webaby;
using Webaby.Data;

namespace TinyCRM.Campaign.Queries
{
    public class AgentCampaignTicketAssginmentListItem
    {
        public Guid CampaignAssignmentId { get; set; }

        public Guid CampaignWorkId { get; set; }

        public long Ma<PERSON>hieu { get; set; }

        public string MaPE { get; set; }

        public string TenNguoiYeuCau { get; set; }

        public string DienThoaiNguoiYeuCau { get; set; }

        public string LoaiDichVu { get; set; }

        public DateTime CreatedDate { get; set; }

        public string CreatedByName { get; set; }

        public string TicketStatus { get; set; }

        public AssignmentStatus AssignmentStatus { get; set; }

        public string AssignmentTag { get; set; }

        public string OrganizationName { get; set; }

        public Guid? SurveyFeedbackId { get; set; }

        public int TotalCount { get; set; }
    }
}
﻿using System;
using System.Linq;
using Webaby;

namespace TinyCRM.Outbound.Brand.Queries
{
    public class GetParentBrandListByCompanyQuery : QueryBase<BrandData>
    {
        public Guid CompanyId
        {
            get;
            set;
        }
    }

    internal class GetParentBrandListByCompanyQueryHandler : QueryHandlerBase<GetParentBrandListByCompanyQuery, BrandData>
    {
        public override QueryResult<BrandData> Execute(GetParentBrandListByCompanyQuery query)
        {
            var brandQuery = EntitySet.Get<BrandEntity>();
            brandQuery = brandQuery.Where(b => b.ParentBrandId.HasValue == false && b.CompanyId == query.CompanyId);

            return QueryResult.Create(brandQuery, BrandData.FromEntity);
        }
    }
}
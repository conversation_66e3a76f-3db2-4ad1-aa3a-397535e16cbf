﻿using System;
using System.Data;
using System.Data.SqlClient;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;

namespace TinyCRM.Sms.Queries
{
    public class GetNewSmsListQuery : QueryBase<SmsData>
    {
        public DateTime GetDateTime { get; set; }
    }

    internal class GetNewSmsListQueryHandler : QueryHandlerBase<GetNewSmsListQuery, SmsData>
    {
        public GetNewSmsListQueryHandler(IEntitySet entitySet, IRepository repository, IText text, IMapper mapper)
            : base(entitySet, repository, text, mapper) { }

        public override async Task<QueryResult<SmsData>> ExecuteAsync(GetNewSmsListQuery query)
        {
            var cmd = EntitySet.CreateDbCommand();
            cmd.CommandText = @"SELECT	*
                                FROM	dbo.Sms s WITH(NOLOCK)
                                WHERE	s.Status <> 2 AND s.Status <> 4
                                        AND s.Deleted = 0
		                                AND s.SentDate <= @GetDateTime";
            cmd.CommandType = CommandType.Text;
            DbParameterHelper.NewNullableDateTimeParameter(cmd, "@GetDateTime", query.GetDateTime);

            var mainQuery = await EntitySet.ExecuteReadCommandAsync<SmsData>(cmd);
            return new QueryResult<SmsData>(mainQuery);
        }
    }
}

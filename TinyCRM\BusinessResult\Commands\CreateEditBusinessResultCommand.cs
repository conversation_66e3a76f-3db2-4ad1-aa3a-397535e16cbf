﻿using AutoMapper;
using System;
using System.Linq;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.BusinessResult.Commands
{
    public class CreateEditBusinessResultCommand : CommandBase
    {
        public Guid Id { get; set; }

        public string Code { get; set; }

        public string Name { get; set; }

        public int DisplayOrder { get; set; }
    }

    internal class CreateEditBusinessResultCommandHandler : CommandHandlerBase<CreateEditBusinessResultCommand>
    {
        public CreateEditBusinessResultCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task ExecuteAsync(CreateEditBusinessResultCommand command)
        {
            var entity = await EntitySet.GetAsync<BusinessResultEntity>(command.Id);
            if (entity == null)
            {
                entity = new BusinessResultEntity();
            }
            Mapper.Map(command, entity);
            await Repository.SaveAsync(entity);
        }
    }
}
﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;
using Webaby.Security;
using AutoMapper;
using Webaby.Localization;

namespace TinyCRM.BusinessPermission
{
    public class GetRoleBusinessPermissionListQuery : QueryBase<RoleBusinessPermissionInfo>
    {
        public List<Guid> BusinessPermissionList { get; set; }
    }

    internal class GetRoleBusinessPermissionListQueryHandler : QueryHandlerBase<GetRoleBusinessPermissionListQuery, RoleBusinessPermissionInfo>
    {
        public GetRoleBusinessPermissionListQueryHandler(IServiceProvider serviceProvider)
            : base(serviceProvider) { }

        public override async Task<QueryResult<RoleBusinessPermissionInfo>> ExecuteAsync(GetRoleBusinessPermissionListQuery query)
        {
            var bpList = await EntitySet.GetAsync<BusinessPermissionEntity>();
            var rbpList = await EntitySet.GetAsync<RoleBusinessPermissionEntity>();
            var rList = await EntitySet.GetAsync<AspNetRoleEntity>();
            var data = from bp in bpList
                       join rbp in rbpList on bp.Id equals rbp.BusinessPermissionId
                       join r in rList on rbp.RoleId equals r.Id
                       where query.BusinessPermissionList.Contains(bp.Id)
                       select new RoleBusinessPermissionInfo
                       {
                           Id = rbp.Id,
                           BusinessPermissionId = bp.Id,
                           BusinessPermission = bp.Name,
                           RoleId = r.Id,
                           Role = r.Name,
                       };
            return new QueryResult<RoleBusinessPermissionInfo>(data);
        }
    }

    public class RoleBusinessPermissionInfo
    {
        public Guid Id { get; set; }

        public Guid BusinessPermissionId { get; set; }

        public string BusinessPermission { get; set; }

        public Guid RoleId { get; set; }

        public string Role { get; set; }
    }
}
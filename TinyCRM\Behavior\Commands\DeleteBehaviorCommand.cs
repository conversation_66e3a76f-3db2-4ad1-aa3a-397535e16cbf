﻿using System;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;

namespace TinyCRM.Behavior.Commands
{
    public class DeleteBehaviorCommand : CommandBase
    {
        public Guid Id { get; set; }
    }

    internal class DeleteBehaviorCommandHandler : CommandHandlerBase<DeleteBehaviorCommand>
    {
        public DeleteBehaviorCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task ExecuteAsync(DeleteBehaviorCommand command)
        {
            var entity = await EntitySet.GetAsync<BehaviorEntity>(command.Id);
            if (entity != null)
            {
                await Repository.DeleteAsync(entity);
            }
        }
    }
}

﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Webaby;
using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.Outbound.Campaign.Queries
{
    public class GetAllCampaignParameterListQuery : QueryBase<CampaignParameterData>
    {
    }

    internal class GetAllCampaignParameterListQueryHandler : QueryHandlerBase<GetAllCampaignParameterListQuery, CampaignParameterData>
    {
        public GetAllCampaignParameterListQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override QueryResult<CampaignParameterData> Execute(GetAllCampaignParameterListQuery query)
        {
            var campaignParameterQuery = EntitySet.Get<CampaignParameterEntity>();
            return QueryResult.Create(campaignParameterQuery, CampaignParameterData.FromEntity);
        }
    }
}
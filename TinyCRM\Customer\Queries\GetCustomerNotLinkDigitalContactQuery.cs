﻿using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TinyCRM.DigitalContact;
using Webaby;

namespace TinyCRM.Customer.Queries
{
    public class GetCustomerNotLinkDigitalContactQuery : QueryBase<CustomerData>
    {
        public Guid DigitalContactTypeId { get; set; }
    }

    internal class GetCustomerNotLinkDigitalContactQueryHandler : QueryHandlerBase<GetCustomerNotLinkDigitalContactQuery, CustomerData>
    {
        public GetCustomerNotLinkDigitalContactQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<CustomerData>> ExecuteAsync(GetCustomerNotLinkDigitalContactQuery query)
        {
            var customerHasLink = (from cus in EntitySet.Get<CustomerEntity>()
                                   join dc in EntitySet.Get<DigitalContactEntity>() on cus.Id equals dc.CustomerId
                                   where dc.DigitalContactTypeId == query.DigitalContactTypeId && dc.CustomerId.HasValue
                                   select cus
                                );
            var allCustomer = EntitySet.Get<CustomerEntity>();

            var customersWithoutLink = await allCustomer.Where(c => !customerHasLink.Any(cl => cl.Id == c.Id)).ToListAsync();

            var mapper = Mapper.Map<List<CustomerData>>(customersWithoutLink);
            return new QueryResult<CustomerData>(mapper);            
        }
    }
}

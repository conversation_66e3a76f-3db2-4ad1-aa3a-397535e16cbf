﻿using AutoMapper;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text.RegularExpressions;
using TinyCRM.Survey;
using Webaby;
using Webaby.Data;
using Webaby.Localization;
using Microsoft.Data.SqlClient;

namespace TinyCRM.AutomaticTask.Command
{
    public class ParseAutoNextTaskUserPathSelectorCommand : CommandBase
    {
        public Guid ReferenceObjectId { get; set; }

        public string UserPathSelector { get; set; }

        public string LinkEntityParamsType { get; set; }

        public string StoredProcedureHandle { get; set; }

        /// <summary>
        /// Có thể thêm vào những param mà trong template content không có
        /// Vẫn giữ cấu trúc {{...}}
        /// </summary>
        public List<string> AdditionalParams { get; set; }

        public Action<string, DataRow> Handle { get; set; }
        public Func<string, string, DataRow, string> Parser { get; set; }
    }

    internal class ParseAutoNextTaskUserPathSelectorCommandHandler : CommandHandlerBase<ParseAutoNextTaskUserPathSelectorCommand>
    {
        public ParseAutoNextTaskUserPathSelectorCommandHandler(
            IServiceProvider serviceProvider,            
            ILogger<ParseAutoNextTaskUserPathSelectorCommandHandler> logger,
            IConfiguration configuration
        ) : base(serviceProvider) { _logger = logger; _configuration = configuration; }

        ILogger<ParseAutoNextTaskUserPathSelectorCommandHandler> _logger { get; set; }
        IConfiguration _configuration { get; set; }

        public string ContentTemplateLinkEntityParams { get { return _configuration.GetValue<string>("content.template.link.entity.params"); } }

        public override async Task ExecuteAsync(ParseAutoNextTaskUserPathSelectorCommand command)
        {
            try
            {
                var paramPattern = new Regex(@"{{(?'link'[^{}]*)}}").Matches(string.Join(" ", command.UserPathSelector));
                List<string> paramListInContent = new List<string>();
                foreach (var item in paramPattern)
                {
                    if (item.ToString() == "{{link}}")
                    {
                        continue;
                    }
                    paramListInContent.Add(item.ToString());
                }
                paramListInContent = paramListInContent.Distinct().ToList();
                var paramList = paramListInContent.Select(x => x).ToList();
                if (command.AdditionalParams != null)
                {
                    paramList.AddRange(command.AdditionalParams);
                }
                var setting = ContentTemplateLinkEntityParams;
                var genericSetting = string.IsNullOrEmpty(setting) ? new List<ContentTemplateLinkEntityParams>() : JsonConvert.DeserializeObject<List<ContentTemplateLinkEntityParams>>(setting);
                var contentTemplateLinkEntityParams = genericSetting.Where(x => x.Type == command.LinkEntityParamsType).SelectMany(x => x.Fields);
                var paramListDetail = paramList.Distinct().Select(x =>
                {
                    x = x.Trim('{', '}');
                    var linkObj = contentTemplateLinkEntityParams.FirstOrDefault(xi => xi.DisplayName == x);
                    if (linkObj == null)
                    {
                        var idx = x.LastIndexOf('.');
                        if (idx == -1)
                        {
                            return null;
                        }
                        linkObj = new ContentTemplateLinkEntityParams.Field
                        {
                            DisplayName = x,
                            RootEntity = x.Substring(0, idx),
                            RootField = x.Substring(idx + 1)
                        };
                    }
                    return new
                    {
                        Text = "{{" + x + "}}",
                        Table = linkObj.RootEntity,
                        Field = linkObj.RootField
                    };
                }).Where(x => x != null);
                DataTable stringstringParams = new DataTable("StringString");
                stringstringParams.Columns.Add("Value1", typeof(string));
                stringstringParams.Columns.Add("Value2", typeof(string));
                paramListDetail.ToList().ForEach(x => stringstringParams.Rows.Add(x.Table, x.Field));

                List<Guid> keyList = new List<Guid> { command.ReferenceObjectId };

                var cmd = EntitySet.CreateDbCommand();
                cmd.Parameters.AddRange(new[]
                {
                    DbParameterHelper.NewIdListParameter("@KeyList", keyList),
                    new SqlParameter("@ParamSet", SqlDbType.Structured)
                    {
                        Value = stringstringParams
                    }
                });

                cmd.CommandText = command.StoredProcedureHandle;
                cmd.CommandType = CommandType.StoredProcedure;
                var data = (await EntitySet.ExecuteReadCommandAsync(cmd)).Tables[0];

                foreach (DataRow dr in data.Rows)
                {
                    var userPathSelector = command.UserPathSelector ?? "";
                    paramListDetail.Where(x => paramListInContent.Contains(x.Text)).ToList().ForEach(m =>
                    {
                        string val = command.Parser != null ? command.Parser.Invoke(m.Table, m.Field, dr) : dr[m.Table + "." + m.Field].ToString();
                        userPathSelector = userPathSelector.Replace(m.Text, val.Trim());
                    });

                    command.Handle.Invoke(userPathSelector, dr);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(string.Format("Error ParseAutoNextTaskUserPathSelectorCommand: ReferenceObjectId: {0}. LinkEntityParamsType: {1}. StoredProcedureHandle: {2}. Error Message: {3}", command.ReferenceObjectId, command.LinkEntityParamsType, command.StoredProcedureHandle, ex.ToString()));
            }
        }
    }
}
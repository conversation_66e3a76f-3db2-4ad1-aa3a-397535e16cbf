{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\source\\Workspaces\\CEP_NETCORE\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{2FF02035-9624-4B9A-8C3E-19CCE8FA2219}|TinyCRM\\TinyCRM.csproj|c:\\users\\<USER>\\source\\workspaces\\cep_netcore\\tinycrm\\outbound\\contactcallresultservicecallback\\tasks\\callservicecallbackscheduledtask.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2FF02035-9624-4B9A-8C3E-19CCE8FA2219}|TinyCRM\\TinyCRM.csproj|solutionrelative:tinycrm\\outbound\\contactcallresultservicecallback\\tasks\\callservicecallbackscheduledtask.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2FF02035-9624-4B9A-8C3E-19CCE8FA2219}|TinyCRM\\TinyCRM.csproj|c:\\users\\<USER>\\source\\workspaces\\cep_netcore\\tinycrm\\requestticket\\events\\requestticketclosedevent.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2FF02035-9624-4B9A-8C3E-19CCE8FA2219}|TinyCRM\\TinyCRM.csproj|solutionrelative:tinycrm\\requestticket\\events\\requestticketclosedevent.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2FF02035-9624-4B9A-8C3E-19CCE8FA2219}|TinyCRM\\TinyCRM.csproj|c:\\users\\<USER>\\source\\workspaces\\cep_netcore\\tinycrm\\requestticket\\queries\\getclonerequestticketinfoquery.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2FF02035-9624-4B9A-8C3E-19CCE8FA2219}|TinyCRM\\TinyCRM.csproj|solutionrelative:tinycrm\\requestticket\\queries\\getclonerequestticketinfoquery.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2FF02035-9624-4B9A-8C3E-19CCE8FA2219}|TinyCRM\\TinyCRM.csproj|c:\\users\\<USER>\\source\\workspaces\\cep_netcore\\tinycrm\\requestticket\\commands\\insertproductrequestticketcommand.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2FF02035-9624-4B9A-8C3E-19CCE8FA2219}|TinyCRM\\TinyCRM.csproj|solutionrelative:tinycrm\\requestticket\\commands\\insertproductrequestticketcommand.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2FF02035-9624-4B9A-8C3E-19CCE8FA2219}|TinyCRM\\TinyCRM.csproj|c:\\users\\<USER>\\source\\workspaces\\cep_netcore\\tinycrm\\requestticket\\events\\requestticketdatasynchronizedevent.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2FF02035-9624-4B9A-8C3E-19CCE8FA2219}|TinyCRM\\TinyCRM.csproj|solutionrelative:tinycrm\\requestticket\\events\\requestticketdatasynchronizedevent.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2FF02035-9624-4B9A-8C3E-19CCE8FA2219}|TinyCRM\\TinyCRM.csproj|c:\\users\\<USER>\\source\\workspaces\\cep_netcore\\tinycrm\\requestticket\\commands\\changeticketownedbyorganizationidbatchcommand.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2FF02035-9624-4B9A-8C3E-19CCE8FA2219}|TinyCRM\\TinyCRM.csproj|solutionrelative:tinycrm\\requestticket\\commands\\changeticketownedbyorganizationidbatchcommand.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2FF02035-9624-4B9A-8C3E-19CCE8FA2219}|TinyCRM\\TinyCRM.csproj|c:\\users\\<USER>\\source\\workspaces\\cep_netcore\\tinycrm\\requestticket\\commands\\batchinsertnewdynamicfieldtoexistedticketlistcommand.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2FF02035-9624-4B9A-8C3E-19CCE8FA2219}|TinyCRM\\TinyCRM.csproj|solutionrelative:tinycrm\\requestticket\\commands\\batchinsertnewdynamicfieldtoexistedticketlistcommand.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2FF02035-9624-4B9A-8C3E-19CCE8FA2219}|TinyCRM\\TinyCRM.csproj|c:\\users\\<USER>\\source\\workspaces\\cep_netcore\\tinycrm\\channel\\commands\\createeditchannelcommand.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2FF02035-9624-4B9A-8C3E-19CCE8FA2219}|TinyCRM\\TinyCRM.csproj|solutionrelative:tinycrm\\channel\\commands\\createeditchannelcommand.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2FF02035-9624-4B9A-8C3E-19CCE8FA2219}|TinyCRM\\TinyCRM.csproj|c:\\users\\<USER>\\source\\workspaces\\cep_netcore\\tinycrm\\workflow\\queries\\getworkflowbyidquery.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2FF02035-9624-4B9A-8C3E-19CCE8FA2219}|TinyCRM\\TinyCRM.csproj|solutionrelative:tinycrm\\workflow\\queries\\getworkflowbyidquery.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2FF02035-9624-4B9A-8C3E-19CCE8FA2219}|TinyCRM\\TinyCRM.csproj|c:\\users\\<USER>\\source\\workspaces\\cep_netcore\\tinycrm\\requestticket\\events\\requestticketcreatedevent.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2FF02035-9624-4B9A-8C3E-19CCE8FA2219}|TinyCRM\\TinyCRM.csproj|solutionrelative:tinycrm\\requestticket\\events\\requestticketcreatedevent.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2FF02035-9624-4B9A-8C3E-19CCE8FA2219}|TinyCRM\\TinyCRM.csproj|c:\\users\\<USER>\\source\\workspaces\\cep_netcore\\tinycrm\\report\\tasks\\reportpreprocessdatascheduledtask.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2FF02035-9624-4B9A-8C3E-19CCE8FA2219}|TinyCRM\\TinyCRM.csproj|solutionrelative:tinycrm\\report\\tasks\\reportpreprocessdatascheduledtask.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2FF02035-9624-4B9A-8C3E-19CCE8FA2219}|TinyCRM\\TinyCRM.csproj|c:\\users\\<USER>\\source\\workspaces\\cep_netcore\\tinycrm\\workflow\\commands\\createedittasktypelistinworkflowcommand.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2FF02035-9624-4B9A-8C3E-19CCE8FA2219}|TinyCRM\\TinyCRM.csproj|solutionrelative:tinycrm\\workflow\\commands\\createedittasktypelistinworkflowcommand.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2FF02035-9624-4B9A-8C3E-19CCE8FA2219}|TinyCRM\\TinyCRM.csproj|c:\\users\\<USER>\\source\\workspaces\\cep_netcore\\tinycrm\\requestticket\\commands\\createticketautomaticsurveyfeedbackscommand.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2FF02035-9624-4B9A-8C3E-19CCE8FA2219}|TinyCRM\\TinyCRM.csproj|solutionrelative:tinycrm\\requestticket\\commands\\createticketautomaticsurveyfeedbackscommand.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2FF02035-9624-4B9A-8C3E-19CCE8FA2219}|TinyCRM\\TinyCRM.csproj|c:\\users\\<USER>\\source\\workspaces\\cep_netcore\\tinycrm\\workflow\\commands\\createedittasktypeinworkflowcommand.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2FF02035-9624-4B9A-8C3E-19CCE8FA2219}|TinyCRM\\TinyCRM.csproj|solutionrelative:tinycrm\\workflow\\commands\\createedittasktypeinworkflowcommand.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2FF02035-9624-4B9A-8C3E-19CCE8FA2219}|TinyCRM\\TinyCRM.csproj|c:\\users\\<USER>\\source\\workspaces\\cep_netcore\\tinycrm\\requestticket\\commands\\customerticketbyservicetype.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2FF02035-9624-4B9A-8C3E-19CCE8FA2219}|TinyCRM\\TinyCRM.csproj|solutionrelative:tinycrm\\requestticket\\commands\\customerticketbyservicetype.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2FF02035-9624-4B9A-8C3E-19CCE8FA2219}|TinyCRM\\TinyCRM.csproj|c:\\users\\<USER>\\source\\workspaces\\cep_netcore\\tinycrm\\requestticket\\events\\requestticketupdatedevent.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2FF02035-9624-4B9A-8C3E-19CCE8FA2219}|TinyCRM\\TinyCRM.csproj|solutionrelative:tinycrm\\requestticket\\events\\requestticketupdatedevent.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 23, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{809f6ff3-8092-454a-8003-6d4091f9b5bb}"}, {"$type": "Bookmark", "Name": "ST:5:0:{809f6ff3-8092-454a-8003-6d4091f9b5bb}"}, {"$type": "Bookmark", "Name": "ST:3:0:{809f6ff3-8092-454a-8003-6d4091f9b5bb}"}, {"$type": "Bookmark", "Name": "ST:6:0:{809f6ff3-8092-454a-8003-6d4091f9b5bb}"}, {"$type": "Bookmark", "Name": "ST:4:0:{809f6ff3-8092-454a-8003-6d4091f9b5bb}"}, {"$type": "Bookmark", "Name": "ST:2:0:{80454082-9ab8-47d4-af23-82bf6739e2a9}"}, {"$type": "Bookmark", "Name": "ST:18:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:20:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:21:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:19:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:22:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:1:0:{80454082-9ab8-47d4-af23-82bf6739e2a9}"}, {"$type": "Bookmark", "Name": "ST:0:0:{80454082-9ab8-47d4-af23-82bf6739e2a9}"}, {"$type": "Bookmark", "Name": "ST:23:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:24:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:25:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:16:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:17:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:13:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:26:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:14:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:15:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:0:0:{65ddf8c3-8f89-4077-a6c6-dbb8853aab13}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "CallServiceCallbackScheduledTask.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\Workspaces\\CEP_NETCORE\\TinyCRM\\Outbound\\ContactCallResultServiceCallback\\Tasks\\CallServiceCallbackScheduledTask.cs", "RelativeDocumentMoniker": "TinyCRM\\Outbound\\ContactCallResultServiceCallback\\Tasks\\CallServiceCallbackScheduledTask.cs", "ToolTip": "C:\\Users\\<USER>\\source\\Workspaces\\CEP_NETCORE\\TinyCRM\\Outbound\\ContactCallResultServiceCallback\\Tasks\\CallServiceCallbackScheduledTask.cs*", "RelativeToolTip": "TinyCRM\\Outbound\\ContactCallResultServiceCallback\\Tasks\\CallServiceCallbackScheduledTask.cs*", "ViewState": "AgIAAAAAAAAAAAAAAAAAABAAAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-30T05:19:50.069Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "GetCloneRequestTicketInfoQuery.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\Workspaces\\CEP_NETCORE\\TinyCRM\\RequestTicket\\Queries\\GetCloneRequestTicketInfoQuery.cs", "RelativeDocumentMoniker": "TinyCRM\\RequestTicket\\Queries\\GetCloneRequestTicketInfoQuery.cs", "ToolTip": "C:\\Users\\<USER>\\source\\Workspaces\\CEP_NETCORE\\TinyCRM\\RequestTicket\\Queries\\GetCloneRequestTicketInfoQuery.cs", "RelativeToolTip": "TinyCRM\\RequestTicket\\Queries\\GetCloneRequestTicketInfoQuery.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAsAAAAEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-30T05:18:07.86Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "RequestTicketDataSynchronizedEvent.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\Workspaces\\CEP_NETCORE\\TinyCRM\\RequestTicket\\Events\\RequestTicketDataSynchronizedEvent.cs", "RelativeDocumentMoniker": "TinyCRM\\RequestTicket\\Events\\RequestTicketDataSynchronizedEvent.cs", "ToolTip": "C:\\Users\\<USER>\\source\\Workspaces\\CEP_NETCORE\\TinyCRM\\RequestTicket\\Events\\RequestTicketDataSynchronizedEvent.cs", "RelativeToolTip": "TinyCRM\\RequestTicket\\Events\\RequestTicketDataSynchronizedEvent.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAUAAAA7AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-30T05:16:28.782Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "InsertProductRequestTicketCommand.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\Workspaces\\CEP_NETCORE\\TinyCRM\\RequestTicket\\Commands\\InsertProductRequestTicketCommand.cs", "RelativeDocumentMoniker": "TinyCRM\\RequestTicket\\Commands\\InsertProductRequestTicketCommand.cs", "ToolTip": "C:\\Users\\<USER>\\source\\Workspaces\\CEP_NETCORE\\TinyCRM\\RequestTicket\\Commands\\InsertProductRequestTicketCommand.cs", "RelativeToolTip": "TinyCRM\\RequestTicket\\Commands\\InsertProductRequestTicketCommand.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAYAAAADAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-30T05:16:08.751Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "ChangeTicketOwnedByOrganizationIdBatchCommand.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\Workspaces\\CEP_NETCORE\\TinyCRM\\RequestTicket\\Commands\\ChangeTicketOwnedByOrganizationIdBatchCommand.cs", "RelativeDocumentMoniker": "TinyCRM\\RequestTicket\\Commands\\ChangeTicketOwnedByOrganizationIdBatchCommand.cs", "ToolTip": "C:\\Users\\<USER>\\source\\Workspaces\\CEP_NETCORE\\TinyCRM\\RequestTicket\\Commands\\ChangeTicketOwnedByOrganizationIdBatchCommand.cs", "RelativeToolTip": "TinyCRM\\RequestTicket\\Commands\\ChangeTicketOwnedByOrganizationIdBatchCommand.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-30T05:16:06.314Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "BatchInsertNewDynamicFieldToExistedTicketListCommand.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\Workspaces\\CEP_NETCORE\\TinyCRM\\RequestTicket\\Commands\\BatchInsertNewDynamicFieldToExistedTicketListCommand.cs", "RelativeDocumentMoniker": "TinyCRM\\RequestTicket\\Commands\\BatchInsertNewDynamicFieldToExistedTicketListCommand.cs", "ToolTip": "C:\\Users\\<USER>\\source\\Workspaces\\CEP_NETCORE\\TinyCRM\\RequestTicket\\Commands\\BatchInsertNewDynamicFieldToExistedTicketListCommand.cs", "RelativeToolTip": "TinyCRM\\RequestTicket\\Commands\\BatchInsertNewDynamicFieldToExistedTicketListCommand.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-30T05:16:01.39Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "CreateEditChannelCommand.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\Workspaces\\CEP_NETCORE\\TinyCRM\\Channel\\Commands\\CreateEditChannelCommand.cs", "RelativeDocumentMoniker": "TinyCRM\\Channel\\Commands\\CreateEditChannelCommand.cs", "ToolTip": "C:\\Users\\<USER>\\source\\Workspaces\\CEP_NETCORE\\TinyCRM\\Channel\\Commands\\CreateEditChannelCommand.cs", "RelativeToolTip": "TinyCRM\\Channel\\Commands\\CreateEditChannelCommand.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-30T05:15:45.364Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 10, "Title": "ReportPreprocessDataScheduledTask.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\Workspaces\\CEP_NETCORE\\TinyCRM\\Report\\Tasks\\ReportPreprocessDataScheduledTask.cs", "RelativeDocumentMoniker": "TinyCRM\\Report\\Tasks\\ReportPreprocessDataScheduledTask.cs", "ToolTip": "C:\\Users\\<USER>\\source\\Workspaces\\CEP_NETCORE\\TinyCRM\\Report\\Tasks\\ReportPreprocessDataScheduledTask.cs", "RelativeToolTip": "TinyCRM\\Report\\Tasks\\ReportPreprocessDataScheduledTask.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAoAAABDAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-30T04:48:10.161Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 8, "Title": "GetWorkflowByIdQuery.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\Workspaces\\CEP_NETCORE\\TinyCRM\\Workflow\\Queries\\GetWorkflowByIdQuery.cs", "RelativeDocumentMoniker": "TinyCRM\\Workflow\\Queries\\GetWorkflowByIdQuery.cs", "ToolTip": "C:\\Users\\<USER>\\source\\Workspaces\\CEP_NETCORE\\TinyCRM\\Workflow\\Queries\\GetWorkflowByIdQuery.cs", "RelativeToolTip": "TinyCRM\\Workflow\\Queries\\GetWorkflowByIdQuery.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABEAAABBAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-30T04:46:05.19Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 11, "Title": "CreateEditTaskTypeListInWorkflowCommand.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\Workspaces\\CEP_NETCORE\\TinyCRM\\Workflow\\Commands\\CreateEditTaskTypeListInWorkflowCommand.cs", "RelativeDocumentMoniker": "TinyCRM\\Workflow\\Commands\\CreateEditTaskTypeListInWorkflowCommand.cs", "ToolTip": "C:\\Users\\<USER>\\source\\Workspaces\\CEP_NETCORE\\TinyCRM\\Workflow\\Commands\\CreateEditTaskTypeListInWorkflowCommand.cs", "RelativeToolTip": "TinyCRM\\Workflow\\Commands\\CreateEditTaskTypeListInWorkflowCommand.cs", "ViewState": "AgIAABAAAAAAAAAAAAA1wBUAAABWAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-30T04:00:44.51Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 13, "Title": "CreateEditTaskTypeInWorkflowCommand.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\Workspaces\\CEP_NETCORE\\TinyCRM\\Workflow\\Commands\\CreateEditTaskTypeInWorkflowCommand.cs", "RelativeDocumentMoniker": "TinyCRM\\Workflow\\Commands\\CreateEditTaskTypeInWorkflowCommand.cs", "ToolTip": "C:\\Users\\<USER>\\source\\Workspaces\\CEP_NETCORE\\TinyCRM\\Workflow\\Commands\\CreateEditTaskTypeInWorkflowCommand.cs", "RelativeToolTip": "TinyCRM\\Workflow\\Commands\\CreateEditTaskTypeInWorkflowCommand.cs", "ViewState": "AgIAACgAAAAAAAAAAAAYwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-30T04:00:17.329Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 14, "Title": "CustomerTicketByServiceType.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\Workspaces\\CEP_NETCORE\\TinyCRM\\RequestTicket\\Commands\\CustomerTicketByServiceType.cs", "RelativeDocumentMoniker": "TinyCRM\\RequestTicket\\Commands\\CustomerTicketByServiceType.cs", "ToolTip": "C:\\Users\\<USER>\\source\\Workspaces\\CEP_NETCORE\\TinyCRM\\RequestTicket\\Commands\\CustomerTicketByServiceType.cs", "RelativeToolTip": "TinyCRM\\RequestTicket\\Commands\\CustomerTicketByServiceType.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-30T03:59:58.795Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 12, "Title": "CreateTicketAutomaticSurveyFeedbacksCommand.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\Workspaces\\CEP_NETCORE\\TinyCRM\\RequestTicket\\Commands\\CreateTicketAutomaticSurveyFeedbacksCommand.cs", "RelativeDocumentMoniker": "TinyCRM\\RequestTicket\\Commands\\CreateTicketAutomaticSurveyFeedbacksCommand.cs", "ToolTip": "C:\\Users\\<USER>\\source\\Workspaces\\CEP_NETCORE\\TinyCRM\\RequestTicket\\Commands\\CreateTicketAutomaticSurveyFeedbacksCommand.cs", "RelativeToolTip": "TinyCRM\\RequestTicket\\Commands\\CreateTicketAutomaticSurveyFeedbacksCommand.cs", "ViewState": "AgIAAC4AAAAAAAAAAAA3wM4AAAAVAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-30T03:57:51.667Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 15, "Title": "RequestTicketUpdatedEvent.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\Workspaces\\CEP_NETCORE\\TinyCRM\\RequestTicket\\Events\\RequestTicketUpdatedEvent.cs", "RelativeDocumentMoniker": "TinyCRM\\RequestTicket\\Events\\RequestTicketUpdatedEvent.cs", "ToolTip": "C:\\Users\\<USER>\\source\\Workspaces\\CEP_NETCORE\\TinyCRM\\RequestTicket\\Events\\RequestTicketUpdatedEvent.cs", "RelativeToolTip": "TinyCRM\\RequestTicket\\Events\\RequestTicketUpdatedEvent.cs", "ViewState": "AgIAAA8AAAAAAAAAAAAAACMAAABcAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-30T03:52:13.623Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 9, "Title": "RequestTicketCreatedEvent.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\Workspaces\\CEP_NETCORE\\TinyCRM\\RequestTicket\\Events\\RequestTicketCreatedEvent.cs", "RelativeDocumentMoniker": "TinyCRM\\RequestTicket\\Events\\RequestTicketCreatedEvent.cs", "ToolTip": "C:\\Users\\<USER>\\source\\Workspaces\\CEP_NETCORE\\TinyCRM\\RequestTicket\\Events\\RequestTicketCreatedEvent.cs", "RelativeToolTip": "TinyCRM\\RequestTicket\\Events\\RequestTicketCreatedEvent.cs", "ViewState": "AgIAAAwAAAAAAAAAAAAAACsAAAAaAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-30T03:52:01.023Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "RequestTicketClosedEvent.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\Workspaces\\CEP_NETCORE\\TinyCRM\\RequestTicket\\Events\\RequestTicketClosedEvent.cs", "RelativeDocumentMoniker": "TinyCRM\\RequestTicket\\Events\\RequestTicketClosedEvent.cs", "ToolTip": "C:\\Users\\<USER>\\source\\Workspaces\\CEP_NETCORE\\TinyCRM\\RequestTicket\\Events\\RequestTicketClosedEvent.cs", "RelativeToolTip": "TinyCRM\\RequestTicket\\Events\\RequestTicketClosedEvent.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAsAAAAEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-30T03:51:47.18Z", "EditorCaption": ""}]}, {"DockedWidth": 1546, "SelectedChildIndex": -1, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{34e76e81-ee4a-11d0-ae2e-00a0c90fffc3}"}]}]}]}
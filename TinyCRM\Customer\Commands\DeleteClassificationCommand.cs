﻿using System;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;

namespace TinyCRM.Customer.Commands
{
    public class DeleteClassificationCommand : CommandBase
    {
        public Guid Id { get; set; }
    }

    internal class DeleteClassificationCommandHandler : CommandHandlerBase<DeleteClassificationCommand>
    {
        public DeleteClassificationCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task ExecuteAsync(DeleteClassificationCommand command)
        {
            await Repository.DeleteAsync<ClassificationEntity>(command.Id);
        }
    }
}

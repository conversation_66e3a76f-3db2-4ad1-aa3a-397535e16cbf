﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TinyCRM.Campaign;
using TinyCRM.Customer;
using TinyCRM.DigitalChannel;
using Webaby;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;

namespace TinyCRM.DigitalContact.Queries
{
    public class DigitalContactInfoReuslt
    {
        public Guid ContactTypeId { get; set; }
        public string UserId { get; set; }
        public string DigitalContactTypeName { get; set; }
        public string DfoThreadId { get; set; }
        public string DfoChannelId { get; set; }
    }


    public class GetDigitalContactInfoByCusIdOrDigitalIdQuery : QueryBase<DigitalContactInfoReuslt>
    {
        public Guid? DigitalContactId { get; set; }

        public Guid? CustomerId { get; set; }
    }

    internal class GetDigitalContactInfoByCusIdOrDigitalIdQueryQueryHandler : QueryHandlerBase<GetDigitalContactInfoByCusIdOrDigitalIdQuery, DigitalContactInfoReuslt>
    { 
        public GetDigitalContactInfoByCusIdOrDigitalIdQueryQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<DigitalContactInfoReuslt>> ExecuteAsync(GetDigitalContactInfoByCusIdOrDigitalIdQuery query)
        {
            var mainquery = new List<DigitalContactInfoReuslt>();
            var dcList = await EntitySet.GetAsync<DigitalContactEntity>();
            var dctList = await EntitySet.GetAsync<DigitalContactTypeEntity>();
            var cList = await EntitySet.GetAsync<CustomerEntity>();
            if (query.CustomerId.HasValue)
            {
                mainquery = (from dc in dcList
                             join c in cList on dc.CustomerId equals c.Id into _c
                             from c in _c.DefaultIfEmpty()
                             join dct in dctList on dc.DigitalContactTypeId equals dct.Id
                             where dc.CustomerId == query.CustomerId.Value
                             select new DigitalContactInfoReuslt
                             {
                                 DfoThreadId = dc.DfoThreadId,
                                 ContactTypeId = dct.Id,
                                 UserId = dc.UserId,
                                 DigitalContactTypeName = dct.Name,
                                 DfoChannelId = dct.DfoChannelId
                             }).ToList();
            }
            if (query.DigitalContactId.HasValue)
            {
                mainquery = (from dc in dcList
                             join dct in dctList on dc.DigitalContactTypeId equals dct.Id
                             where dc.Id == query.DigitalContactId.Value
                             select new DigitalContactInfoReuslt
                             {
                                 DfoThreadId = dc.DfoThreadId,
                                 ContactTypeId = dct.Id,
                                 UserId = dc.UserId,
                                 DigitalContactTypeName = dct.Name,
                                 DfoChannelId = dct.DfoChannelId
                             }).ToList();
            }
            return new QueryResult<DigitalContactInfoReuslt>(mainquery);
        }
    }
}
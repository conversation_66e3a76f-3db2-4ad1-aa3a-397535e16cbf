﻿using System;
using System.Linq;
using Webaby;

namespace TinyCRM.Outbound.Campaign.Queries
{
    public class GetCampaignTeamAssigmentQuery : QueryBase<CampaignTeamAssignmentData>
    {
        public Guid CampaignId
        {
            get;
            set;
        }

        public Guid TeamId
        {
            get;
            set;
        }
    }

    internal class GetCampaignTeamAssigmentQueryHandler : QueryHandlerBase<GetCampaignTeamAssigmentQuery, CampaignTeamAssignmentData>
    {
        public override QueryResult<CampaignTeamAssignmentData> Execute(GetCampaignTeamAssigmentQuery query)
        {
            var campaignTeamAssignmentQuery = EntitySet.Get<CampaignTeamAssignmentEntity>();

            var campaignTeamAssignmentEntity = (from cta in campaignTeamAssignmentQuery
                                                where cta.CampaignId == query.CampaignId && cta.TeamId == query.TeamId
                                                select cta).FirstOrDefault();

            return new QueryResult<CampaignTeamAssignmentData>(CampaignTeamAssignmentData.FromEntity(campaignTeamAssignmentEntity));
        }
    }
}
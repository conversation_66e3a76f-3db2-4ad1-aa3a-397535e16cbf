﻿using System;
using Webaby;
using System.Linq;
using TinyCRM.Outbound.Prospect;
using TinyCRM.Outbound.ProspectAssignment;
using Webaby.Core.File;
using Webaby.Core.File.Queries;
using System.Collections.Generic;

namespace TinyCRM.Outbound.Campaign.Queries
{
    public class GetCampaignByCampaignNameQuery : QueryBase<CampaignData>
    {
        public string CampaignName { get; set; }
    }

    internal class GetCampaignByCampaignNameQueryHandler : QueryHandlerBase<GetCampaignByCampaignNameQuery, CampaignData>
    {
        public override QueryResult<CampaignData> Execute(GetCampaignByCampaignNameQuery query)
        {
            var campaign = EntitySet.Get<CampaignEntity>().FirstOrDefault(x=>x.CampaignName == query.CampaignName);
            var campaignData = CampaignData.FromEntity(campaign);
            return new QueryResult<CampaignData>(campaignData);
        }
    }
}
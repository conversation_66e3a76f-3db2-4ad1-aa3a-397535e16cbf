﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TinyCRM.Customer;
using TinyCRM.Customer.Queries;
using TinyCRM.Outbound.Prospect;
using Webaby;

namespace TinyCRM.Outbound.Contact.Queries
{
    public class GetContactByAssignmentIdQuery : QueryBase<CustomerData>
    {
        public Guid ContactAssignmentId { get; set; }
    }

    internal class GetContactByAssignmentIdQueryHandler : QueryHandlerBase<GetContactByAssignmentIdQuery, CustomerData>
    {
        public override QueryResult<CustomerData> Execute(GetContactByAssignmentIdQuery query)
        {
            var contactAssignment = EntitySet.Get<ProspectEntity>();
            var contact = EntitySet.Get<CustomerEntity>();

            var contacts = (from ca in contactAssignment
                            join c in contact on ca.CustomerId equals c.Id
                            where ca.Id == query.ContactAssignmentId
                            select c);

            if (contacts == null) throw new InvalidOperationException(T["Không tìm  thấy contact có id '{0}'", query.ContactAssignmentId]);
            return QueryResult.Create(contacts, CustomerData.FromEntity);  
        }
    }
}
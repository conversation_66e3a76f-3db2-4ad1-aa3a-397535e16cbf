﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using Webaby;
using Webaby.Data;

namespace TinyCRM.Outbound.Contact.Commands
{
    public class AddAnonymousDigitalContactListToCampaignCommand : CommandBase
    {
        public Guid CampaignId { get; set; }

        public bool? NotInCampaign { get; set; }

        public Guid? SelectedCampaignId { get; set; }

        public List<Guid> ResultCodeIds { get; set; }

        public List<Guid> ExcludeCampaignIds { get; set; }

        public string UID { get; set; }

        public Guid? DigitalContactTypeId { get; set; }

        public Guid? ImportSessionId { get; set; }

        public Guid? UserId { get; set; }

        public List<Guid> DigitalContactSelectedList { get; set; }

        public int? AssignedNumber { get; set; }
    }

    internal class AddAnonymousDigitalContactListToCampaignCommandHandler : CommandHandlerBase<AddAnonymousDigitalContactListToCampaignCommand>
    {
        public AddAnonymousDigitalContactListToCampaignCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task ExecuteAsync(AddAnonymousDigitalContactListToCampaignCommand command)
        {
            var cmd = EntitySet.CreateDbCommand();
            cmd.Parameters.AddRange(new[]
            {
                DbParameterHelper.AddNullableGuid(cmd, "@CampaignId", command.CampaignId),
                DbParameterHelper.NewNullableBooleanParameter(cmd, "@NotInCampaign", command.NotInCampaign),
                DbParameterHelper.AddNullableString(cmd, "@UID", command.UID),
                DbParameterHelper.AddNullableGuid(cmd, "@SelectedCampaignId", command.SelectedCampaignId, true),
                DbParameterHelper.NewIdListParameter("@ResultCodeIds", command.ResultCodeIds),
                DbParameterHelper.NewIdListParameter("@ExcludeCampaignIds", command.ExcludeCampaignIds),
                DbParameterHelper.AddNullableGuid(cmd, "@ImportSessionId", command.ImportSessionId),
                DbParameterHelper.AddNullableGuid(cmd, "@UserId", command.UserId),
                DbParameterHelper.AddNullableGuid(cmd, "@DigitalContactTypeId", command.DigitalContactTypeId),

                DbParameterHelper.NewIdListParameter("@DigitalContactSelectedList", command.DigitalContactSelectedList),
                DbParameterHelper.AddNullableInt(cmd, "@AssignedNumber", command.AssignedNumber),

            });

            cmd.CommandText = "dbo.DigitalContact_AddAnonymousDigitalContactToCampaign";
            cmd.CommandType = CommandType.StoredProcedure;

            await EntitySet.ExecuteNonQueryAsync(cmd);
        }
    }
}
﻿using System;
using Webaby;

namespace TinyCRM.ContentTemplate.Command
{
    public class CreateEditAutoCompleteContentTemplateCommand : CommandBase
    {
        public Guid? Id { get; set; }

        public string Content { get; set; }

        public Guid? ReferenceObjectId { get; set; }

        public string ReferenceObjectType { get; set; }

        public bool IsPublic { get; set; }

        public bool IsNew { get; set; }

        public TemplateType? Type { get; set; }
        public string UrlGetTemplateParameter { get; set; }

        public string Keyword { get; set; }
    }

    internal class CreateEditAutoCompleteContentTemplateCommandHandler : CommandHandlerBase<CreateEditAutoCompleteContentTemplateCommand>
    {
        public CreateEditAutoCompleteContentTemplateCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }
        public override async Task ExecuteAsync(CreateEditAutoCompleteContentTemplateCommand command)
        {
            var entity = new AutoCompleteContentTemplateEntity();
            if (!command.IsNew)
            {
                entity = await EntitySet.GetAsync<AutoCompleteContentTemplateEntity>(command.Id.Value);
            }
            if(entity != null)
            {
                entity.Type = command.Type;
                entity.UrlGetTemplateParameter = command.UrlGetTemplateParameter;
                entity.Content = command.Content;
                entity.ReferenceObjectId = command.ReferenceObjectId;
                entity.ReferenceObjectType = command.ReferenceObjectType;
                entity.IsPublic = command.IsPublic;
                entity.Keyword = command.Keyword;
                await Repository.SaveAsync(entity);
            }
        }
    }
}

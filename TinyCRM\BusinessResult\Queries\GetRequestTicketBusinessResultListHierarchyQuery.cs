﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using Webaby;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.BusinessResult.Queries
{
    public class GetRequestTicketBusinessResultListHierarchyQuery : QueryBase<NodeResult>
    {
        public Guid ServiceTypeId { get; set; }
        public Guid? WorkflowId { get; set; }
    }

    public class GetRequestTicketBusinessResultListHierarchyQueryQuery : QueryHandlerBase<GetRequestTicketBusinessResultListHierarchyQuery, NodeResult>
    {
        public GetRequestTicketBusinessResultListHierarchyQueryQuery(IServiceProvider serviceProvider)
            : base(serviceProvider) { }

        public override async Task<QueryResult<NodeResult>> ExecuteAsync(GetRequestTicketBusinessResultListHierarchyQuery query)
        {
            var cmd = EntitySet.CreateDbCommand();
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@ServiceTypeId", query.ServiceTypeId));
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@WorkflowId", query.WorkflowId));
            cmd.CommandText = "dbo.GetRequestTicketBusinessResultList";
            cmd.CommandType = CommandType.StoredProcedure;
            var businessResultListQuery = (await EntitySet.ExecuteReadCommandAsync<BusinessResultData>(cmd)).ToList();

            var parentNodeList = (from br in businessResultListQuery
                                  where br.ParentId == null || br.ParentId == Guid.Empty
                                  orderby br.DisplayOrder
                                  select new NodeResult
                                  {
                                      id = br.Id,
                                      text = br.Name,
                                      children = null
                                  }).ToList();

            var childNodeList = (from br in businessResultListQuery
                                 where br.ParentId != null && br.ParentId != Guid.Empty
                                 orderby br.DisplayOrder
                                 select new Child
                                 {
                                     id = br.Id,
                                     text = br.Name,
                                     ParentId = br.ParentId
                                 }).ToList();

            foreach (var parentNode in parentNodeList)
            {
                parentNode.children = (from child in childNodeList
                                       where child.ParentId == parentNode.id
                                       select child).ToList();
            }

            return new QueryResult<NodeResult>(parentNodeList);
        }
    }
}
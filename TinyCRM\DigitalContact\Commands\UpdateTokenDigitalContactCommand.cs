﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using TinyCRM.DigitalChannel;
using TinyCRM.DigitalChannel.Queries;
using TinyCRM.DigitalPushCode.Queries;
using Webaby;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;

namespace TinyCRM.DigitalContact.Commands
{
    public class UpdateTokenDigitalContactCommand : CommandBase
    {
        public string UserId { get; set; }

        public string ExtensionToken { get; set; }

        public Guid DigitalContactTypeId { get; set; }
    }

    internal class UpdateTokenDigitalContactCommandHandler : CommandHandlerBase<UpdateTokenDigitalContactCommand>
    {
        public UpdateTokenDigitalContactCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task ExecuteAsync(UpdateTokenDigitalContactCommand command)
        {
            var digitalContact = (await EntitySet.GetAsync<DigitalContactEntity>()).Where(x => x.UserId == command.UserId && x.DigitalContactTypeId == command.DigitalContactTypeId).ToList();
            if (digitalContact.Count < 1 || digitalContact == null)
            {
                var getNameContactType = await EntitySet.GetAsync<DigitalContactTypeEntity>(command.DigitalContactTypeId);
                throw new Exception(T["Không tim thầy Digital Contact có UserId: {0} với contact type là: {1}", command.UserId, getNameContactType.Name]);
            }
            var editEntity = new List<DigitalContactEntity>();
            foreach (var item in digitalContact)
            {
                item.ExtensionToken = command.ExtensionToken;
                editEntity.Add(item);
            }
            await Repository.SaveAsync(editEntity);
        }
    }
}

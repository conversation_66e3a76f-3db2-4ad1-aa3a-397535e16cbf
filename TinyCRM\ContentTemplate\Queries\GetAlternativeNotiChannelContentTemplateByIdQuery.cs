﻿using System;
using Webaby;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;

namespace TinyCRM.ContentTemplate.Queries
{
    public class GetAlternativeNotiChannelContentTemplateByIdQuery : QueryBase<AlternativeNotiChannelContentTemplateData>
    {
        public Guid Id { get; set; }
    }

    internal class GetAlternativeNotiChannelContentTemplateByIdQueryHandler : QueryHandlerBase<GetAlternativeNotiChannelContentTemplateByIdQuery, AlternativeNotiChannelContentTemplateData>
    {
        public GetAlternativeNotiChannelContentTemplateByIdQueryHandler(IServiceProvider serviceProvider)
            : base(serviceProvider) { }

        public override async Task<QueryResult<AlternativeNotiChannelContentTemplateData>> ExecuteAsync(GetAlternativeNotiChannelContentTemplateByIdQuery query)
        {
            var entity = await EntitySet.GetAsync<AlternativeNotiChannelContentTemplateEntity>(query.Id);
            return new QueryResult<AlternativeNotiChannelContentTemplateData>(Mapper.Map<AlternativeNotiChannelContentTemplateData>(entity));
        }
    }
}

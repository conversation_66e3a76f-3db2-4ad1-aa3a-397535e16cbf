﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Webaby.Data;
using Webaby;
using AutoMapper;
using Webaby.Localization;

namespace TinyCRM.AutomaticTask.Queries
{
    public class GetRequestTicketFirstTaskAutoNextTaskErrorLisQuery : QueryBase<RequestTicketFirstTaskAutoNextTaskErrorItem>
    {
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
    }

    internal class GetRequestTicketFirstTaskAutoNextTaskErrorLisQueryHandler : QueryHandlerBase<GetRequestTicketFirstTaskAutoNextTaskErrorLisQuery, RequestTicketFirstTaskAutoNextTaskErrorItem>
    {
        public GetRequestTicketFirstTaskAutoNextTaskErrorLisQueryHandler(IServiceProvider serviceProvider)
            : base(serviceProvider) { }

        public override async Task<QueryResult<RequestTicketFirstTaskAutoNextTaskErrorItem>> ExecuteAsync(GetRequestTicketFirstTaskAutoNextTaskErrorLisQuery query)
        {
            var cmd = EntitySet.CreateDbCommand();
            cmd.Parameters.Add(DbParameterHelper.NewNullableDateTimeParameter(cmd, "@FromDate", query.FromDate));
            cmd.Parameters.Add(DbParameterHelper.NewNullableDateTimeParameter(cmd, "@ToDate", query.ToDate));
            cmd.Parameters.Add(DbParameterHelper.AddNullableInt(cmd,"@StartRow", query.Pagination.StartRow));
            cmd.Parameters.Add(DbParameterHelper.AddNullableInt(cmd,"@EndRow", query.Pagination.EndRow));
            cmd.CommandText = "dbo.GetRequestTicketFirstTaskAutoNextTaskErrorList";
            cmd.CommandType = CommandType.StoredProcedure;
            var mainQuery = await EntitySet.ExecuteReadCommandAsync<RequestTicketFirstTaskAutoNextTaskErrorItem>(cmd);
            return new QueryResult<RequestTicketFirstTaskAutoNextTaskErrorItem>(mainQuery);
        }
    }
}
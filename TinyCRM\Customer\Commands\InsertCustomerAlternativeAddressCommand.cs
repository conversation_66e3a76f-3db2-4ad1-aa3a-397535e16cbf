﻿using System;
using System.Threading.Tasks;
using AutoMapper;
using Webaby;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.Customer.Commands
{
    public class InsertCustomerAlternativeAddressCommand : CommandBase
    {
        public Guid Id { get; set; }

        public string Name { get; set; }
        
        public Guid CustomerId { get; set; }

        public string AddressNumber { get; set; }

        public string AddressStreet { get; set; }

        public Guid? RegionId { get; set; }

        public Guid? AreaId { get; set; }

        public Guid? ProvinceId { get; set; }

        public Guid? DistrictId { get; set; }

        public Guid? WardId { get; set; }
    }

    internal class InsertCustomerAlternativeAddressCommandHandler :
        CommandHandlerBase<InsertCustomerAlternativeAddressCommand>
    {
        public InsertCustomerAlternativeAddressCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task ExecuteAsync(InsertCustomerAlternativeAddressCommand command)
        {
            var entity = new CustomerAlternativeAddressEntity();
            Mapper.Map(command, entity);
            await Repository.SaveAsync(entity);
        }
    }
}

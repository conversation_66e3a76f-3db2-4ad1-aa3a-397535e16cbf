﻿using AutoMapper;
using System;
using System.Collections.Generic;
using System.Linq;
using TinyCRM.TaskType;
using TinyCRM.Workflow;
using Webaby;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.BusinessResult.Commands
{
    public class AddWorkflowBusinessResultCommand : CommandBase
    {
        public Guid WorkflowId { get; set; }

        public List<Guid> BusinessResultList { get; set; }
    }

    internal class AddWorkflowBusinessResultCommandHandler : CommandHandlerBase<AddWorkflowBusinessResultCommand>
    {
        public AddWorkflowBusinessResultCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }
        public override async Task ExecuteAsync(AddWorkflowBusinessResultCommand command)
        {
            List<IEntity> savedEntites = new List<IEntity>();
            foreach (var businessResultId in command.BusinessResultList)
            {
                BusinessResultReferenceEntity businessResultReferenceEntity = new BusinessResultReferenceEntity();
                businessResultReferenceEntity.ReferenceObjectId = command.WorkflowId;
                businessResultReferenceEntity.BusinessResultId = businessResultId;
                businessResultReferenceEntity.ReferenceType = "Workflow";

                savedEntites.Add(businessResultReferenceEntity);
            }

            await Repository.SaveAsync(savedEntites);
        }
    }
}
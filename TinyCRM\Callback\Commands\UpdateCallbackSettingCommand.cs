﻿using AutoMapper;
using System;
using Webaby;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.Callback.Commands
{
    public class UpdateCallbackSettingCommand : CommandBase
    {
        public Guid Id { get; set; }

        public string Name { get; set; }

        public string Value { get; set; }

        public string DefaultValue { get; set; }

        public string Description { get; set; }

        public string Section { get; set; }
    }

    internal class UpdateCallbackSettingCommandHandler : CommandHandlerBase<UpdateCallbackSettingCommand>
    {
        public UpdateCallbackSettingCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }
        public override async Task ExecuteAsync(UpdateCallbackSettingCommand command)
        {
            var entity = new CallbackSettingsEntity();
            entity.Id = command.Id;
            entity.Name = command.Name;
            entity.Value = command.Value;
            entity.DefaultValue = command.DefaultValue;
            entity.Description = command.Description;
            entity.Section = command.Section;

            await Repository.SaveAsync  (entity);
        }
    }
}
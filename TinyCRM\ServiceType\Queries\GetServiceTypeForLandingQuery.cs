﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TinyCRM.ServiceCategory;
using Webaby;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;

namespace TinyCRM.ServiceType.Queries
{
    public class GetServiceTypeForLandingQuery : QueryBase<GetServiceTypeForLandingQuery.Result>
    {
        public class Result
        {
            public Guid ServiceTypeId { get; set; }
            public Guid Level1Id { get; set; }

            public string Level1Name { get; set; }

            public Guid? Level2Id { get; set; }

            public string Level2Name { get; set; }

            public Guid? Level3Id { get; set; }

            public string Level3Name { get; set; }

            public Guid? Level4Id { get; set; }

            public string Level4Name { get; set; }
        }
    }

    internal class GetServiceTypeForLandingQueryHandler : QueryHandlerBase<GetServiceTypeForLandingQuery, GetServiceTypeForLandingQuery.Result>
    {
        public GetServiceTypeForLandingQueryHandler(IEntitySet entitySet, IRepository repository, IText text, IMapper mapper)
            : base(entitySet, repository, text, mapper) { }

        public override async Task<QueryResult<GetServiceTypeForLandingQuery.Result>> ExecuteAsync(GetServiceTypeForLandingQuery query)
        {
            var stList = await EntitySet.GetAsync<ServiceTypeEntity>();
            var catList = await EntitySet.GetAsync<ServiceCategoryEntity>();
            var result = from st in stList
                         join l1 in catList on st.Level1Id equals l1.Id
                         join l2 in catList on st.Level2Id equals l2.Id into _l2
                         from l2 in _l2.DefaultIfEmpty()
                         join l3 in catList on st.Level3Id equals l3.Id into _l3
                         from l3 in _l3.DefaultIfEmpty()
                         join l4 in catList on st.Level4Id equals l4.Id into _l4
                         from l4 in _l4.DefaultIfEmpty()
                         select new GetServiceTypeForLandingQuery.Result
                         {
                             ServiceTypeId = st.Id,
                             Level1Id = l1.Id,
                             Level1Name = l1.Name,
                             Level2Id = l2 != null ? l2.Id : (Guid?)null,
                             Level2Name = l2 != null ? l2.Name : null,
                             Level3Id = l3 != null ? l3.Id : (Guid?)null,
                             Level3Name = l3 != null ? l3.Name : null,
                             Level4Id = l4 != null ? l4.Id : (Guid?)null,
                             Level4Name = l4 != null ? l4.Name : null
                         };
            return QueryResult.Create(result);
        }
    }
}

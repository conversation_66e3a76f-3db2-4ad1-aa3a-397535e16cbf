﻿using System;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using TinyCRM.Phase;
using TinyCRM.RequestTicket;
using Webaby;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.Customer.Queries
{
    public class GetCustomerByTaskIdQuery : QueryBase<CustomerData>
    {
        public GetCustomerByTaskIdQuery(Guid taskId)
        {
            this.taskId = taskId;
        }

        public Guid taskId { get; set; }
    }

    internal class GetCustomerByTaskIdQueryHandler : QueryHandlerBase<GetCustomerByTaskIdQuery, CustomerData>
    {
        public GetCustomerByTaskIdQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<CustomerData>> ExecuteAsync(GetCustomerByTaskIdQuery query)
        {
            var customerEntities = from cus in EntitySet.Get<CustomerEntity>()
                                   join ticket in EntitySet.Get<RequestTicketEntity>() on cus.Id equals ticket.CustomerId
                                   join phase in EntitySet.Get<PhaseEntity>() on ticket.Id equals phase.TicketId
                                   join task in EntitySet.Get<TaskEntity>() on phase.Id equals task.PhaseId
                                   where task.Id == query.taskId
                                   select cus;
            return QueryResult.Create(customerEntities, Mapper.Map<CustomerData>);
        }
    }
}

﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TinyCRM.EntityLink;
using TinyCRM.Enums;
using TinyCRM.RequestTicket;
using TinyCRM.ServiceType;
using Webaby;
using Webaby.Core.DynamicForm;
using Webaby.Core.File;
using Webaby.Core.File.Queries;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;
using TinyCRM.Outbound.Campaign;

namespace TinyCRM.Campaign.Queries
{
    public class GetFilesFromEntityLinkWithFieldEditFilesQuery : QueryBase<FileData>
    {
        public Guid ObjectId { get; set; }
        public string ObjectGet { get; set; }
    }

    internal class GetFilesFromEntityLinkWithFieldEditFilesQueryHandler : QueryHandlerBase<GetFilesFromEntityLinkWithFieldEditFilesQuery, FileData>
    {
        public GetFilesFromEntityLinkWithFieldEditFilesQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<FileData>> ExecuteAsync(GetFilesFromEntityLinkWithFieldEditFilesQuery query)
        {
            var elList = await EntitySet.GetAsync<EntityLinkEntity>();
            var cList = await EntitySet.GetAsync<CampaignEntity>();
            var fList = await EntitySet.GetAsync<FileEntity>();
            var mainQuery = (from el in elList
                             join c in cList on el.ToEntityId equals c.Id
                             join f in fList on c.Id equals f.ReferenceObjectId
                             where el.FromEntityId == query.ObjectId && f.ReferenceObjectType == "Campaign.EditFiles"
                             select f);
            return QueryResult.Create(mainQuery, x => Mapper.Map<FileData>(x));
        }
    }
}

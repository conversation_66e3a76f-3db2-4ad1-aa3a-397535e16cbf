﻿using Webaby.Data;
using AutoMapper;
using Webaby.Localization;
using Webaby;
using System.Threading.Tasks;
using System;
using System.Linq;
using TinyCRM.BusinessResult;
using TinyCRM.TaskType;

namespace TinyCRM.AutomaticTask.Queries
{
    public class GetAutoNextTaskByIdQuery : QueryBase<AutoNextTaskData>
    {
        public Guid Id { get; set; }
    }

    internal class GetAutoNextTaskByIdQueryHandler : QueryHandlerBase<GetAutoNextTaskByIdQuery, AutoNextTaskData>
    {
        public GetAutoNextTaskByIdQueryHandler(IServiceProvider serviceProvider)
            : base(serviceProvider) { }

        public override async Task<QueryResult<AutoNextTaskData>> ExecuteAsync(GetAutoNextTaskByIdQuery query)
        {
            var mainQuery = from autoNextTask in EntitySet.Get<AutoNextTaskEntity>()
                            join _tbr in EntitySet.Get<BusinessResultEntity>() on autoNextTask.TaskBusinessResultId equals _tbr.Id into _tempTbr
                            from tbr in _tempTbr.DefaultIfEmpty()
                            join _userPathSelector in EntitySet.Get<UserPathSelectorEntity>() on autoNextTask.AssignedUserPathSelectorId equals _userPathSelector.Id into _tempUserPathSelector
                            from userPathSelector in _tempUserPathSelector.DefaultIfEmpty()
                            join autoCondition in EntitySet.Get<AutoConditionEntity>() on autoNextTask.AutoConditionId equals autoCondition.Id into tempAutoNextTasks
                            from tempAutoNextTask in tempAutoNextTasks.DefaultIfEmpty()
                            join _nextTaskType in EntitySet.Get<TaskTypeEntity>() on autoNextTask.NextTaskId equals _nextTaskType.Id into _tempNextTaskType
                            from nextTaskType in _tempNextTaskType.DefaultIfEmpty()
                            where autoNextTask.Id == query.Id
                            orderby autoNextTask.EventOrder
                            select new AutoNextTaskData
                            {
                                Id = autoNextTask.Id,
                                AutoAction = autoNextTask.AutoAction,
                                TaskBusinessResultId = autoNextTask.TaskBusinessResultId,
                                TaskBusinessResult = tbr.Name,
                                ReferenceObjectId = autoNextTask.ReferenceObjectId,
                                ReferenceType = autoNextTask.ReferenceType,
                                EventOrder = autoNextTask.EventOrder,
                                EventCondition = autoNextTask.EventCondition,
                                NextTaskFormula = autoNextTask.NextTaskFormula,
                                MultiTaskTriggered = autoNextTask.MultiTaskTriggered,
                                AssignedUserPathSelectorId = autoNextTask.AssignedUserPathSelectorId,
                                AssignedUserPathSelector = userPathSelector.Path,
                                AssignedUserPathSelectorName = userPathSelector.Name,
                                AutoConditionId = autoNextTask.AutoConditionId,
                                AutoCondition = tempAutoNextTask.Condition,
                                DynamicFieldConditionId = autoNextTask.DynamicFieldConditionId,
                                NextTaskId = autoNextTask.NextTaskId,
                                NextTaskTypeName = nextTaskType.TaskType,
                                TriggeredEvent = autoNextTask.TriggeredEvent,
                                DynamicFieldConditionValue= autoNextTask.DynamicFieldConditionValue,
                                RequestTicketClosedBusinessResultId = autoNextTask.RequestTicketClosedBusinessResultId,
                                FreeConditionalStatement = autoNextTask.FreeConditionalStatement,
                                WorkflowTaskTypeClosedBusinessResultId = autoNextTask.WorkflowTaskTypeClosedBusinessResultId,
                                TaskConditionName = autoNextTask.TaskConditionName,
                                ObjectApproveAction = autoNextTask.ObjectApproveAction,
                                TaskAssignmentRouting = autoNextTask.TaskAssignmentRouting
                            };
            var result = mainQuery.ToList();
            return new QueryResult<AutoNextTaskData>(result);
        }
    }
}

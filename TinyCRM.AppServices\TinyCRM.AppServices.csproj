﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup Label="Globals">
    <SccProjectName>SAK</SccProjectName>
    <SccProvider>SAK</SccProvider>
    <SccAuxPath>SAK</SccAuxPath>
    <SccLocalPath>SAK</SccLocalPath>
  </PropertyGroup>

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>disable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <None Remove="TinyCRM.AppServices.csproj.vspscc" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\TinyCRM\TinyCRM.csproj" />
    <ProjectReference Include="..\Webaby.Core\Webaby.Core.csproj" />
    <ProjectReference Include="..\Webaby\Webaby.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="RequestTicket\Dto\" />
  </ItemGroup>

</Project>

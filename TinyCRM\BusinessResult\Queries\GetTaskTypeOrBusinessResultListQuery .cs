﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using TinyCRM.TaskType;
using Webaby;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.BusinessResult.Queries
{
    public class GetTaskTypeOrBusinessResultListQuery : QueryBase<ConvertDataResult>
    {
        public Guid? TaskTypeId { get; set; }
    }
    public class LastResult
    {
        public int PageIndex { get; set; }
        public int PageSize { get; set; }
        public int Total { get; set; }
        public bool isHasTaskTypeId { get; set; }
        public List<ConvertDataResult> listdata { get; set; }
    }
    public class ConvertDataResult
    {
        public string Name { get; set; }
        public Guid Id { get; set; }
        public int count { get; set; }
        public bool IsSystem { get; set; }
        public string Code { get; set; }
        public string CodeColor { get; set; }
    }
    public class GetTaskTypeOrBusinessResultListQueryHandler : QueryHandlerBase<GetTaskTypeOrBusinessResultListQuery, ConvertDataResult>
    {
        public GetTaskTypeOrBusinessResultListQueryHandler(IServiceProvider serviceProvider)
            : base(serviceProvider) { }

        public override async Task<QueryResult<ConvertDataResult>> ExecuteAsync(GetTaskTypeOrBusinessResultListQuery query)
        {
            var allBr = (await EntitySet.GetAsync<BusinessResultEntity>()).ToList();
            var allRef = (await EntitySet.GetAsync<BusinessResultReferenceEntity>()).ToList();
            var entityList = new List<ConvertDataResult>().AsQueryable();
            if (query.TaskTypeId != null)
            {
                entityList = (from br in allBr
                              join ttbr in allRef on br.Id equals ttbr.BusinessResultId
                              where ttbr.ReferenceObjectId == query.TaskTypeId && br.ParentId == null
                              orderby br.IsSystem descending, br.DisplayOrder, br.Name 
                              select new ConvertDataResult
                              {
                                  Name = br.Name,
                                  Id = br.Id,
                                  IsSystem = br.IsSystem,
                                  Code = br.Code,
                                  CodeColor = br.CodeColor
                              }).AsQueryable();
            }
            else
            {
                entityList = (from br in allBr
                              join ttbr in allRef on br.Id equals ttbr.BusinessResultId into ttbr_join
                              from ttbr in ttbr_join.DefaultIfEmpty()
                              where br.ParentId == null
                              group new { ttbr, br } by new
                              {
                                  ttbr.BusinessResultId,
                                  br.Name,
                                  br.Id,
                                  br.IsSystem,
                                  br.Code,
                                  CodeColor = br.CodeColor
                              } into g
                              orderby g.Key.IsSystem descending, g.Key.Name
                              select new ConvertDataResult
                              {
                                  Name = g.Key.Name,
                                  Id = g.Key.Id,
                                  IsSystem = g.Key.IsSystem,
                                  Code = g.Key.Code,
                                  CodeColor = g.Key.CodeColor,
                                  count = g.Count(p => p.ttbr != null && p.ttbr.BusinessResultId != null)
                              }).AsQueryable();
            }
            return QueryResult.Create(entityList, query.Pagination);
        }
    }
}
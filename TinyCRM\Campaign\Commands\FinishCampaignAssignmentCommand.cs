﻿using System;
using System.Threading.Tasks;
using TinyCRM.Enums;
using Webaby;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;

namespace TinyCRM.Campaign.Commands
{
    public class FinishCampaignAssignmentCommand : CommandBase
    {
        public Guid CampaignAssignmentId { get; set; }
    }

    internal class FinishCampaignAssignmentCommandHandler : CommandHandlerBase<FinishCampaignAssignmentCommand>
    {
        public FinishCampaignAssignmentCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task ExecuteAsync(FinishCampaignAssignmentCommand command)
        {
            var entity = await EntitySet.GetAsync<CampaignAssignmentEntity>(command.CampaignAssignmentId);
            if (entity == null)
            {
                throw new Exception("Không tìm thấy Campaign Assignment có Id: " + command.CampaignAssignmentId.ToString());
            }

            entity.Status = AssignmentStatus.Done;
            entity.FinishedDate = DateTime.Now;

            await Repository.SaveAsync(entity);
        }
    }
}

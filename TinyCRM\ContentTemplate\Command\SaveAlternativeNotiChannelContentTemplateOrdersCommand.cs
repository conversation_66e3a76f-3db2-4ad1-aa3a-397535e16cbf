﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Text.RegularExpressions;
using Webaby;
using Webaby.Data;
using Webaby.Security;
using Microsoft.Data.SqlClient;

namespace TinyCRM.ContentTemplate.Command
{
    public class SaveAlternativeNotiChannelContentTemplateOrderCommand : CommandBase
    {
        public Guid NotificationChannelSettingId { get; set; }

        public List<AlternativeNotiChannelContentTemplateOrder> AlternativeNotiChannelContentTemplateOrders { get; set; }
    }

    internal class SaveAlternativeNotiChannelContentTemplateOrderCommandHandler : CommandHandlerBase<SaveAlternativeNotiChannelContentTemplateOrderCommand>
    {
        public SaveAlternativeNotiChannelContentTemplateOrderCommandHandler(IServiceProvider serviceProvider, IUserService userService) : base(serviceProvider)
        {
            _userService = userService;
        }
        
        public IUserService _userService { get; set; }

        public override async Task ExecuteAsync(SaveAlternativeNotiChannelContentTemplateOrderCommand command)
        {
            var cmd = EntitySet.CreateDbCommand();
            cmd.Parameters.AddRange(new[]
            {
                DbParameterHelper.AddNullableGuid(cmd,"@NotificationChannelSettingId", command.NotificationChannelSettingId)
            });

            DataTable fieldOrderTable = new DataTable();
            fieldOrderTable.Columns.Add("Id", typeof(Guid));
            fieldOrderTable.Columns.Add("SortOrder", typeof(int));

            foreach (AlternativeNotiChannelContentTemplateOrder FieldOrder in command.AlternativeNotiChannelContentTemplateOrders)
            {
                fieldOrderTable.Rows.Add(FieldOrder.Id, FieldOrder.Order);
            }
            var dynamicFieldOrdersParameter = new SqlParameter();            
            dynamicFieldOrdersParameter.SqlDbType = SqlDbType.Structured;
            dynamicFieldOrdersParameter.TypeName = "dbo.SortIdList";

            cmd.Parameters.Add(dynamicFieldOrdersParameter);
            cmd.CommandText = "dbo.SaveAlternativeNotiChannelContentTemplateOrder";
            cmd.CommandType = CommandType.StoredProcedure;

            await EntitySet.ExecuteNonQueryAsync(cmd);
        }
    }

    public class AlternativeNotiChannelContentTemplateOrder
    {
        public Guid Id { get; set; }

        public int Order { get; set; }
    }
}

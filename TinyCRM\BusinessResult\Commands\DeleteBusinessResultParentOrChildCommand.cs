﻿using AutoMapper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TinyCRM.TaskType;
using Webaby;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.BusinessResult.Commands
{
    public class DeleteBusinessResultParentOrChildCommand : CommandBase
    {
        public Guid BusinessResultId { get; set; }
        public bool isParent { get; set; }
    }

    internal class DeleteBusinessResultParentOrChildCommandHandler : CommandHandlerBase<DeleteBusinessResultParentOrChildCommand>
    {
        public DeleteBusinessResultParentOrChildCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task ExecuteAsync(DeleteBusinessResultParentOrChildCommand command)
        {
            List<IEntity> deletedEntites = new List<IEntity>();
            if (command.isParent == false)
            {
                BusinessResultEntity businessResultEntity = await EntitySet.GetAsync<BusinessResultEntity>(command.BusinessResultId);
                if (businessResultEntity != null)
                {
                    if (businessResultEntity.IsSystem)
                    {
                        throw new InvalidOperationException(T["Đây là kết quả nghiệp vụ của hệ thống, không được xóa"]);
                    }
                    else
                    {
                        await Repository.DeleteAsync(businessResultEntity);
                    }
                }
                List<BusinessResultReferenceEntity> tasktypeBusinessResultEntity = EntitySet.Get<BusinessResultReferenceEntity>().Where(ttbr => ttbr.BusinessResultId == command.BusinessResultId).ToList();
                if (tasktypeBusinessResultEntity.Count != 0)
                {
                    deletedEntites.AddRange(tasktypeBusinessResultEntity);
                    await Repository.DeleteAsync(deletedEntites);
                }
            }
            else
            {
                BusinessResultEntity businessResultEntity = await EntitySet.GetAsync<BusinessResultEntity>(command.BusinessResultId);
                if (businessResultEntity != null)
                {
                    if (businessResultEntity.IsSystem)
                    {
                        throw new InvalidOperationException(T["Đây là kết quả nghiệp vụ của hệ thống, không được xóa"]);
                    }
                    else
                    {
                        await Repository.DeleteAsync(businessResultEntity);
                    }
                }
                List<BusinessResultReferenceEntity> tasktypeBusinessResultEntity = EntitySet.Get<BusinessResultReferenceEntity>().Where(ttbr => ttbr.BusinessResultId == command.BusinessResultId).ToList();
                if (tasktypeBusinessResultEntity.Count != 0)
                {
                    deletedEntites.AddRange(tasktypeBusinessResultEntity);
                }
                List<BusinessResultEntity> listBusinessResultEntity = EntitySet.Get<BusinessResultEntity>().Where(br => br.ParentId == command.BusinessResultId).ToList();
                if (listBusinessResultEntity.Count != 0)
                {
                    bool existsSystem = listBusinessResultEntity.Any(x => x.IsSystem == true);
                    if (existsSystem)
                    {
                        throw new InvalidOperationException(T["Có tồn tại kết quả nghiệp vụ của hệ thống, không được xóa"]);
                    }
                    else
                    {
                        deletedEntites.AddRange(listBusinessResultEntity);
                        foreach (var item in listBusinessResultEntity)
                        {
                            List<BusinessResultReferenceEntity> entity = EntitySet.Get<BusinessResultReferenceEntity>().Where(ttbr => ttbr.BusinessResultId == item.Id).ToList();
                            deletedEntites.AddRange(entity);
                        }
                    }
                }
                await Repository.DeleteAsync(deletedEntites);
            }
        }
    }
}
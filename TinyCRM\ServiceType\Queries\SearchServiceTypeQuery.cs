﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;
using Microsoft.Data.SqlClient;

namespace TinyCRM.ServiceType.Queries
{
    public class SearchServiceTypeQuery : QueryBase<ServiceTypeData>
    {
        public Guid? Level1Id { get; set; }

        public Guid? Level2Id { get; set; }

        public Guid? Level3Id { get; set; }

        public Guid? Level4Id { get; set; }

        public bool IsSearchDeletedServiceType { get; set; }

        public string Code { get; set; }
        public List<Guid> RoleIds { get; set; }
        public bool ShowRoleList { get; set; } // chú ý: ShowRoleList=True -> kết quả sẽ bao gồm roleName và bị duplicate, cần group lại kêt quả


        public Guid? AddToRoleId { get; set; }
        public List<Guid> SelectedIds { get; set; }
        public int HandleRoleAction { get; set; }
    }

    internal class SearchServiceTypeQueryHandler : QueryHandlerBase<SearchServiceTypeQuery, ServiceTypeData>
    {
        public SearchServiceTypeQueryHandler(IEntitySet entitySet, IRepository repository, IText text, IMapper mapper)
            : base(entitySet, repository, text, mapper) { }

        public override async Task<QueryResult<ServiceTypeData>> ExecuteAsync(SearchServiceTypeQuery query)
        {
            int startRow = query.Pagination.Index * query.Pagination.Size + 1;
            int endRow = query.Pagination.Index * query.Pagination.Size + query.Pagination.Size;
            query.RoleIds = query.RoleIds ?? new List<Guid>();
            query.SelectedIds = query.SelectedIds ?? new List<Guid>();

            var cmd = EntitySet.CreateDbCommand();
            //Chưa đưa method này AddDataAuthorizedParameters vào core
            //cmd.AddDataAuthorizedParameters();
            DbParameterHelper.AddNullableGuid(cmd, "@level1Id", query.Level1Id);
            DbParameterHelper.AddNullableGuid(cmd, "@level2Id", query.Level2Id);
            DbParameterHelper.AddNullableGuid(cmd, "@level3Id", query.Level3Id);
            DbParameterHelper.AddNullableGuid(cmd, "@level4Id", query.Level4Id);
            DbParameterHelper.AddNullableGuid(cmd, "@addToRoleId", query.AddToRoleId);
            DbParameterHelper.AddNullableString(cmd, "@Code", query.Code);
            DbParameterHelper.NewNullableBooleanParameter(cmd, "@isShowDeletedServiceType", query.IsSearchDeletedServiceType);
            cmd.Parameters.AddRange(new[]
            {
                new SqlParameter("@roleIds", SqlDbType.Structured)
                {
                    Value = query.RoleIds.Select(x => new { Id = x }).ToDataTable(),
                    TypeName = "dbo.IdList"
                },
                new SqlParameter("@showRoleList", query.ShowRoleList),

                new SqlParameter("@selectedIds", SqlDbType.Structured)
                {
                    Value = query.SelectedIds.Select(x => new { Id = x }).ToDataTable(),
                    TypeName = "dbo.IdList"
                },
                new SqlParameter("@handleRoleAction", query.HandleRoleAction),

                new SqlParameter("@startRow", startRow),
                new SqlParameter("@endRow", endRow), 
            });

            cmd.CommandText = "SearchServiceType";
            cmd.CommandType = CommandType.StoredProcedure;
            var mainQuery = await EntitySet.ExecuteReadCommandAsync<ServiceTypeData>(cmd);
            return new QueryResult<ServiceTypeData>(mainQuery);
        }
    }
}

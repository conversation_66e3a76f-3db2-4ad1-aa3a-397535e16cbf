﻿IF NOT EXISTS (SELECT * FROM dbo.BusinessPermission WHERE Id = '59E9CF0A-C21A-4077-AE54-790B345D3C72') BEGIN
	INSERT INTO [BusinessPermission]
	(
		[Id],
		[Name],
		[ParentId],
		[CreatedDate],
		[CreatedBy],
		[ModifiedDate],
		[ModifiedBy],
		[Deleted],
		[DeletedDate],
		[DeletedBy],
		[Order],
		[DisplayOnly],
		[OrganizationBasedEnabled],
		[CreatorBasedEnabled],
		[OwnerBasedEnabled]
	)
	VALUES
	(	
		'59E9CF0A-C21A-4077-AE54-790B345D3C72', 
		N'Thông tin Email đến KH (trong tác vụ)',
		'DA932A21-33E9-4E59-8899-A7794E355C29', 
		GETDATE(), 
		'00000000-1111-2222-3333-444444444444', 
		NULL,
		NULL, 
		0, 
		NULL,
		NULL, 
		101, 
		0, 
		0, 
		0, 
		0
	)

	
END


DELETE dbo.AccessBusinessPermission WHERE BusinessPermissionId = '59E9CF0A-C21A-4077-AE54-790B345D3C72'
	INSERT INTO dbo.AccessBusinessPermission
			( 
				Id,
				AccessId,
				BusinessPermissionId,
				CreatedDate,
				CreatedBy,
				Deleted
			)
	SELECT	NEWID(), Id AccessId, '59E9CF0A-C21A-4077-AE54-790B345D3C72', GETDATE(), '00000000-1111-2222-3333-444444444444', 0
	FROM	dbo.Accesses
	WHERE	ControllerName = 'Task'
			AND ActionName = 'OpenTaskContentTemplate'

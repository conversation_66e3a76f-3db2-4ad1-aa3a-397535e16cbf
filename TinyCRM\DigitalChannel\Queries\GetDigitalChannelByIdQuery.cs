﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;

namespace TinyCRM.DigitalChannel.Queries
{
    public class GetDigitalChannelByIdQuery : QueryBase<DigitalChannelData>
    {
        public Guid Id { get; set; }
    }

    internal class GetDigitalChannelByIdQueryHandler : QueryHandlerBase<GetDigitalChannelByIdQuery, DigitalChannelData>
    {
        public GetDigitalChannelByIdQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<DigitalChannelData>> ExecuteAsync(GetDigitalChannelByIdQuery query)
        {
            var channelEntities = await EntitySet.GetAsync<DigitalChannelEntity>();
            var filtered = channelEntities.Where(x => x.Id == query.Id);
            return QueryResult.Create(filtered, x => Mapper.Map<DigitalChannelData>(x));
        }
    }
}
﻿using System;
using System.Data;
using System.Data.SqlClient;
using TinyCRM.Enums;
using Webaby;
using Webaby.Data;
using Webaby.Security;

namespace TinyCRM.Customer.Queries
{
    public class SearchCustomersQuery : QueryBase<CustomerListItem>
    {
        public string Code { get; set; }

        public string PhoneNumber { get; set; }

        public string Email { get; set; }

        public string Name { get; set; }

        public CustomerType? Type { get; set; }

        public string B2BCode { get; set; }

        public string ContactPhone { get; set; }

        public string FacebookId { get; set; }

        public string WorkAddress { get; set; }

        public string Job { get; set; }

        public string Notes { get; set; }

        public string SubName { get; set; }

        public string Address { get; set; }

        public bool? CustomerClass { get; set; }

        public int? CreditLimit { get; set; }

        public Gender? Sex { get; set; }

        public DateTime? Dob { get; set; }

        public string TaxNumber { get; set; }

        public string LicenseType { get; set; }

        public string License { get; set; }

        public DateTime? LicenseDate { get; set; }

        public string Avatar { get; set; }

        public string Background { get; set; }

        public DateTime? LicenseExpired { get; set; }

        public string LicensePlace { get; set; }

        public string OriginNation { get; set; }

        public string Nation { get; set; }

        public string BankId { get; set; }

        public string LocationId { get; set; }

        public string Residence { get; set; }

        public string Status { get; set; }

        public string CMND { get; set; }

        public Guid? SourceClassificationId { get; set; }

        public bool? IsBackendCustomer { get; set; }

        public PrimaryPartCustomer? IsPrimary { get; set; }

        public bool IsDisabled { get; set; }

        public string CustomerVersion { get; set; }

        public string DfoThreadId { get; set; }
        public string DfoUserId { get; set; }
    }

    internal class SearchCustomersQueryHandler : QueryHandlerBase<SearchCustomersQuery, CustomerListItem>
    {
        IUserService _userService { get; set; }
        public SearchCustomersQueryHandler(IServiceProvider serviceProvider, IUserService userService) : base(serviceProvider) { _userService = userService; }

        public override async Task<QueryResult<CustomerListItem>> ExecuteAsync(SearchCustomersQuery query)
        {
            int startRow = query.Pagination.Index * query.Pagination.Size + 1;
            int endRow = query.Pagination.Index * query.Pagination.Size + query.Pagination.Size;

            string name = string.Empty;
            if (query.Name.IsNotNullOrEmpty())
            {
                string tempName = query.Name.Trim(' ').Replace(",", "").Replace(";", "");
                while (tempName.Contains("  "))
                {
                    tempName = tempName.Replace("  ", " ");
                }

                name = tempName.Replace(" ", " AND ");
            }

            string address = string.Empty;
            if (query.Address.IsNotNullOrEmpty())
            {
                string tempAddress = query.Address.Trim(' ').Replace(",", "").Replace(";", "");
                while (tempAddress.Contains("  "))
                {
                    tempAddress = tempAddress.Replace("  ", " ");
                }
                address = tempAddress.Replace(" ", " AND ");
            }

            var cmd = EntitySet.CreateDbCommand();
            cmd.AddDataAuthorizedParameters(_userService);
            cmd.Parameters.AddRange(new[]
            {
                DbParameterHelper.AddNullableString(cmd, "@Name", name),
                DbParameterHelper.AddNullableString(cmd, "@Code", query.Code),
                DbParameterHelper.AddNullableString(cmd, "@PhoneNumber", query.PhoneNumber),
                DbParameterHelper.AddNullableString(cmd, "@Email", query.Email),
                DbParameterHelper.AddNullableEnum(cmd, "@Type", query.Type),
                DbParameterHelper.AddNullableString(cmd,"@B2BCode", query.B2BCode),
                DbParameterHelper.AddNullableString(cmd,"@ContactPhone", query.ContactPhone),
                DbParameterHelper.AddNullableString(cmd,"@FacebookId", query.FacebookId),
                DbParameterHelper.AddNullableString(cmd,"@WorkAddress", query.WorkAddress),
                DbParameterHelper.AddNullableString(cmd,"@Job", query.Job),
                DbParameterHelper.AddNullableString(cmd,"@Notes", query.Notes),
                DbParameterHelper.AddNullableString(cmd,"@SubName", query.SubName),
                DbParameterHelper.NewNullableBooleanParameter(cmd, "@CustomerClass", query.CustomerClass),
                DbParameterHelper.AddNullableInt(cmd, "@CreditLimit", query.CreditLimit),
                DbParameterHelper.AddNullableEnum(cmd,"@Sex", query.Sex),
                DbParameterHelper.NewNullableDateTimeParameter(cmd, "@Dob", query.Dob),
                DbParameterHelper.AddNullableString(cmd, "@TaxNumber", query.TaxNumber),
                DbParameterHelper.AddNullableString(cmd, "@LicenseType", query.LicenseType),
                DbParameterHelper.AddNullableString(cmd, "@License", query.License),
                DbParameterHelper.NewNullableDateTimeParameter(cmd, "@LicenseDate", query.LicenseDate),
                DbParameterHelper.AddNullableString(cmd, "@Avatar", query.Avatar),
                DbParameterHelper.AddNullableString(cmd, "@Background", query.Background),
                DbParameterHelper.NewNullableDateTimeParameter(cmd, "@LicenseExpired", query.LicenseExpired),
                DbParameterHelper.AddNullableString(cmd,"@LicensePlace", query.LicensePlace),
                DbParameterHelper.AddNullableString(cmd,"@OriginNation", query.OriginNation),
                DbParameterHelper.AddNullableString(cmd,"@Nation", query.Nation),
                DbParameterHelper.AddNullableString(cmd,"@BankId", query.BankId),
                DbParameterHelper.AddNullableString(cmd,"@LocationId", query.LocationId),
                DbParameterHelper.AddNullableString(cmd,"@Residence", query.Residence),
                DbParameterHelper.AddNullableString(cmd,"@Status", query.Status),
                DbParameterHelper.AddNullableString(cmd,"@CMND", query.CMND),
                DbParameterHelper.AddNullableString(cmd,"@Address", address),
                DbParameterHelper.AddNullableString(cmd,"@CustomerVersion", query.CustomerVersion),
                DbParameterHelper.AddNullableString(cmd,"@DfoUserId", query.DfoUserId),
                DbParameterHelper.AddNullableString(cmd,"@DfoThreadId", query.DfoThreadId),
                DbParameterHelper.AddNullableGuid(cmd, "@SourceClassificationId", query.SourceClassificationId),
                DbParameterHelper.NewNullableBooleanParameter(cmd, "@IsBackendCustomer", query.IsBackendCustomer),
                DbParameterHelper.NewNullableBooleanParameter(cmd, "@IsDisabled", query.IsDisabled),
                DbParameterHelper.AddNullableEnum(cmd,"@IsPrimary", query.IsPrimary),
                DbParameterHelper.AddNullableInt(cmd,"@StartRow", startRow),
                DbParameterHelper.AddNullableInt(cmd,"@EndRow", endRow)
            });

            cmd.CommandText = "dbo.SearchCustomers";
            cmd.CommandType = CommandType.StoredProcedure;

            var mainQuery = await EntitySet.ExecuteReadCommandAsync<CustomerListItem>(cmd);
            return new QueryResult<CustomerListItem>(mainQuery);
        }
    }
}
﻿using AutoMapper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TinyCRM.BusinessResult.Queries;
using TinyCRM.TaskType;
using Webaby;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.BusinessResult.Commands
{
    public class EditColorBusinessResultCommand : CommandBase
    {
        public List<BusinessResultData> listedit { get; set; }
    }

    internal class EditColorBusinessResultCommandHandler : CommandHandlerBase<EditColorBusinessResultCommand>
    {
        public EditColorBusinessResultCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task ExecuteAsync(EditColorBusinessResultCommand command)
        {
            var savedEntity = new List<IEntity>();
            foreach(var item in command.listedit)
            {
                BusinessResultEntity businessresultentity = await EntitySet.GetAsync<BusinessResultEntity>(item.Id);
                businessresultentity.CodeColor = item.CodeColor;
                savedEntity.Add(businessresultentity);
            }
            await Repository.SaveAsync(savedEntity);
        }
    }
}
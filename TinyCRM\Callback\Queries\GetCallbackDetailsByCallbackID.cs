﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Data.SqlTypes;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;
using Webaby.Localization;
using AutoMapper;

namespace TinyCRM.Callback.Queries
{
    public class GetCallbackDetailsByCallbackIDQuery : QueryBase<CallbackDetailItem>
    {
        public Guid CallbackID { get; set; }
    }

    internal class GetCallbackDetailsByCallbackIDHandler : QueryHandlerBase<GetCallbackDetailsByCallbackIDQuery, CallbackDetailItem>
    {
        public GetCallbackDetailsByCallbackIDHandler(IServiceProvider serviceProvider)
            : base(serviceProvider) { }

        public override async Task<QueryResult<CallbackDetailItem>> ExecuteAsync(GetCallbackDetailsByCallbackIDQuery query)
        {
            var cmd = EntitySet.CreateDbCommand();
            int? StartRow = 1;
            int? EndRow = Int32.MaxValue;
            cmd.Parameters.Add(cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@CallbackId", query.CallbackID)));
            cmd.Parameters.Add(cmd.Parameters.Add(DbParameterHelper.NewNullableDateTimeParameter(cmd, "@FromDate", null)));
            cmd.Parameters.Add(cmd.Parameters.Add(DbParameterHelper.NewNullableDateTimeParameter(cmd, "@ToDate", null)));
            cmd.Parameters.Add(cmd.Parameters.Add(DbParameterHelper.AddNullableString(cmd, "@AgentID", string.Empty)));
            cmd.Parameters.Add(cmd.Parameters.Add(DbParameterHelper.AddNullableString(cmd, "@Status", string.Empty)));
            cmd.Parameters.Add(cmd.Parameters.Add(DbParameterHelper.AddNullableString(cmd, "@Result", string.Empty)));
            cmd.Parameters.Add(cmd.Parameters.Add(DbParameterHelper.AddNullableInt(cmd, "@StartRow", StartRow)));
            cmd.Parameters.Add(cmd.Parameters.Add(DbParameterHelper.AddNullableInt(cmd, "@EndRow", EndRow)));

            cmd.CommandText = "SearchCallbackDetails";
            cmd.CommandType = CommandType.StoredProcedure;
            var mainQuery = await EntitySet.ExecuteReadCommandAsync<CallbackDetailItem>(cmd);
            return new QueryResult<CallbackDetailItem>(mainQuery);
        }
    }
}
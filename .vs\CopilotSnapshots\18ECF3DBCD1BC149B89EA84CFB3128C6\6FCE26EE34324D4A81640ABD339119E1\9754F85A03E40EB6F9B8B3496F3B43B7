﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Webaby.Data;
using Webaby.Localization;
using Webaby;
using System;
using System.Threading.Tasks;

namespace TinyCRM.Outbound.CallResult.Queries
{
    public class GetResultCodeSuiteByIdQuery : QueryBase<ResultCodeSuiteData>
    {
        public Guid Id { get; set; }
    }

    internal class GetResultCodeSuiteByIdQueryHandler : QueryHandlerBase<GetResultCodeSuiteByIdQuery, ResultCodeSuiteData>
    {
        public GetResultCodeSuiteByIdQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<ResultCodeSuiteData>> ExecuteAsync(GetResultCodeSuiteByIdQuery query)
        {
            var suiteResult = await EntitySet.GetAsync<ResultCodeSuiteEntity>(query.Id);
            if (suiteResult == null) throw new InvalidOperationException(T["Không tìm  thấy bộ ResultCode có id '{0}'", query.Id]);
            return new QueryResult<ResultCodeSuiteData>(Mapper.Map<ResultCodeSuiteData>(suiteResult));
        }
    }
}
﻿using AutoMapper;
using System;
using TinyCRM.Survey;
using Webaby;
using Webaby.Data;

namespace TinyCRM.Campaign.Queries
{
    public class CampaignInfo
    {
        public Guid CampaignId { get; set; }

        public string SurveyCampaignName { get; set; }

        public string SurveyName { get; set; }

        public SurveyTarget SurveyTarget { get; set; }

        public DateTime? StartDate { get; set; }

        public DateTime? EndDate { get; set; }
    }
}
﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using AutoMapper;
using Webaby;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.AutomaticTask.Queries
{
    public class GetUserAssignmentByRoutingQuery : QueryBase<Guid>
    {
        public Guid OrganizationId { get; set; }
        public string AssignmentType { get; set; }
        public Guid RequestTicketId { get; set; }
        public Guid? TaskTypeId { get; set; }
    }

    internal class GetUserAssignmentByRoutingQueryHandler : QueryHandlerBase<GetUserAssignmentByRoutingQuery, Guid>
    {
        public GetUserAssignmentByRoutingQueryHandler(IServiceProvider serviceProvider)
            : base(serviceProvider) { }

        public override async Task<QueryResult<Guid>> ExecuteAsync(GetUserAssignmentByRoutingQuery query)
        {
            var cmd = EntitySet.CreateDbCommand();
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@OrganizationId", query.OrganizationId));
            cmd.Parameters.Add(DbParameterHelper.AddNullableString(cmd, "@AssignmentType", query.AssignmentType));
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@RequestTicketId", query.RequestTicketId));
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@TaskTypeId", query.TaskTypeId));                        
            cmd.CommandText = "dbo.GetUserAssignmentByRouting";
            cmd.CommandType = CommandType.StoredProcedure;
            var mainQuery = await EntitySet.ExecuteReadCommandAsync<Guid>(cmd);
            return new QueryResult<Guid>(mainQuery);
        }
    }
}

﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;
using System.Threading.Tasks;
using TinyCRM.Geolocation;
using TinyCRM.Survey.Queries;
using Webaby;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.Customer.Queries
{
    public class GetCustomerByIdQuery : QueryBase<CustomerData>
    {
        public Guid Id { get; set; }
    }

    internal class GetCustomerByIdQueryHandler : QueryHandlerBase<GetCustomerByIdQuery, CustomerData>
    {
        public GetCustomerByIdQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<CustomerData>> ExecuteAsync(GetCustomerByIdQuery query)
        {
            var customerData = await (from cus in EntitySet.Get<CustomerEntity>()
                               join cls in EntitySet.Get<ClassificationEntity>() on cus.SourceClassificationId equals cls.Id into clsLeft
                               from clsX in clsLeft.DefaultIfEmpty()
                               join geoRe in EntitySet.Get<GeolocationEntity>() on cus.RegionId equals geoRe.Id into geoReLeft
                               from geoReX in geoReLeft.DefaultIfEmpty()
                               join geoAr in EntitySet.Get<GeolocationEntity>() on cus.AreaId equals geoAr.Id into geoArLeft
                               from geoArX in geoArLeft.DefaultIfEmpty()
                               join geoPr in EntitySet.Get<GeolocationEntity>() on cus.ProvinceId equals geoPr.Id into geoPrLeft
                               from geoPrX in geoPrLeft.DefaultIfEmpty()
                               join geoDi in EntitySet.Get<GeolocationEntity>() on cus.DistrictId equals geoDi.Id into geoDiLeft
                               from geoDiX in geoDiLeft.DefaultIfEmpty()
                               join geoWa in EntitySet.Get<GeolocationEntity>() on cus.WardId equals geoWa.Id into geoWaLeft
                               from geoWaX in geoWaLeft.DefaultIfEmpty()
                               where cus.Id == query.Id
                               select new CustomerData
                               {
                                   Id = cus.Id,
                                   Code = cus.Code,
                                   B2BCode = cus.B2BCode,
                                   Type = cus.Type,
                                   Name = cus.Name,
                                   SubName = cus.SubName,
                                   CompanyType = cus.CompanyType,
                                   CustomerClass = cus.CustomerClass,
                                   Sex = cus.Sex,
                                   Dob = cus.Dob,
                                   Job = cus.Job,
                                   CMND = cus.CMND,

                                   RegionId = cus.RegionId,
                                   RegionName = geoReX.Name,
                                   AreaId = cus.AreaId,
                                   AreaName = geoArX.Name,
                                   ProvinceId = cus.ProvinceId,
                                   ProvinceName = geoPrX.Name,
                                   DistrictId = cus.DistrictId,
                                   DistrictName = geoDiX.Name,
                                   WardId = cus.WardId,
                                   WardName = geoWaX.Name,
                                   Address = cus.Address,
                                   AddressStreet = cus.AddressStreet,
                                   AddressNumber = cus.AddressNumber,
                                   Phone1 = cus.Phone1,
                                   Phone2 = cus.Phone2,
                                   Phone3 = cus.Phone3,                                   
                                   Email = cus.Email,
                                   FacebookId = cus.FacebookId,
                                   Notes = cus.Notes,

                                   WorkAddress = cus.WorkAddress,
                                   ContactPhone = cus.ContactPhone,
                                   SourceClassificationId = cus.SourceClassificationId,
                                   SourceClassification = clsX.Name,
                                   
                                   TaxNumber = cus.TaxNumber,
                                   CreditLimit = cus.CreditLimit,
                                   LicenseType = cus.LicenseType,
                                   License = cus.License,
                                   LicenseDate = cus.LicenseDate,
                                   Avatar = cus.Avatar,
                                   Background = cus.Background,
                                   LicenseExpire = cus.LicenseExpire,
                                   LicensePlace = cus.LicensePlace,
                                   OriginNation = cus.OriginNation,
                                   Nation = cus.Nation,
                                   BankID = cus.BankID,
                                   LocationID = cus.LocationID,
                                   Residence = cus.Residence,
                                   Status = cus.Status,
                                   CompanyAddress  = cus.CompanyAddress,    
                                   CompanyName = cus.CompanyName,
                                   CompanyPhone = cus.CompanyPhone, 
                                   QualifiedProgram = cus.QualifiedProgram,
                                   IncomeSource = cus.IncomeSource,
                                   MaritalStatus = cus.MaritalStatus,
                                   IsBackendCustomer = cus.IsBackendCustomer,

                                   AdditionalTemplateId = cus.AdditionalTemplateId,
                                   AdditionalData = cus.AdditionalData,
                                   LinkedCustomerId = cus.LinkedCustomerId
                               }).FirstOrDefaultAsync();
            return new QueryResult<CustomerData>(customerData);
        }
    }
}

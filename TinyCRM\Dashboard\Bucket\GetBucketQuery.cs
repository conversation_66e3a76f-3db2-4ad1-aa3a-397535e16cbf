﻿using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using TinyCRM.Campaign.Queries;
using TinyCRM.DigitalChannel;
using Webaby;
using Webaby.Core.Organization;
using Webaby.Core.UserAccount.Queries;
using Webaby.Data;
using Webaby.Localization;
using Webaby.Security;

namespace TinyCRM.Dashboard.Bucket
{
    public class GetBucketQuery : QueryBase<BucketItem>
    {
        public Guid CurrentUserId { get; set; }
        public Action<string> Error { get; set; }

        internal class QueryHandler : QueryHandlerBase<GetBucketQuery, BucketItem>
        {
            IConfiguration _configuration { get; set; }
            IText _text { get; set; }
            public QueryHandler(IServiceProvider serviceProvider, IConfiguration configuration, IText text) : base(serviceProvider) { _configuration = configuration; _text = text; }

            public override async Task<QueryResult<BucketItem>> ExecuteAsync(GetBucketQuery query)
            {
                var configString = _configuration.GetValue<string>("dashboard.bucket");
                if (configString.IsNullOrEmpty())
                {
                    return new QueryResult<BucketItem>(Array.Empty<BucketItem>());
                } 
                var configObject = Newtonsoft.Json.JsonConvert.DeserializeObject<BucketItem[]>(configString);

                var regex = new Regex("(?<n>[a-zA-Z_][a-zA-Z_0-9]*)(\\((?<a>-?[0-9]+)\\))?", RegexOptions.Compiled);

                foreach (var item in configObject)
                {
                    var dateRange = BucketTime.Parse(item.TimeRange, query.Error,_text) ?? BucketTime.ThisMonth(DateTime.Now);

                    var fieldBulder = item.Details.Select(x => $"{x.Expression} [{x.Label}]");

                    var command = EntitySet.CreateDbCommand();
                    command.Parameters.Add(DbParameterHelper.NewNullableDateTimeParameter(command, "@TimeFrom", dateRange.Start));
                    command.Parameters.Add(DbParameterHelper.NewNullableDateTimeParameter(command, "@TimeTo", dateRange.End));
                    command.Parameters.Add(DbParameterHelper.AddNullableString(command, "@ServiceTypeId", item.ServiceTypeId));
                    command.Parameters.Add(DbParameterHelper.AddNullableGuid(command, "@CurrentUserId", query.CurrentUserId));
                    command.Parameters.Add(DbParameterHelper.AddNullableString(command, "@SelectList", string.Join(",", fieldBulder)));                    
                    command.CommandText = "dbo.GetBucketDashboard";
                    command.CommandType = CommandType.StoredProcedure;

                    item.Data = (await EntitySet.ExecuteReadCommandAsync(command)).Tables[0];
                }
                return new QueryResult<BucketItem>(configObject);
            }
        }
    }
}

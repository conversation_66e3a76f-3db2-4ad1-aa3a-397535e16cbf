﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TinyCRM.Callback;
using TinyCRM.Callback.Queries;
using Webaby;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;

namespace TinyCRM.Callback.Queries
{
    public class SearchCallbackDetailsQuery : QueryBase<CallbackDetailItem>
    {
        public Guid CallbackId { get; set; }

        public DateTime? FromDate { get; set; }

        public DateTime? ToDate { get; set; }

        public string AgentId { get; set; }

        public string Status { get; set; }

        public string Result { get; set; }
    }

    internal class SearchCallbackDetailsQueryHandler : QueryHandlerBase<SearchCallbackDetailsQuery, CallbackDetailItem>
    {
        public SearchCallbackDetailsQueryHandler(IServiceProvider serviceProvider)
            : base(serviceProvider) { }

        public override async Task<QueryResult<CallbackDetailItem>> ExecuteAsync(SearchCallbackDetailsQuery query)
        {
            var cmd = EntitySet.CreateDbCommand();
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@CallbackId", query.CallbackId));
            cmd.Parameters.Add(DbParameterHelper.NewNullableDateTimeParameter(cmd, "@FromDate", query.FromDate));
            cmd.Parameters.Add(DbParameterHelper.NewNullableDateTimeParameter(cmd, "@ToDate", query.ToDate));
            cmd.Parameters.Add(DbParameterHelper.AddNullableString(cmd, "@AgentID", query.AgentId.IsNullOrEmpty() ? string.Empty : query.AgentId));
            cmd.Parameters.Add(DbParameterHelper.AddNullableString(cmd, "@Status", query.Status.IsNullOrEmpty() ? string.Empty : query.Status));
            cmd.Parameters.Add(DbParameterHelper.AddNullableString(cmd, "@Result", query.Result.IsNullOrEmpty() ? string.Empty : query.Result));
            cmd.Parameters.Add(DbParameterHelper.AddNullableInt(cmd, "@StartRow", query.Pagination.StartRow));
            cmd.Parameters.Add(DbParameterHelper.AddNullableInt(cmd, "@EndRow", query.Pagination.EndRow));

            cmd.CommandText = "SearchCallbackDetails";
            cmd.CommandType = CommandType.StoredProcedure;
            var mainQuery = await EntitySet.ExecuteReadCommandAsync<CallbackDetailItem>(cmd);
            return new QueryResult<CallbackDetailItem>(mainQuery);
        }
    }
}
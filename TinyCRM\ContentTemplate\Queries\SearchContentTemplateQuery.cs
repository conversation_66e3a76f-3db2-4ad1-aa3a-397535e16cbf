﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;

namespace TinyCRM.ContentTemplate.Queries
{
    public class SearchContentTemplateQuery : QueryBase<SearchContentTemplateQuery.Result>
    {
        public class Result
        {
            public ContentTemplateEntity ContentTemplateItem { get; set; }
        }
        public Guid? Id { get; set; }
        public string Name { get; set; }
    }

    internal class SearchContentTemplateQueryHandler : QueryHandlerBase<SearchContentTemplateQuery, SearchContentTemplateQuery.Result>
    {
        public SearchContentTemplateQueryHandler(IServiceProvider serviceProvider)
            : base(serviceProvider) { }

        public override async Task<QueryResult<SearchContentTemplateQuery.Result>> ExecuteAsync(SearchContentTemplateQuery query)
        {
            var result = EntitySet.Get<ContentTemplateEntity>();
            if (query.Id.HasValue)
            {
                result = result.Where(x => x.Id == query.Id);
            }
            if (!string.IsNullOrWhiteSpace(query.Name))
            {
                result = result.Where(x => x.Name.Contains(query.Name));
            }
            return QueryResult.Create(result.Select(x => new SearchContentTemplateQuery.Result { ContentTemplateItem = x }), query.Pagination);
        }
    }
}

﻿using System;
using Webaby;
using System.Linq;
using Webaby.Core.DynamicForm;
using Webaby.Core.File;

namespace TinyCRM.Outbound.Campaign.Queries
{
    public class GetCampaignByDynamicFormIdQuery : QueryBase<CampaignData>
    {
        public Guid DynamicFormId { get; set; }
    }

    internal class GetCampaignByDynamicFormIdQueryHandler : QueryHandlerBase<GetCampaignByDynamicFormIdQuery, CampaignData>
    {
        public override QueryResult<CampaignData> Execute(GetCampaignByDynamicFormIdQuery query)
        {
            var campaign = EntitySet.Get<CampaignEntity>().Where(x => x.DynamicFormId == query.DynamicFormId).FirstOrDefault();
            var campaignData = CampaignData.FromEntity(campaign);
            return new QueryResult<CampaignData>(campaignData);
        }
    }
}
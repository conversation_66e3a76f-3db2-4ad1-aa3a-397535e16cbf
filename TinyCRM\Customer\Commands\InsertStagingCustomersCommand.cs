﻿using System;
using System.Data;
using System.Data.SqlClient;
using AutoMapper;
using TinyCRM.Enums;
using Webaby;
using Webaby.Data;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text.RegularExpressions;
using Webaby.Validation.PhoneNumber;
using System.Threading.Tasks;
using Webaby.Localization;

namespace TinyCRM.Customer.Commands
{
    public class InsertStagingCustomersCommand : CommandBase
    {
        public Guid ImportSessionId { get; set; }

        public DataTable SheetData { get; set; }
    }

    internal class InsertStagingCustomersCommandHandler : CommandHandlerBase<InsertStagingCustomersCommand>
    {
        public InsertStagingCustomersCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task ExecuteAsync(InsertStagingCustomersCommand command)
        {
            const string idCoumnName = "Id";
            const string importSessionIdCoumnName = "ImportSessionId";
            const string importStatusCoumnName = "ImportStatus";

            var infoColumnMapping = new dynamic[] {

                new { DbColumn = "ClassificationChannel", FileColumnIndex = 3},
                new { DbColumn = "CustomerCode", FileColumnIndex = 4},
                new { DbColumn = "CustomerName", FileColumnIndex = 5},
                new { DbColumn = "CustomerAddress", FileColumnIndex = 6},
                new { DbColumn = "CustomerWard", FileColumnIndex = 7},
                new { DbColumn = "CustomerDistrict", FileColumnIndex = 8},
                new { DbColumn = "CustomerProvince", FileColumnIndex = 9},
                new { DbColumn = "CustomerPhone1", FileColumnIndex = 10},
                new { DbColumn = "CustomerPhone2", FileColumnIndex = 11},
                new { DbColumn = "CustomerEmail", FileColumnIndex = 12},
                new { DbColumn = "CustomerFacebookId", FileColumnIndex = 13},

                new { DbColumn = "CustomerBillingCode", FileColumnIndex = 14},

                new { DbColumn = "AlternativeAddressCode", FileColumnIndex = 15},
                new { DbColumn = "AlternativeAddressName", FileColumnIndex = 16},
                new { DbColumn = "AlternativeAddressAddress", FileColumnIndex = 17},
                new { DbColumn = "AlternativeAddressWard", FileColumnIndex = 18},
                new { DbColumn = "AlternativeAddressDistrict", FileColumnIndex = 19},
                new { DbColumn = "AlternativeAddressProvince", FileColumnIndex = 20},
                new { DbColumn = "AlternativeAddressPhone", FileColumnIndex = 21},
                new { DbColumn = "ClassificationCode", FileColumnIndex = 22},
                new { DbColumn = "ClassificationName", FileColumnIndex = 23}
            };

            var dataTable = new DataTable();
            dataTable.Columns.Add(idCoumnName, typeof(Guid));
            dataTable.Columns.Add(importSessionIdCoumnName, typeof(Guid));
            dataTable.Columns.Add(importStatusCoumnName, typeof(int));
            foreach (var mapping in infoColumnMapping)
            {
                dataTable.Columns.Add(mapping.DbColumn);
            }

            CheckPhoneValidResult checkPhone = new CheckPhoneValidResult();
            for (int i = 1; i < command.SheetData.Rows.Count; i++)
            {
                DataRow inputDataRow = dataTable.NewRow();

                DataRow dr = command.SheetData.Rows[i];

                if (dr[0].ToString().Trim() == string.Empty && dr[1].ToString().Trim() == string.Empty && dr[2].ToString().Trim() == string.Empty)
                {
                    continue;
                }

                Guid dataRowId = Guid.NewGuid();

                inputDataRow[idCoumnName] = dataRowId;
                inputDataRow[importSessionIdCoumnName] = command.ImportSessionId;
                inputDataRow[importStatusCoumnName] = 0;

                inputDataRow["ClassificationChannel"] = dr[0].ToString().Trim().Replace("#N/A", "").Replace("#n/a", "").Replace("N/A", "").Replace("n/a", "");
                inputDataRow["CustomerCode"] = dr[1].ToString().Trim().Replace("#N/A", "").Replace("#n/a", "").Replace("N/A", "").Replace("n/a", "");
                inputDataRow["CustomerName"] = dr[2].ToString().Trim().Replace("#N/A", "").Replace("#n/a", "").Replace("N/A", "").Replace("n/a", "");
                inputDataRow["CustomerAddress"] = dr[3].ToString().Trim().Replace("#N/A", "").Replace("#n/a", "").Replace("N/A", "").Replace("n/a", "");
                inputDataRow["CustomerWard"] = dr[4].ToString().Trim().Replace("#N/A", "").Replace("#n/a", "").Replace("N/A", "").Replace("n/a", "");
                inputDataRow["CustomerDistrict"] = dr[5].ToString().Trim().Replace("#N/A", "").Replace("#n/a", "").Replace("N/A", "").Replace("n/a", "");
                inputDataRow["CustomerProvince"] = dr[6].ToString().Trim().Replace("#N/A", "").Replace("#n/a", "").Replace("N/A", "").Replace("n/a", "");
                inputDataRow["CustomerPhone1"] = dr[7].ToString().Trim().Replace("#N/A", "").Replace("#n/a", "").Replace("N/A", "").Replace("n/a", "");
                inputDataRow["CustomerPhone2"] = dr[8].ToString().Trim().Replace("#N/A", "").Replace("#n/a", "").Replace("N/A", "").Replace("n/a", "");
                                
                string email = dr[9].ToString().Replace("#N/A", "").Replace("#n/a", "").Replace("N/A", "").Replace("n/a", "");
                if (email.Trim() != string.Empty)
                {
                    bool isEmail = Regex.IsMatch(email, @"\A(?:[a-z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[a-z0-9!#$%&'*+/=?^_`{|}~-]+)*@(?:[a-z0-9](?:[a-z0-9-]*[a-z0-9])?\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?)\Z", RegexOptions.IgnoreCase);
                    if (!isEmail)
                    {
                        inputDataRow[importStatusCoumnName] = -3;    // Email is invalid
                    }
                }
                inputDataRow["CustomerEmail"] = dr[9].ToString().Trim().Replace("#N/A", "").Replace("#n/a", "").Replace("N/A", "").Replace("n/a", "");
                inputDataRow["CustomerFacebookId"] = dr[10].ToString().Trim().Replace("#N/A", "").Replace("#n/a", "").Replace("N/A", "").Replace("n/a", "");

                inputDataRow["CustomerBillingCode"] = dr[11].ToString().Trim().Replace("#N/A", "").Replace("#n/a", "").Replace("N/A", "").Replace("n/a", "");

                inputDataRow["AlternativeAddressCode"] = dr[12].ToString().Trim().Replace("#N/A", "").Replace("#n/a", "").Replace("N/A", "").Replace("n/a", "");
                inputDataRow["AlternativeAddressName"] = dr[13].ToString().Trim().Replace("#N/A", "").Replace("#n/a", "").Replace("N/A", "").Replace("n/a", "");
                inputDataRow["AlternativeAddressAddress"] = dr[14].ToString().Trim().Replace("#N/A", "").Replace("#n/a", "").Replace("N/A", "").Replace("n/a", "");
                inputDataRow["AlternativeAddressWard"] = dr[15].ToString().Trim().Replace("#N/A", "").Replace("#n/a", "").Replace("N/A", "").Replace("n/a", "");
                inputDataRow["AlternativeAddressDistrict"] = dr[16].ToString().Trim().Replace("#N/A", "").Replace("#n/a", "").Replace("N/A", "").Replace("n/a", "");
                inputDataRow["AlternativeAddressProvince"] = dr[17].ToString().Trim().Replace("#N/A", "").Replace("#n/a", "").Replace("N/A", "").Replace("n/a", "");
                inputDataRow["AlternativeAddressPhone"] = dr[18].ToString().Trim().Replace("#N/A", "").Replace("#n/a", "").Replace("N/A", "").Replace("n/a", "");

                inputDataRow["ClassificationCode"] = dr[19].ToString().Trim().Replace("#N/A", "").Replace("#n/a", "").Replace("N/A", "").Replace("n/a", "");
                inputDataRow["ClassificationName"] = dr[20].ToString().Trim().Replace("#N/A", "").Replace("#n/a", "").Replace("N/A", "").Replace("n/a", "");

                dataTable.Rows.Add(inputDataRow);
            }

            var mappingColumnCollection = infoColumnMapping.Select(x => new[] { (string)x.DbColumn, (string)x.DbColumn }).ToList();
            mappingColumnCollection.Add(new[] { idCoumnName, idCoumnName });
            mappingColumnCollection.Add(new[] { importSessionIdCoumnName, importSessionIdCoumnName });
            mappingColumnCollection.Add(new[] { importStatusCoumnName, importStatusCoumnName });

            await Task.Run(() => Repository.BulkInsertAll(dataTable, "dbo.Staging_Customers", mappingColumnCollection));
        }
    }
}

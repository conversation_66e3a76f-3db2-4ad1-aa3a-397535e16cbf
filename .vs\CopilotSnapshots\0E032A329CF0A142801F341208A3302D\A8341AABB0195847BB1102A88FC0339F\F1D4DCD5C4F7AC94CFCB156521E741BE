﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Webaby.Data;
using Webaby.Localization;
using Webaby;
using System;
using System.Linq;
using System.Threading.Tasks;
using TinyCRM.Outbound.Prospect;
using TinyCRM.Outbound.ProspectAssignment;
using Webaby.Core.File;
using Webaby.Core.File.Queries;
using System.Collections.Generic;

namespace TinyCRM.Outbound.Campaign.Queries
{
    public class GetCampaignByIdQuery : QueryBase<CampaignData>
    {
        public Guid Id { get; set; }
    }

    internal class GetCampaignByIdQueryHandler : QueryHandlerBase<GetCampaignByIdQuery, CampaignData>
    {
        public GetCampaignByIdQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<CampaignData>> ExecuteAsync(GetCampaignByIdQuery query)
        {
            var campaign = await EntitySet.GetAsync<CampaignEntity>(query.Id);
            if (campaign == null)
            {
                throw new InvalidOperationException(T["Không tìm  thấy chiến dịch có id '{0}'", query.Id]);
            }
            var campaignData = Mapper.Map<CampaignData>(campaign);
            campaignData.EmailAttachmentFileList = new List<FileData>();
            var files = await (from f in EntitySet.Get<FileEntity>()
                               where f.ReferenceObjectType == "Campaign"
                               && f.ReferenceObjectId == campaign.Id
                               select f).ToListAsync();
            foreach (var file in files)
            {
                campaignData.EmailAttachmentFileList.Add(Mapper.Map<FileData>(file));
            }

            campaignData.FileEditList = new List<FileData>();
            var editfiles = await (from f in EntitySet.Get<FileEntity>()
                                   where f.ReferenceObjectType == "Campaign.EditFiles"
                                   && f.ReferenceObjectId == campaign.Id
                                   select f).ToListAsync();
            foreach (var editfile in editfiles)
            {
                campaignData.FileEditList.Add(Mapper.Map<FileData>(editfile));
            }

            return new QueryResult<CampaignData>(campaignData);
        }
    }

    public class GetCampaignByProspectAssignmentQuery : QueryBase<CampaignData>
    {
        public Guid ProspectAssignmentId { get; set; }

        internal class Handler : QueryHandlerBase<GetCampaignByProspectAssignmentQuery, CampaignData>
        {
            public Handler(IServiceProvider serviceProvider) : base(serviceProvider) { }

            public override async Task<QueryResult<CampaignData>> ExecuteAsync(GetCampaignByProspectAssignmentQuery query)
            {
                var result = await (from c in EntitySet.Get<CampaignEntity>()
                                    join p in EntitySet.Get<ProspectEntity>() on c.Id equals p.CampaignId
                                    join pa in EntitySet.Get<ProspectAssignmentEntity>() on p.Id equals pa.ProspectId
                                    where pa.Id == query.ProspectAssignmentId
                                    select c).ToListAsync();
                var mapped = Mapper.Map<List<CampaignData>>(result);
                return QueryResult.Create(mapped);
            }
        }
    }
}
﻿using AutoMapper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TinyCRM.BusinessResult.Queries;
using TinyCRM.TaskType;
using Webaby;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.BusinessResult.Commands
{
    public class AddNewBusinessResult
    {
        public string Name { get; set; }
        public string Code { get; set; }
        public string CodeColor { get; set; }
    }

    public class AddBusinessResultCommand : CommandBase
    {
        public Guid Id { get; set; }
        public string Name { get; set; }

        public string Code { get; set; }

        public int DisplayOrder { get; set; }

        public string CodeColor { get; set; }

        public Guid? ParentId { get; set; }
        public List<AddNewBusinessResult> listAddNew { get; set; }
        public List<BusinessResultData> listEdit { get; set; }
    }

    internal class AddBusinessResultCommandHandler : CommandHandlerBase<AddBusinessResultCommand>
    {
        public AddBusinessResultCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task ExecuteAsync(AddBusinessResultCommand command)
        {
            if (command.ParentId == null)
            {
                List<IEntity> addLvl1 = new List<IEntity>();
                if (command.listAddNew != null && command.listAddNew.Count > 0)
                {
                    foreach (var item in command.listAddNew)
                    {
                        if (item.Code.IsNotNullOrEmpty() && item.Name.IsNotNullOrEmpty())
                        {
                            BusinessResultEntity businessresultentity = new BusinessResultEntity();
                            businessresultentity.Name = item.Name;
                            businessresultentity.Code = item.Code;
                            businessresultentity.CodeColor = item.CodeColor;
                            businessresultentity.Id = Guid.NewGuid();
                            addLvl1.Add(businessresultentity);
                        }
                    }
                    await Repository.SaveAsync(addLvl1);
                }
            }
            else
            {
                BusinessResultEntity editLvl1 = await EntitySet.GetAsync<BusinessResultEntity>(command.ParentId.Value);
                if (editLvl1 != null)
                {
                    if (editLvl1.Code.IsNotNullOrEmpty() && editLvl1.Name.IsNotNullOrEmpty())
                    {
                        editLvl1.Name = command.Name;
                        editLvl1.Code = command.Code;
                        editLvl1.CodeColor = command.CodeColor;
                        await Repository.SaveAsync(editLvl1);
                    }
                }
                List<IEntity> addLvl2 = new List<IEntity>();
                if (command.listAddNew != null && command.listAddNew.Count > 0)
                {
                    foreach (var item in command.listAddNew)
                    {
                        if (item.Code.IsNotNullOrEmpty() && item.Name.IsNotNullOrEmpty())
                        {
                            BusinessResultEntity businessresultentity = new BusinessResultEntity();
                            businessresultentity.ParentId = command.ParentId;
                            businessresultentity.Name = item.Name;
                            businessresultentity.Code = item.Code;
                            businessresultentity.CodeColor = item.CodeColor;
                            businessresultentity.Id = Guid.NewGuid();
                            addLvl2.Add(businessresultentity);
                        }
                    }
                    await Repository.SaveAsync(addLvl2);
                }
                if (command.listEdit != null)
                {
                    var editLvl2 = new List<BusinessResultEntity>();
                    foreach (var item in command.listEdit)
                    {
                        if (item.Code.IsNotNullOrEmpty() && item.Name.IsNotNullOrEmpty())
                        {
                            BusinessResultEntity businessresultentity = await EntitySet.GetAsync<BusinessResultEntity>(item.Id);
                            businessresultentity.Name = item.Name;
                            businessresultentity.Code = item.Code;
                            businessresultentity.CodeColor = item.CodeColor;
                            editLvl2.Add(businessresultentity);
                        }
                    }
                    await Repository.SaveAsync(editLvl2);
                }
            }
        }
    }
}
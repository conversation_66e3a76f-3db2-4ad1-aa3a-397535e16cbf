﻿using System;
using System.Threading.Tasks;
using AutoMapper;
using Webaby;
using Webaby.Validation.PhoneNumber;
using Webaby.Data;
using Webaby.Localization;
using TinyCRM.Outbound.Contact;

namespace TinyCRM.Customer.Commands
{
    public class CreateEditCustomerCommand : CommandBase
    {
        public Guid Id { get; set; }

        public string Name { get; set; }

        public string SubName { get; set; }

        public string Code { get; set; }

        public string B2BCode { get; set; }

        public DateTime? Dob { get; set; }

        public Enums.Gender Sex { get; set; }

        public bool CustomerClass { get; set; }

        public Guid SourceClassificationId { get; set; }

        public int Type { get; set; }

        public string CMND { get; set; }

        public string DataSource { get; set; }

        public string Phone1 { get; set; }

        public string Phone2 { get; set; }

        public string Phone3 { get; set; }

        public string Email { get; set; }

        public string FacebookId { get; set; }

        public string Job { get; set; }

        public string WorkAddress { get; set; }

        public string ContactPhone { get; set; }

        public string Address { get; set; }

        public string AddressNumber { get; set; }

        public string AddressStreet { get; set; }

        public Guid? RegionId { get; set; }

        public Guid? AreaId { get; set; }

        public Guid? ProvinceId { get; set; }

        public Guid? DistrictId { get; set; }

        public Guid? WardId { get; set; }

        public long? CreditLimit { get; set; }

        public string TaxNumber { get; set; }

        public string LicenseType { get; set; }

        public string License { get; set; }

        public DateTime? LicenseDate { get; set; }

        public string Avatar { get; set; }

        public string Background { get; set; }

        public DateTime? LicenseExpire { get; set; }

        public string LicensePlace { get; set; }

        public string OriginNation { get; set; }

        public string Nation { get; set; }

        public string BankID { get; set; }

        public string LocationID { get; set; }

        public string Residence { get; set; }

        public string Status { get; set; }

        public string Notes { get; set; }

        public MaritalStatus? MaritalStatus { get; set; }

        public string FullAddress { get; set; }

        public bool IsBackendCustomer { get; set; }

        public decimal? Income { get; set; }
    }

    internal class CreateEditCustomerCommandHandler : CommandHandlerBase<CreateEditCustomerCommand>
    {
        IText _text { get; set; }
        public CreateEditCustomerCommandHandler(IServiceProvider serviceProvider, IText text) : base(serviceProvider) { _text = text; }

        public override async Task ExecuteAsync(CreateEditCustomerCommand command)
        {
            bool isNewCustomer = false;
            string oldCode = string.Empty;
            string oldB2BCode = string.Empty;

            var entity = await EntitySet.GetAsync<CustomerEntity>(command.Id);
            if (entity == null)
            {
                entity = new CustomerEntity();
                isNewCustomer = true;
            }
            else
            {
                oldCode = entity.Code;
                oldB2BCode = entity.B2BCode;
            }
            Mapper.Map(command, entity);

            // Nếu là Khách hàng Vãng lai, KHÔNG cho cập nhật Code
            if (!isNewCustomer && entity.IsBackendCustomer == false)
            {
                if (oldCode.IsNotNullOrEmpty())
                {
                    entity.Code = oldCode;
                }
                if (oldB2BCode.IsNotNullOrEmpty())
                {
                    entity.B2BCode = oldB2BCode;
                }
            }

            CheckPhoneValidResult checkPhoneValidResult = new CheckPhoneValidResult();
            entity.Phone1 = PhoneNumberValidator.Input(entity.Phone1, _text).Output;
            entity.Phone2 = PhoneNumberValidator.Input(entity.Phone2, _text).Output;
            entity.Phone3 = PhoneNumberValidator.Input(entity.Phone3, _text).Output;
            entity.ContactPhone = PhoneNumberValidator.Input(entity.ContactPhone, _text).Output;
            entity.Email = entity.Email.IsNullOrEmpty() ? null : entity.Email;

            await Repository.SaveAsync(entity);
        }
    }
}
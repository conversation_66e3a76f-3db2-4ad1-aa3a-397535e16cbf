﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;

namespace TinyCRM.DigitalChannel.Queries
{
    public class GetDigitalServiceConfigurationQuery : QueryBase<DigitalServiceConfigurationEntity>
    {
        public Guid? Id { get; set; }
    }

    internal class GetDigitalServiceConfigurationQueryHandler : QueryHandlerBase<GetDigitalServiceConfigurationQuery, DigitalServiceConfigurationEntity>
    {
        public GetDigitalServiceConfigurationQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<DigitalServiceConfigurationEntity>> ExecuteAsync(GetDigitalServiceConfigurationQuery query)
        {
            var channelEntities = await EntitySet.GetAsync<DigitalServiceConfigurationEntity>();
            if (query.Id.HasValue)
            {
                channelEntities = channelEntities.Where(x => x.Id == query.Id);
            }
            return QueryResult.Create(channelEntities);
        }
    }
}
﻿using Webaby.Data;
using AutoMapper;
using Webaby.Localization;
using Webaby;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace TinyCRM.CustomerVersionName.Queries
{
    public class GetAllCustomerVersionNameQuery : QueryBase<CustomerVersionNameEntity>
    {
        public string Job { get; set; }
    }

    internal class GetAllCustomerVersionNameQueryHandler : QueryHandlerBase<GetAllCustomerVersionNameQuery, CustomerVersionNameEntity>
    {
        public GetAllCustomerVersionNameQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<CustomerVersionNameEntity>> ExecuteAsync(GetAllCustomerVersionNameQuery query)
        {
            var entity = await EntitySet.GetAsync<CustomerVersionNameEntity>();
            var ordered = entity.OrderBy(x => x.YearVersion).ThenBy(x => x.MonthVersion).ThenBy(x => x.NameVersion);
            return QueryResult.Create(ordered);
        }
    }
}
﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using TinyCRM.DynamicForm.Queries;
using TinyCRM.Survey;
using Webaby;
using Webaby.Caching;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.ContentTemplate.Queries
{
    public class GetDynamicFormContentTemplateLinkEntityParamsQuery : QueryBase<ContentTemplateLinkEntityParams>
    {
    }

    internal class GetDynamicFormContentTemplateLinkEntityParamsQueryHandler : QueryHandlerBase<GetDynamicFormContentTemplateLinkEntityParamsQuery, ContentTemplateLinkEntityParams>
    {
        private const Int32 GetAllDynamicFieldDefinitionInfoExpiredInMinutes = 10080;   // 7 ngày

        private void SetGetAllDynamicFieldDefinitionInfoExpire(IAcquireContext context)
        {
            context.Monitor(_clcok.WhenUtc(DateTime.UtcNow.AddMinutes(GetAllDynamicFieldDefinitionInfoExpiredInMinutes)));
        }

        IQueryExecutor _queryExecutor { get; set; }

        ICache _cache { get; set; }

        IClock _clcok { get; set; }

        public GetDynamicFormContentTemplateLinkEntityParamsQueryHandler(IServiceProvider serviceProvider, IQueryExecutor
            queryExecutor, ICache cache, IClock clock)
            : base(serviceProvider) { _queryExecutor = queryExecutor; _cache = cache; _clcok = clock; }

        public override async Task<QueryResult<ContentTemplateLinkEntityParams>> ExecuteAsync(GetDynamicFormContentTemplateLinkEntityParamsQuery query)
        {
            var result = new List<ContentTemplateLinkEntityParams>();

            var dynamicFieldList = _cache.Get("DynamicFieldDefinition.AllDynamicFieldDefinitionInfo", c =>
            {
                SetGetAllDynamicFieldDefinitionInfoExpire(c);
                return _queryExecutor.ExecuteManyAsync(new GetAllDynamicFieldDefinitionInfoQuery { }).Result.ToList();
            });

            var ticketFields = new List<ContentTemplateLinkEntityParams.Field>();
            var taskFields = new List<ContentTemplateLinkEntityParams.Field>();
            foreach (var dynamicField in dynamicFieldList)
            {
                if (dynamicField.DataType == "TinyCRM.DynamicDefinedTable.Queries.DynamicDefinedTableGridData")
                {
                    ticketFields.Add(new ContentTemplateLinkEntityParams.Field
                    {
                        DisplayName = string.Format("UserDefinedTable/{0}- Phiếu - {1} : {2}", dynamicField.Name, dynamicField.DynamicFormName, dynamicField.DisplayName),
                        RootEntity = "TicketDynamicField",
                        RootField = dynamicField.Name,
                        Type = dynamicField.DataType,
                    });
                }
                else
                {
                    ticketFields.Add(new ContentTemplateLinkEntityParams.Field
                    {
                        DisplayName = string.Format("Phiếu - {0} : {1}", dynamicField.DynamicFormName, dynamicField.DisplayName),
                        RootEntity = "TicketDynamicField",
                        RootField = dynamicField.Name,
                        Type = dynamicField.DataType,
                    });
                }

                taskFields.Add(new ContentTemplateLinkEntityParams.Field
                {
                    DisplayName = string.Format("Tác vụ - {0} : {1}", dynamicField.DynamicFormName, dynamicField.DisplayName),
                    RootEntity = "TaskDynamicField",
                    RootField = dynamicField.Name,
                    Type = dynamicField.DataType,
                });
            }
            result.Add(new ContentTemplateLinkEntityParams
            {
                Fields = ticketFields,
                Type = "RequestTicket",
            });

            result.Add(new ContentTemplateLinkEntityParams
            {
                Fields = taskFields,
                Type = "Task",
            });
            return new QueryResult<ContentTemplateLinkEntityParams>(result);
        }
    }
}

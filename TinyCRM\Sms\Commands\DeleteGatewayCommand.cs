﻿using System;
using System.Threading.Tasks;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;
using Webaby;

namespace TinyCRM.Sms.Commands
{
    public class DeleteGatewayCommand : CommandBase
    {
        public Guid Id { get; set; }
    }

    internal class DeleteGatewayCommandHandler : CommandHandlerBase<DeleteGatewayCommand>
    {
        public DeleteGatewayCommandHandler(
            IText text,
            IMapper mapper,
            IRepository repository,
            IEntitySet entitySet,
            IQueryExecutor queryExecutor,
            ICommandExecutor commandExecutor,
            ILocalTransactionManager transactionManager,
            IEventBus eventBus
        ) : base(text, mapper, repository, entitySet, queryExecutor, commandExecutor, transactionManager, eventBus) { }

        public override async Task ExecuteAsync(DeleteGatewayCommand command)
        {
            var entity = await EntitySet.GetAsync<GatewayEntity>(command.Id);
            if (entity == null)
            {
                throw new InvalidOperationException(T["Cannot find Gateway with Id = {0}", command.Id]);
            }
            await Repository.DeleteAsync(entity);
        }
    }
}

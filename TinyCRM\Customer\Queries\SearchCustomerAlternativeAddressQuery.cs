﻿using System;
using System.Data;
using System.Data.SqlClient;
using Webaby;
using Webaby.Data;

namespace TinyCRM.Customer.Queries
{
    public class SearchCustomerAlternativeAddressQuery : QueryBase<CustomerAlternativeAddressData>
    {
        public Guid CustomerId { get; set; }

        public string Query { get; set; }
    }

    internal class GetCustomerAlternativeAddressQueryHandler :
        QueryHandlerBase<SearchCustomerAlternativeAddressQuery, CustomerAlternativeAddressData>
    {
        public GetCustomerAlternativeAddressQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<CustomerAlternativeAddressData>> ExecuteAsync(SearchCustomerAlternativeAddressQuery query)
        {
            var cmd = EntitySet.CreateDbCommand();
            cmd.Parameters.AddRange(new[]
            {
                DbParameterHelper.AddNullableGuid(cmd, "@customerId", query.CustomerId),
                DbParameterHelper.AddNullableString(cmd, "@query", query.Query), 
            });
            cmd.CommandType = CommandType.StoredProcedure;
            cmd.CommandText = "GetCustomerAlternativeAddress";
            var mainQuery = await EntitySet.ExecuteReadCommandAsync<CustomerAlternativeAddressData>(cmd);
            return new QueryResult<CustomerAlternativeAddressData>(mainQuery);
        }
    }
}

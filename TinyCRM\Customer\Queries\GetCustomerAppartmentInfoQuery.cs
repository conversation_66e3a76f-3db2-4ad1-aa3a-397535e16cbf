﻿using System;
using System.Linq;
using System.Collections.Generic;
using Webaby;
using TinyCRM.PartCustomer;
using TinyCRM.Part;

namespace TinyCRM.Customer.Queries
{
    public class GetCustomerAppartmentInfoQuery : QueryBase<CustomerAppartmentInfo>
    {
        public IEnumerable<Guid> PartCustomerIds { get; set; }

        public string Name { get; set; }

        public string PhoneNumber { get; set; }

        public string CMND { get; set; }

        public IEnumerable<Guid> PartIds { get; set; }
    }

    internal class GetCustomerAppartmentInfoQueryHandler : QueryHandlerBase<GetCustomerAppartmentInfoQuery, CustomerAppartmentInfo>
    {
        public GetCustomerAppartmentInfoQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<CustomerAppartmentInfo>> ExecuteAsync(GetCustomerAppartmentInfoQuery query)
        {
            var partCustomerQuery = from customer in EntitySet.Get<CustomerEntity>()
                                join partCustomer in EntitySet.Get<PartCustomerEntity>() on customer.Id equals partCustomer.CustomerId
                                select new { customer, partCustomer };

            if (query.PartCustomerIds != null && query.PartCustomerIds.Any())
            {
                partCustomerQuery = partCustomerQuery.Where(x => query.PartCustomerIds.Contains(x.partCustomer.Id));
            }
            else
            {
                if (query.Name.IsNotNullOrEmpty())
                {
                    partCustomerQuery = partCustomerQuery.Where(x => x.customer.Name.Contains(query.Name));
                }

                if (query.CMND.IsNotNullOrEmpty())
                {
                    partCustomerQuery = partCustomerQuery.Where(x => x.customer.CMND.Contains(query.CMND));
                }

                if (query.PhoneNumber.IsNotNullOrEmpty())
                {
                    partCustomerQuery = partCustomerQuery.Where(x => x.customer.Phone1.Contains(query.PhoneNumber) ||
                                                            x.customer.Phone2.Contains(query.PhoneNumber) ||
                                                            x.customer.Phone3.Contains(query.PhoneNumber));
                }
            }

            var mainQuery = (from pc in partCustomerQuery
                             join part in EntitySet.Get<PartEntity>() on new { PartId = pc.partCustomer.PartId, IsValid = pc.partCustomer.IsValid } equals new { PartId = part.Id, IsValid = true }
                             where (!query.PartIds.Any() || query.PartIds.Contains(part.Id))
                             group new { pc, part } by pc
                             into grp
                             select new CustomerAppartmentInfo
                             {
                                Id = grp.Key.partCustomer.Id,
                                Name = grp.Key.customer.Name,
                                CMND = grp.Key.customer.CMND,
                                PhoneNumber = string.Join("-", new object[] { grp.Key.customer.Phone1, grp.Key.customer.Phone2, grp.Key.customer.Phone3 }),
                                Appartment = grp.First().part.Name,
                             })
                             .OrderBy(x => x.Name);

            return new QueryResult<CustomerAppartmentInfo>(mainQuery, query.Pagination);                            
        }
    }
}

﻿using Webaby.Data;
using Webaby;
using System.Data;
using AutoMapper;
using Webaby.Localization;
using System.Data.Common;

namespace TinyCRM.DynamicDefinedTable.Commands
{
    public class UpdateEntityLinkByLinkedTicketColumnCommand : CommandBase
    {
        public Guid DynamicFieldValueId { get; set; }

        public Guid ReferenceObjectId { get; set; }

        public Guid LinkedTicketBusinessSpecificId { get; set; }

        public List<Guid> LinkedTicketIdList { get; set; }

        public Guid CurrentUserId { get; set; }
    }

    internal class UpdateEntityLinkByLinkedTicketColumnCommandHandler : CommandHandlerBase<UpdateEntityLinkByLinkedTicketColumnCommand>
    {
        public UpdateEntityLinkByLinkedTicketColumnCommandHandler(IText text, IMapper mapper, IRepository repository, IEntitySet entitySet, IQueryExecutor queryExecutor, ICommandExecutor commandExecutor, ILocalTransactionManager transactionManager, IEventBus eventBus)
            : base(text, mapper, repository, entitySet, queryExecutor, commandExecutor, transactionManager, eventBus) { }

        public override async Task ExecuteAsync(UpdateEntityLinkByLinkedTicketColumnCommand command)
        {
            DbCommand dbCommand = EntitySet.CreateDbCommand();
            dbCommand.CommandType = CommandType.StoredProcedure;
            dbCommand.CommandText = "dbo.UpdateEntityLinkByLinkedTicketColumn";
            dbCommand.Parameters.AddRange(new[]
            {
                DbParameterHelper.AddNullableGuid(dbCommand, "@DynamicFieldValueId", command.DynamicFieldValueId),
                DbParameterHelper.AddNullableGuid(dbCommand, "@ReferenceObjectId", command.ReferenceObjectId),
                DbParameterHelper.AddNullableGuid(dbCommand, "@LinkedTicketBusinessSpecificId", command.LinkedTicketBusinessSpecificId),
                DbParameterHelper.AddNullableGuid(dbCommand, "@CurrentUserId", command.CurrentUserId),
                DbParameterHelper.NewIdListParameter("@LinkedTicketIdList", command.LinkedTicketIdList),
            });

            await EntitySet.ExecuteNonQueryAsync(dbCommand);
        }
    }
}

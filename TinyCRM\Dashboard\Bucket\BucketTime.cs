﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Webaby;
using Webaby.Localization;

namespace TinyCRM.Dashboard.Bucket
{
    public class BucketTime
    {
        public struct DateRange
        {
            public DateTime Start { get; set; }
            public DateTime End { get; set; }
            public override string ToString()
            {
                return $"{Start:dd/MM/yyyy HH:mm}-{End:dd/MM/yyyy HH:mm}";
            }
        }

        public static DateRange ThisYear(DateTime date) => new DateRange { Start = new DateTime(date.Year, 1, 1).AddYears(-0), End = new DateTime(date.Year, 1, 1).AddYears(1).AddMilliseconds(-1) };
        public static DateRange LastYear(DateTime date) => new DateRange { Start = new DateTime(date.Year, 1, 1).AddYears(-1), End = new DateTime(date.Year, 1, 1).AddYears(0).AddMilliseconds(-1) };
        public static DateRange ThisMonth(DateTime date) => new DateRange { Start = new DateTime(date.Year, date.Month, 1).AddMonths(-0), End = new DateTime(date.Year, date.Month, 1).AddMonths(1).AddMilliseconds(-1) };
        public static DateRange LastMonth(DateTime date) => new DateRange { Start = new DateTime(date.Year, date.Month, 1).AddMonths(-1), End = new DateTime(date.Year, date.Month, 1).AddMonths(0).AddMilliseconds(-1) };
        public static DateRange ThisWeek(DateTime date) => new DateRange { Start = date.Date.AddDays(-(int)date.DayOfWeek - 0), End = date.Date.AddDays(7).AddDays(-(int)date.DayOfWeek).AddMilliseconds(-1) };
        public static DateRange LastWeek(DateTime date) => new DateRange { Start = date.Date.AddDays(-(int)date.DayOfWeek - 7), End = date.Date.AddDays(0).AddDays(-(int)date.DayOfWeek).AddMilliseconds(-1) };
        public static DateRange ToDay(DateTime date) => new DateRange { Start = date.Date.AddDays(-0), End = date.Date.AddDays(1).AddMilliseconds(-1) };
        public static DateRange YesterDay(DateTime date) => new DateRange { Start = date.Date.AddDays(-1), End = date.Date.AddDays(0).AddMilliseconds(-1) };

        public static DateRange YearAddStart(DateRange date, int years) => new DateRange { Start = date.Start.AddYears(years), End = date.End };
        public static DateRange MonthAddStart(DateRange date, int months) => new DateRange { Start = date.Start.AddMonths(months), End = date.End };
        public static DateRange DayAddStart(DateRange date, int days) => new DateRange { Start = date.Start.AddDays(days), End = date.End };
        public static DateRange HourAddStart(DateRange date, int hours) => new DateRange { Start = date.Start.AddHours(hours), End = date.End };
        public static DateRange MinuteAddStart(DateRange date, int minutes) => new DateRange { Start = date.Start.AddMinutes(minutes), End = date.End };

        public static DateRange YearAddEnd(DateRange date, int years) => new DateRange { Start = date.Start, End = date.End.AddYears(years) };
        public static DateRange MonthAddEnd(DateRange date, int months) => new DateRange { Start = date.Start, End = date.End.AddMonths(months) };
        public static DateRange DayAddEnd(DateRange date, int days) => new DateRange { Start = date.Start, End = date.End.AddDays(days) };
        public static DateRange HourAddEnd(DateRange date, int hours) => new DateRange { Start = date.Start, End = date.End.AddHours(hours) };
        public static DateRange MinuteAddEnd(DateRange date, int minutes) => new DateRange { Start = date.Start, End = date.End.AddMinutes(minutes) };

        public static DateRange? Parse(string tag, Action<string> errorCallback, IText text)
        {
            var regex = new Regex("(?<n>[a-zA-Z_][a-zA-Z_0-9]*)(\\((?<a>-?[0-9]+)\\))?", RegexOptions.Compiled);
            DateRange? dateRange = null;
            var eList = regex.Matches(Regex.Replace(tag, "\\|", Environment.NewLine));
            foreach (Match e in eList)
            {
                var funcName = e.Groups["n"].Value;
                var arg = e.Groups["a"].Value;
                var argValue = 0;
                int.TryParse(arg, out argValue);
                var func = typeof(BucketTime).GetMethod(funcName);
                if (func == null)
                {
                    errorCallback?.Invoke(text["BucketDashboard - Biểu thức [{0}] - [{1}] không hợp lệ", funcName, tag]);
                    continue;
                }
                if (func.GetParameters().Length == 1)
                {
                    dateRange = func.Invoke(null, new object[] { (dateRange?.Start) ?? DateTime.Now }) as BucketTime.DateRange?;
                }
                else
                {
                    dateRange = func.Invoke(null, new object[] { dateRange, argValue }) as DateRange?;
                }
            }
            return dateRange;
        }
    }
}

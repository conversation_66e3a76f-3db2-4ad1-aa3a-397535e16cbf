﻿using Webaby.Data;
using AutoMapper;
using Webaby.Localization;
using Webaby;
using System.Threading.Tasks;
using System;
using TinyCRM.Building;

namespace TinyCRM.AutomaticTask.Queries
{
    public class GetUserSelectorPathByIdQuery : QueryBase<UserPathSelectorData>
    {
        public Guid Id { get; set; }
    }

    internal class GetUserSelectorPathByIdQueryHandler : QueryHandlerBase<GetUserSelectorPathByIdQuery, UserPathSelectorData>
    {
        public GetUserSelectorPathByIdQueryHandler(IServiceProvider serviceProvider)
            : base(serviceProvider) { }

        public override async Task<QueryResult<UserPathSelectorData>> ExecuteAsync(GetUserSelectorPathByIdQuery query)
        {
            var entity = await EntitySet.GetAsync<UserPathSelectorEntity>(query.Id);
            var mapped = Mapper.Map<UserPathSelectorData>(entity);
            return new QueryResult<UserPathSelectorData>(mapped);
        }
    }
}

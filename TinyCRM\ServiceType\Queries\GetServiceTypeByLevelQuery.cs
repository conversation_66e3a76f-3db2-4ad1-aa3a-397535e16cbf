﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;

namespace TinyCRM.ServiceType.Queries
{
    public class GetServiceTypeByLevelQuery : QueryBase<ServiceTypeData>
    {
        public Guid? Level1Id { get; set; }

        public Guid? Level2Id { get; set; }

        public Guid? Level3Id { get; set; }

        public Guid? Level4Id { get; set; }
    }

    internal class GetServiceTypeByLevelQueryHandler : QueryHandlerBase<GetServiceTypeByLevelQuery, ServiceTypeData>
    {
        public GetServiceTypeByLevelQueryHandler(IEntitySet entitySet, IRepository repository, IText text, IMapper mapper)
            : base(entitySet, repository, text, mapper) { }

        public override async Task<QueryResult<ServiceTypeData>> ExecuteAsync(GetServiceTypeByLevelQuery query)
        {
            var stList = await EntitySet.GetAsync<ServiceTypeEntity>();
            var r = from st in stList
                    where (st.Level1Id == query.Level1Id || (!query.Level1Id.HasValue && !st.Level1Id.HasValue))
                    && (st.Level2Id == query.Level2Id || (!query.Level2Id.HasValue && !st.Level2Id.HasValue))
                    && (st.Level3Id == query.Level3Id || (!query.Level3Id.HasValue && !st.Level3Id.HasValue))
                    && (st.Level4Id == query.Level4Id || (!query.Level4Id.HasValue && !st.Level4Id.HasValue))
                    select st;
            return QueryResult.Create(r, query.Pagination, Mapper.Map<ServiceTypeData>);
        }
    }
}

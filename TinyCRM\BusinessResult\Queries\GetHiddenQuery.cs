﻿using Newtonsoft.Json;
using System;
using System.Linq;
using Webaby;
using System.Collections.Generic;
using System.Web;
using static TinyCRM.BusinessResult.Queries.GetHiddenQuery;
using Webaby.Caching;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;

namespace TinyCRM.BusinessResult.Queries
{
    public class GetHiddenQuery : QueryBase<ResultHiddenData>
    {
        public Guid Indentity { get; set; }
        public class RequestTicketFilterConfig
        {
            public class LayoutConfig
            {
                public string filterDisplayRow { get; set; }
            }
            public class FilterControl
            {
                public string name { get; set; }
                public int order { get; set; }
                public int size { get; set; }
                public bool required { get; set; }
                public string tooltip { get; set; }
            }

            public class ResultColumn
            {
                public string name { get; set; }
                public string display { get; set; }
                public int order { get; set; }
                public string @class { get; set; }
                public string mode { get; set; }
                public bool IsDynamicField { get; set; }
            }

            public LayoutConfig layout { get; set; }
            public FilterControl[] filter { get; set; }
            public ResultColumn[] result { get; set; }
        }
        public class TestModeModel
        {
            public bool IsActive { get; set; }
            public RequestTicketFilterConfig requestTicketFilterConfig { get; set; }
        }
        public class ConfigureUISearchByModeModel
        {
            public RequestTicketFilterConfig LiveModel { get; set; }
            public TestModeModel TestModel { get; set; }
        }
        public class ResultHiddenData
        {
            public string Name { get; set; }
            public string Value { get; set; }
        }
    }

    internal class GetHiddenQueryHandler : QueryHandlerBase<GetHiddenQuery, ResultHiddenData>
    {
        public GetHiddenQueryHandler(IServiceProvider serviceProvider, ICacheProvider cacheProvider, IConfiguration configuration)
            : base(serviceProvider) { _cacheProvider = cacheProvider; _configuration = configuration; }

        ICacheProvider _cacheProvider { get; set; }
        IConfiguration _configuration { get; set; }

        public override async Task<QueryResult<ResultHiddenData>> ExecuteAsync(GetHiddenQuery query)
        {
            var json_data = _configuration.GetValue<string>("ticket.filter.config");            
            var default_data = JsonConvert.DeserializeObject<RequestTicketFilterConfig>(json_data);
            var dataConfigure = new ConfigureUISearchByModeModel();
            dataConfigure.LiveModel = default_data;

            string testModeCacheKey = string.Format("{0}_{1}", "configure_filter", query.Indentity);
            if (_cacheProvider.IsSet(testModeCacheKey))
            {
                dataConfigure.TestModel = new TestModeModel();
                var jsoncache = JsonConvert.SerializeObject(_cacheProvider.Get(testModeCacheKey));
                dataConfigure.TestModel = JsonConvert.DeserializeObject<TestModeModel>(jsoncache);
            }
            var mainquery = new List<ResultHiddenData>();
            foreach (var item in dataConfigure.TestModel.requestTicketFilterConfig.filter.OrderBy(model => model.order < 0))
            {
                if(item.name != "IncludeDataInCampaign" && item.name != "IsExport" && item.name!= "CampaignId" && item.order <0)
                {
                    var mod = new ResultHiddenData
                    {
                        Name = item.name,
                        Value = item.name
                    };
                    mainquery.Add(mod);
                }
            }

            return new QueryResult<ResultHiddenData>(mainquery);
        }
    }
}

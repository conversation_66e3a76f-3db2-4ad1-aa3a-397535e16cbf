﻿using System;
using System.Linq;
using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Webaby.Data;
using Webaby.Localization;
using Webaby;

namespace TinyCRM.Outbound.Campaign.Queries
{
    public class GetCampaignExecutingTimeListQuery : QueryBase<CampaignExecutingTimeInfo>
    {
        public Guid CampaignId { get; set; }
    }

    internal class GetCampaignExecutingTimeListQueryHandler : QueryHandlerBase<GetCampaignExecutingTimeListQuery, CampaignExecutingTimeInfo>
    {
        public GetCampaignExecutingTimeListQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override QueryResult<CampaignExecutingTimeInfo> Execute(GetCampaignExecutingTimeListQuery query)
        {
            var campaignExecutingTimeQuery = EntitySet.Get<CampaignExecutingTimeEntity>();
            campaignExecutingTimeQuery = campaignExecutingTimeQuery.Where(x => x.CampaignId == query.CampaignId).OrderBy(d => d.FromTime);

            return QueryResult.Create(campaignExecutingTimeQuery, CampaignExecutingTimeInfo.FromEntity);
        }
    }
}
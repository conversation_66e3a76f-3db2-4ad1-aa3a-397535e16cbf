﻿using System;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using Webaby;
using Webaby.Core.UserAccount;
using Webaby.Core.UserAccount.Queries;
using Webaby.Data;
using Webaby.Localization;
using Webaby.Security;

namespace TinyCRM.Access
{
    public class GetUserProfileByAccessIdQuery : QueryBase<ApplicationUser>
    {
        public Guid CodeAccessId { get; set; }
    }

    internal class GetUserProfileByAccessIdQueryHandler : QueryHandlerBase<GetUserProfileByAccessIdQuery, ApplicationUser>
    {
        public GetUserProfileByAccessIdQueryHandler(IServiceProvider serviceProvider)
            : base(serviceProvider) { }

        public override async Task<QueryResult<ApplicationUser>> ExecuteAsync(GetUserProfileByAccessIdQuery query)
        {
            var userProfileEntities = from up in EntitySet.Get<AspNetUserEntity>()
                                      join uir in EntitySet.Get<AspNetUserRoleEntity>() on up.Id equals uir.UserId
                                      join rb in EntitySet.Get<RoleBusinessPermissionEntity>() on uir.RoleId equals rb.RoleId
                                      join ab in EntitySet.Get<AccessBusinessPermissionEntity>() on rb.BusinessPermissionId equals ab.BusinessPermissionId
                                      where ab.AccessId == query.CodeAccessId && up.IsApproved
                                      select up;
            var result = userProfileEntities.ToList();
            var mapped = result.Select(x => Mapper.Map<ApplicationUser>(x));
            return QueryResult.Create(mapped);
        }
    }
}

﻿using System.Linq;
using TinyCRM.DigitalChannel;
using TinyCRM.DigitalChannel.Queries;
using Webaby;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;
using System.Threading.Tasks;

namespace TinyCRM.DigitalContact.Queries
{
    public class GetDigitalChannelByCodeQuery : QueryBase<DigitalChannelData>
    {
        public string ChannelCode { get; set; }
    }

    internal class GetDigitalChannelByCodeQueryHandler : QueryHandlerBase<GetDigitalChannelByCodeQuery, DigitalChannelData>
    { 
        public GetDigitalChannelByCodeQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<DigitalChannelData>> ExecuteAsync(GetDigitalChannelByCodeQuery query)
        {
            var channelEntities = (await EntitySet.GetAsync<DigitalChannelEntity>()).OrderByDescending(x => x.CreatedDate);
            if (query.ChannelCode.IsNotNullOrEmpty())
            {
                channelEntities = (await EntitySet.GetAsync<DigitalChannelEntity>()).Where(x => x.ChannelCode.Contains(query.ChannelCode)).OrderByDescending(x => x.CreatedDate);
            }
            return QueryResult.Create(channelEntities, query.Pagination, x => Mapper.Map<DigitalChannelData>(x));
        }
    }
}
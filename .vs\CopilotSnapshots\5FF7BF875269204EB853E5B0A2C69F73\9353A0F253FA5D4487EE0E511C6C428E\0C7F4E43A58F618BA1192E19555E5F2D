﻿using System;
using System.Linq;
using TinyCRM.Customer;
using TinyCRM.Customer.Queries;
using TinyCRM.Outbound.Prospect;
using Webaby;

namespace TinyCRM.Outbound.Contact.Queries
{
    public class GetContactListByChienDichQuery : QueryBase<CustomerData>
    {
        public Guid ChienDichId
        {
            get;
            set;
        }

        public Guid? TeamId
        {
            get;
            set;
        }
    }

    internal class GetContactListByChienDichQueryHandler : QueryHandlerBase<GetContactListByChienDichQuery, CustomerData>
    {
        public override QueryResult<CustomerData> Execute(GetContactListByChienDichQuery query)
        {
            var contactQuery = EntitySet.Get<CustomerEntity>();
            var contactAssignmentQuery = EntitySet.Get<ProspectEntity>();

            var mainQuery = (from c in contactQuery
                             join ca in contactAssignmentQuery on c.Id equals ca.CustomerId
                             where ca.CampaignId == query.ChienDichId
                             orderby c.CreatedDate, c.Name
                             select c);

            return QueryResult.Create(mainQuery, CustomerData.FromEntity);
        }
    }
}
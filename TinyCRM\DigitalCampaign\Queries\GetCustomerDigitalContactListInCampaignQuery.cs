﻿using System;
using System.Data.SqlClient;
using Webaby.Data;
using Webaby;
using System.Data;
using System.Threading.Tasks;
using AutoMapper;
using Webaby.Localization;

namespace TinyCRM.DigitalCampaign.Queries
{
    public class GetCustomerDigitalContacRawtListInCampaignQuery : QueryBase<CampaignCustomerDigitalContactRawData>
    {
        public Guid CampaignId { get; set; }

        public Guid? ProspectAssignmentId { get; set; }
    }

    internal class GetCustomerDigitalContacRawtListInCampaignQueryHandler : QueryHandlerBase<GetCustomerDigitalContacRawtListInCampaignQuery, CampaignCustomerDigitalContactRawData>
    {
        public GetCustomerDigitalContacRawtListInCampaignQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<CampaignCustomerDigitalContactRawData>> ExecuteAsync(GetCustomerDigitalContacRawtListInCampaignQuery query)
        {
            var cmd = EntitySet.CreateDbCommand();
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@CampaignId", query.CampaignId));
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@ProspectAssignmentId", query.ProspectAssignmentId));
            cmd.CommandText = "dbo.GetCustomerDigitalContactListInCampaign";
            cmd.CommandType = CommandType.StoredProcedure;
            var mainQuery = await EntitySet.ExecuteReadCommandAsync<CampaignCustomerDigitalContactRawData>(cmd);
            return new QueryResult<CampaignCustomerDigitalContactRawData>(mainQuery);
        }
    }
}
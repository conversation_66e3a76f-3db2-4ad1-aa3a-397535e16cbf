﻿using AutoMapper;
using System;
using System.Collections.Generic;
using System.Linq;
using TinyCRM.AutomaticTask.Queries;
using Webaby;
using Webaby.Data;
using Webaby.Localization;
using Webaby.Security;

namespace TinyCRM.AutomaticTask.Command
{
    public class CreateEditAutoNextTaskListCommand : CommandBase
    {
        public Guid ReferenceObjectId { get; set; }

        public string ReferenceType { get; set; }

        public List<AutoNextTaskData> AutoNextTaskListItems { get; set; }
    }

    internal class CreateEditAutoNextTaskListCommandHandler : CommandHandlerBase<CreateEditAutoNextTaskListCommand>
    {
        public CreateEditAutoNextTaskListCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        IUserService _userService { get; set; }

        public override async Task ExecuteAsync(CreateEditAutoNextTaskListCommand command)
        {
            var existingEntities = EntitySet.Get<AutoNextTaskEntity>().Where(x => x.ReferenceObjectId == command.ReferenceObjectId).ToList();
            var saveEntities = (from x in command.AutoNextTaskListItems
                                join ea in existingEntities on x.Id equals ea.Id into _temp
                                from a in _temp.DefaultIfEmpty()
                                select new AutoNextTaskEntity
                                {
                                    Id = x.Id.HasValue ? x.Id.Value : Guid.NewGuid(),
                                    AutoConditionId = x.AutoConditionId,
                                    AssignedUserPathSelectorId = x.AssignedUserPathSelectorId,
                                    ReferenceObjectId = command.ReferenceObjectId,
                                    ReferenceType = command.ReferenceType,
                                    TaskBusinessResultId = x.TaskBusinessResultId,
                                    EventCondition = x.EventCondition,
                                    EventOrder = x.EventOrder,
                                    NextTaskFormula = x.NextTaskFormula,
                                    MultiTaskTriggered = x.MultiTaskTriggered,                                    
                                    CreatedDate = x.Id.HasValue ? a.CreatedDate : DateTime.Now,
                                    CreatedBy = x.Id.HasValue ? a.CreatedBy : _userService.GetCurrentUser().Id,
                                    DynamicFieldConditionId = x.DynamicFieldConditionId,
                                    NextTaskId = x.NextTaskId
                                });

            var deletedEntities = existingEntities.Where(x => command.AutoNextTaskListItems.All(y => y.Id != x.Id)).ToList();
            await Repository.SaveAsync(saveEntities);
            await Repository.DeleteAsync(deletedEntities);
        }
    }
}

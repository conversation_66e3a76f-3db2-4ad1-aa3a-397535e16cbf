﻿using System;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Webaby.Data;
using Webaby.Localization;
using Webaby;
using TinyCRM.Customer;
using TinyCRM.Customer.Queries;
using TinyCRM.Outbound.Prospect;

namespace TinyCRM.Outbound.Contact.Queries
{
    public class GetContactListByChienDichQuery : QueryBase<CustomerData>
    {
        public Guid ChienDichId { get; set; }
        public Guid? TeamId { get; set; }
    }

    internal class GetContactListByChienDichQueryHandler : QueryHandlerBase<GetContactListByChienDichQuery, CustomerData>
    {
        public GetContactListByChienDichQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<CustomerData>> ExecuteAsync(GetContactListByChienDichQuery query)
        {
            var contactQuery = EntitySet.Get<CustomerEntity>();
            var contactAssignmentQuery = EntitySet.Get<ProspectEntity>();

            var mainQuery = from c in contactQuery
                            join ca in contactAssignmentQuery on c.Id equals ca.CustomerId
                            where ca.CampaignId == query.ChienDichId
                            orderby c.CreatedDate, c.Name
                            select c;

            var items = await mainQuery.Select(x => new CustomerData
            {
                Id = x.Id,
                Name = x.Name,
                SubName = x.SubName,
                Code = x.Code,
                B2BCode = x.B2BCode,
                Type = x.Type,
                CMND = x.CMND,
               
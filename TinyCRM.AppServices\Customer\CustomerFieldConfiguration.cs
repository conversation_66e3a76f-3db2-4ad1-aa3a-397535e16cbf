﻿
namespace TinyCRM.AppServices.Customer
{
    public class CustomerFieldConfiguration
    {
        public Guid Id { get; set; }

        public string FieldName { get; set; }

        public string DataType { get; set; }

        public string DisplayName { get; set; }

        public string GroupName { get; set; }

        public int GroupOrder { get; set; }

        public string GroupCondition { get; set; }

        public int? GridSize { get; set; }

        public int? Order { get; set; }

        public bool Required { get; set; }

        public bool IsHidden { get; set; }

        public string UIHint { get; set; }

        public string FunctionName { get; set; }
    }
}
﻿using System;
using Webaby;
using System.Linq;
using System.Collections.Generic;

namespace TinyCRM.CustomerVersionName.Commands
{
    public class CreateEditCustomerVersionNameCommand : CommandBase
    {
        public Guid Id { get; set; }

        public string NameVersion { get; set; }

        public string MonthVersion { get; set; }

        public string YearVersion { get; set; }

        public string Code { get; set; }

        public int? NumberOfCustomer { get; set; }

        public bool BatchCreate { get; set; }
    }

    internal class CreateEditCustomerVersionNameCommandHandler : CommandHandlerBase<CreateEditCustomerVersionNameCommand>
    {
        public CreateEditCustomerVersionNameCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }
        public override async Task ExecuteAsync(CreateEditCustomerVersionNameCommand command)
        {
            if(command.BatchCreate)
            {
                var listEntity = new List<CustomerVersionNameEntity>();
                var lastImportDate = DateTime.Now;
                for (int i = 1; i<=12; i++)
                {
                    var monthCreated = i.ToString("00");
                    var textnameversion = command.YearVersion + (monthCreated.IsNotNullOrEmpty() ? "." + monthCreated : "") 
                        + (command.Code.IsNotNullOrEmpty() ? "." + command.Code : "");
                    var newId = Guid.NewGuid();
                    var check = CheckDupVersionName(textnameversion, newId);
                    if(check == false)
                    {
                        var entity = new CustomerVersionNameEntity();
                        entity.Id = newId;
                        entity.YearVersion = command.YearVersion;
                        entity.MonthVersion = monthCreated;
                        entity.Code = command.Code;
                        entity.NameVersion = textnameversion;                        
                        entity.LastImportDate = lastImportDate;                        
                        listEntity.Add(entity);
                    }
                }
                await Repository.SaveAsync(listEntity);

            } else
            {
                var textnameversion = command.YearVersion + (command.MonthVersion.IsNotNullOrEmpty() ? "." + command.MonthVersion : "")
                                     + (command.Code.IsNotNullOrEmpty() ? "." + command.Code : "");
                var checkDup = CheckDupVersionName(textnameversion, command.Id);
                if (checkDup == true)
                {
                    throw new Exception(T["Tên version đã tồn tại"]);
                }
                var entity = await EntitySet.GetAsync<CustomerVersionNameEntity>(command.Id);
                if (entity == null)
                {
                    entity = new CustomerVersionNameEntity();
                    entity.Id = command.Id;                                  
                    entity.YearVersion = command.YearVersion;
                    entity.MonthVersion = command.MonthVersion;
                    entity.Code = command.Code;
                    entity.NameVersion = textnameversion;
                    entity.LastImportDate = DateTime.Now;
                }
                else
                {
                    entity.YearVersion = command.YearVersion;
                    entity.MonthVersion = command.MonthVersion;
                    entity.Code = command.Code;
                    entity.NameVersion = textnameversion;
                    entity.LastImportDate = DateTime.Now;
                    entity.NumberOfCustomer = command.NumberOfCustomer != null ? command.NumberOfCustomer.Value : entity.NumberOfCustomer;
                }
                await Repository.SaveAsync(entity);
            }
        }

        private bool CheckDupVersionName(string name, Guid id)
        {
            var resultEntity = (from data in EntitySet.Get<CustomerVersionNameEntity>()
                                where (id != data.Id) && data.NameVersion == name
                           select data).FirstOrDefault();

            if (resultEntity != null)
            {
                return true;
            }
            return false;
        }
    }
}
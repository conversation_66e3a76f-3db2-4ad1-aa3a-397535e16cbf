﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;

namespace TinyCRM.Campaign.Queries
{
    public class GetCampaignWorkByCurrentAssginementIdQuery : QueryBase<CampaignWorkData>
    {
        public Guid CurrentAssignmentId { get; set; }
    }

    internal class GetCampaignWorkByCurrentAssginementIdQueryHandler : QueryHandlerBase<GetCampaignWorkByCurrentAssginementIdQuery, CampaignWorkData>
    {
        public GetCampaignWorkByCurrentAssginementIdQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<CampaignWorkData>> ExecuteAsync(GetCampaignWorkByCurrentAssginementIdQuery query)
        {
            var assignmentQuery = await EntitySet.GetAsync<CampaignAssignmentEntity>();
            var workQuery = await EntitySet.GetAsync<CampaignWorkEntity>();

            var mainQuery = (from w in workQuery
                             join a in assignmentQuery on w.CurrentAssignmentId equals a.Id
                             where a.Id == query.CurrentAssignmentId
                             select w);

            return QueryResult.Create(mainQuery, x => Mapper.Map<CampaignWorkData>(x));
        }
    }
}

﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TinyCRM.DynamicDefinedTable;
using Webaby;
using Webaby.Core.DynamicForm;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;

namespace TinyCRM.ServiceType.Commands
{
    public class ImportServiceTypeCommandCommand : CommandBase
    {
        public string Script { get; set; }
    }

    internal class ImportWorkflowCommandCommandHandler : CommandHandlerBase<ImportServiceTypeCommandCommand>
    {
        //[Import]
        //IDynamicDefinedTableUtility DynamicDefinedTableUtility { get; set; }

        public ImportWorkflowCommandCommandHandler(
            IText text,
            IMapper mapper,
            IRepository repository,
            IEntitySet entitySet,
            IQueryExecutor queryExecutor,
            ICommandExecutor commandExecutor,
            ILocalTransactionManager transactionManager,
            IEventBus eventBus
        ) : base(text, mapper, repository, entitySet, queryExecutor, commandExecutor, transactionManager, eventBus) { }

        public override async Task ExecuteAsync(ImportServiceTypeCommandCommand command)
        {
            var cmd = EntitySet.CreateDbCommand();
            cmd.CommandText = command.Script;
            cmd.CommandType = CommandType.Text;

            IEnumerable<ImportServiceTypeResult> importServiceTypeResults = await EntitySet.ExecuteReadCommandAsync<ImportServiceTypeResult>(cmd);
            if (importServiceTypeResults.Any())
            {
                ImportServiceTypeResult importServiceTypeResult = importServiceTypeResults.FirstOrDefault();
                if (importServiceTypeResult != null)
                {
                    #region Build Own Db TableSchema cho các Dynamic User Defined Table sử dụng OwnDbTable

                    ServiceTypeEntity serviceTypeEntity = await EntitySet.GetAsync<ServiceTypeEntity>(importServiceTypeResult.ServiceTypeId);
                    if (serviceTypeEntity != null)
                    {
                        if (serviceTypeEntity.DynamicFormId.IsNotNullOrEmpty())
                        {
                            List<DynamicFieldDefinitionEntity> dynamicFieldDefinitionEntities = (await EntitySet.GetAsync<DynamicFieldDefinitionEntity>()).Where(dfd => dfd.DynamicFormId == serviceTypeEntity.DynamicFormId).ToList();
                            foreach (var dynamicFieldDefinition in dynamicFieldDefinitionEntities)
                            {
                                if (dynamicFieldDefinition.DynamicDefinedTableSchemaId.IsNotNullOrEmpty())
                                {
                                    //DynamicDefinedTableUtility.BuildOwnDbTableSchema(dynamicFieldDefinition.DynamicDefinedTableSchemaId.Value);
                                }
                            }
                        }
                    }

                    #endregion
                }
            }
        }

        private class ImportServiceTypeResult
        {
            public Guid ServiceTypeId { get; set; }
        }
    }
}
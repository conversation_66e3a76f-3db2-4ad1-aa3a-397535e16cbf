﻿using AutoMapper;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Reflection;
using Webaby;

namespace TinyCRM.DigitalChannelMessageTemplate.Commands
{
    public class CreateEditDigitalMessageTemplateCommand : CommandBase
    {
        public Guid Id { get; set; }

        public Guid DigitalChannelId { get; set; }

        public Guid DigitalMessageTemplateId { get; set; }

        public Guid? DigitalContactTypeId { get; set; }

        public string Header { get; set; }

        public string Content { get; set; }

        public string ChannelSpecificJsonData { get; set; }

        public string Language { get; set; }        
    }

    internal class CreateEditEntityLinkCommandHandler : CommandHandlerBase<CreateEditDigitalMessageTemplateCommand>
    {
        public CreateEditEntityLinkCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task ExecuteAsync(CreateEditDigitalMessageTemplateCommand command)
        {
            var entity = await EntitySet.GetAsync<DigitalChannelMessageTemplateEntity>(command.Id);
            if(entity == null)
            {
                entity = new DigitalChannelMessageTemplateEntity();
                entity.Id = command.Id;
            }
            entity.DigitalChannelId = command.DigitalChannelId;
            entity.DigitalMessageTemplateId = command.DigitalMessageTemplateId;
            entity.DigitalContactTypeId = command.DigitalContactTypeId;
            entity.Header = command.Header;
            entity.Content = command.Content;
            entity.Language = command.Language;
            try
            {
                JsonConvert.DeserializeObject(command.ChannelSpecificJsonData);
                entity.ChannelSpecificJsonData = command.ChannelSpecificJsonData;
            }
            catch { }
            await Repository.SaveAsync(entity);
        }
    }
}
﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Webaby;
using HtmlAgilityPack;

namespace TinyCRM.ContentTemplate.Command
{
    public class CreateEditContentTemplateCommand : CommandBase
    {
        public Guid Id { get; set; }
        public string Name { get; set; }
        public string Title { get; set; }
        public string Content { get; set; }
        public bool? IsRawText { get; set; }
    }

    internal class CreateEditKnowledgeItemCommandHandler : CommandHandlerBase<CreateEditContentTemplateCommand>
    {
        public CreateEditKnowledgeItemCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }
        public override async Task ExecuteAsync(CreateEditContentTemplateCommand command)
        {
            var old = await EntitySet.GetAsync<ContentTemplateEntity>(command.Id);
            try
            {
                HtmlAgilityPack.HtmlDocument doc = new HtmlAgilityPack.HtmlDocument();
                var html = command.Content;
                doc.LoadHtml(html);
                doc.DocumentNode.Descendants().Where(x => x.Name.ToLower() == "script" || x.Name.ToLower() == "style").ToList().ForEach(x => x.Remove());
                doc.DocumentNode.Descendants().ToList().ForEach(x =>
                {
                    x.Attributes.ToList().ForEach(a =>
                    {
                        if (a.Name.ToLower().StartsWith("on"))
                        {
                            a.Remove();
                        }
                        if (a.Name.ToLower() == "style" && a.Value.ToLower().Contains("position"))
                        {
                            a.Remove();
                        }
                    });
                });
                command.Content = doc.DocumentNode.InnerHtml;
            }
            catch (Exception) { }
            if (old != null)
            {
                //old.Content = Sanitizer.GetSafeHtmlFragment(command.Content);
                old.Content = command.Content;
                old.Title = command.Title;
                old.Name = command.Name;
                old.IsRawText = command.IsRawText;
                await Repository.SaveAsync(old);
            }
            else
            {
                await Repository.SaveAsync(new ContentTemplateEntity
                {
                    //Content = Sanitizer.GetSafeHtmlFragment(command.Content),
                    Content = command.Content,
                    Id = command.Id,
                    Name = command.Name,
                    Title = command.Title,
                    IsRawText = command.IsRawText
                });
            }
        }
    }
}

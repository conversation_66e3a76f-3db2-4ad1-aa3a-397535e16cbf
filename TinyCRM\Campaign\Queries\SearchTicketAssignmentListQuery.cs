﻿using System;
using System.Data;
using System.Data.SqlClient;
using TinyCRM.Enums;
using Webaby;
using Webaby.Data;

namespace TinyCRM.Campaign.Queries
{
    public class SearchTicketAssignmentListQuery : QueryBase<TicketAssignmentListItem>
    {
        public Guid CampaignId { get; set; }

        public Guid? LoaiDichVuId { get; set; }

        public Guid? OrganizationId { get; set; }

        public AssignmentStatus? AssignmentStatus { get; set; }

        public Guid? OwnerId { get; set; }
    }

    internal class SearchTicketAssignmentListQueryHandler : QueryHandlerBase<SearchTicketAssignmentListQuery, TicketAssignmentListItem>
    {
        public SearchTicketAssignmentListQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<TicketAssignmentListItem>> ExecuteAsync(SearchTicketAssignmentListQuery query)
        {
            var cmd = EntitySet.CreateDbCommand();
            cmd.Parameters.AddRange(new[]
            {
                DbParameterHelper.AddNullableGuid(cmd, "@CampaignId", query.CampaignId),
                DbParameterHelper.AddNullableGuid(cmd, "@OrganizationId", query.OrganizationId),
                DbParameterHelper.AddNullableGuid(cmd, "@LoaiDichVuId", query.LoaiDichVuId),
                DbParameterHelper.AddNullableEnum(cmd, "@AssignmentStatus", query.AssignmentStatus),
                DbParameterHelper.AddNullableGuid(cmd, "@OwnerId", query.OwnerId),
                DbParameterHelper.AddNullableInt(cmd, "@StartRow", query.Pagination.StartRow),
                DbParameterHelper.AddNullableInt(cmd, "@EndRow", query.Pagination.EndRow)
            });

            cmd.CommandText = "SearchTicketAssignmentList";
            cmd.CommandType = CommandType.StoredProcedure;

            var mainQuery = await EntitySet.ExecuteReadCommandAsync<TicketAssignmentListItem>(cmd);

            return new QueryResult<TicketAssignmentListItem>(mainQuery);
        }
    }
}
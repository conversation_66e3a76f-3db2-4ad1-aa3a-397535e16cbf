﻿using System.Linq;
using TinyCRM.DigitalChannel;
using TinyCRM.DigitalChannel.Queries;
using Webaby;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;
using System.Threading.Tasks;

namespace TinyCRM.DigitalContact.Queries
{
    public class GetDigitalChannelByChannelTypeQuery : QueryBase<DigitalChannelData>
    {
        public ChannelType ChannelType { get; set; }
    }

    internal class GetDigitalChannelByChannelTypeQueryHandler : QueryHandlerBase<GetDigitalChannelByChannelTypeQuery, DigitalChannelData>
    { 
        public GetDigitalChannelByChannelTypeQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<DigitalChannelData>> ExecuteAsync(GetDigitalChannelByChannelTypeQuery query)
        {
            var channelEntities = (await EntitySet.GetAsync<DigitalChannelEntity>()).Where(x => x.ChannelType == query.ChannelType);
            return QueryResult.Create(channelEntities, x => Mapper.Map<DigitalChannelData>(x));
        }
    }
}
﻿using System;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using TinyCRM.ServiceCategory;
using Webaby;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.ServiceType.Queries
{
    public class GetServiceTypeDataByLevelQuery : QueryBase<ServiceTypeData>
    {
        public Guid? Level1Id { get; set; }

        public Guid? Level2Id { get; set; }

        public Guid? Level3Id { get; set; }

        public Guid? Level4Id { get; set; }
    }

    internal class GetServiceTypeDataByLevelQueryHandler : QueryHandlerBase<GetServiceTypeDataByLevelQuery, ServiceTypeData>
    {
        public GetServiceTypeDataByLevelQueryHandler(IEntitySet entitySet, IRepository repository, IText text, IMapper mapper)
            : base(entitySet, repository, text, mapper) { }

        public override async Task<QueryResult<ServiceTypeData>> ExecuteAsync(GetServiceTypeDataByLevelQuery query)
        {
            var stList = await EntitySet.GetAsync<ServiceTypeEntity>();
            var catList = await EntitySet.GetAsync<ServiceCategoryEntity>();
            var serviceTypes = (from st in stList
                                join lv1 in catList on st.Level1Id equals lv1.Id
                                join _lv2 in catList on st.Level2Id equals _lv2.Id into _tempLv2
                                from lv2 in _tempLv2.DefaultIfEmpty()
                                join _lv3 in catList on st.Level3Id equals _lv3.Id into _tempLv3
                                from lv3 in _tempLv3.DefaultIfEmpty()
                                join _lv4 in catList on st.Level4Id equals _lv4.Id into _tempLv4
                                from lv4 in _tempLv4.DefaultIfEmpty()
                                where (!query.Level1Id.HasValue || st.Level1Id == query.Level1Id)
                                && (!query.Level2Id.HasValue || st.Level2Id == query.Level2Id)
                                && (!query.Level3Id.HasValue || st.Level3Id == query.Level3Id)
                                && (!query.Level4Id.HasValue || st.Level4Id == query.Level4Id)
                                orderby lv1.Order, lv2.Order, lv3.Order, lv4.Order
                                select new ServiceTypeData
                                {
                                    Id = st.Id,
                                    
                                    Level1Id = st.Level1Id,
                                    Level1Name = lv1.Name,
                                    Level1Order = lv1.Order,

                                    Level2Id = st.Level2Id,
                                    Level2Name = lv2.Name,
                                    Level2Order = lv2.Order,

                                    Level3Id = st.Level3Id,
                                    Level3Name = lv3.Name,
                                    Level3Order = lv3.Order,

                                    Level4Id = st.Level4Id,
                                    Level4Name = lv4.Name,
                                    Level4Order = lv4.Order
                                });

            return QueryResult.Create(serviceTypes);
        }
    }
}
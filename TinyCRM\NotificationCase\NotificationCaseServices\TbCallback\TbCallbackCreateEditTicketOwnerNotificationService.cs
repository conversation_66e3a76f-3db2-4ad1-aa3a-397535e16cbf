﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using TinyCRM.NotificationCase.Commands;
using TinyCRM.RequestTicket.Queries;
using TinyCRM.TbCallback.Queries;
using Webaby;
using Webaby.Data;
using Webaby.Notification;
using Microsoft.EntityFrameworkCore;

namespace TinyCRM.NotificationCase.NotificationCaseServices.TbCallback
{
    [AutoRegisterService(typeof(INotificationCaseService), ServiceLifetime.Transient, "tbcallback.createedit.ticketowner")]
    public class TbCallbackCreateEditTicketOwnerNotificationService : INotificationCaseService
    {
        private static Guid _notificationChanelSettingId = Guid.Parse("6D5FC655-56EA-43D6-A0CC-D103F7975E74");

        private readonly IConfiguration _configuration;
        private readonly IEntitySet _entitySet;
        private readonly IQueryExecutor _queryExecutor;
        private readonly IRepository _repository;
        private readonly ICommandExecutor _commandExecutor;

        public TbCallbackCreateEditTicketOwnerNotificationService(
            IConfiguration configuration,
            IEntitySet entitySet,
            IQueryExecutor queryExecutor,
            ICommandExecutor commandExecutor,
            IRepository repository)
        {
            _configuration = configuration;
            _entitySet = entitySet;
            _queryExecutor = queryExecutor;
            _commandExecutor = commandExecutor;
            _repository = repository;
        }

        public string ApplicationUrl { get { return _configuration.GetValue<string>("application.url"); } }

        public int CallbackCustomerInTicketBeforeMinutes { get { return _configuration.GetValue<int>("ticket.tbcallback.notification.before.minutes"); } }

        public void Close(Guid rootEntityId)
        {
            _commandExecutor.ExecuteAsync(new CloseNotificationCaseCommand { RootEntityId = rootEntityId, NotificationChanelSettingId = _notificationChanelSettingId }).Wait();
        }

        public List<Guid> Create(object rootEntity, Dictionary<string, string> extendedTemplateParams = null)
        {
            if (rootEntity != null)
            {
                var tbCallback = rootEntity as TbCallbackData;
                if (tbCallback != null)
                {
                    int callbackCustomerInTicketBeforeMinutes = 10;
                    if (CallbackCustomerInTicketBeforeMinutes > 0)
                    {
                        callbackCustomerInTicketBeforeMinutes = CallbackCustomerInTicketBeforeMinutes;
                    }
                    var requestTicketData =   _queryExecutor.ExecuteOneAsync(new GetRequestTicketByIdQuery(tbCallback.ReferenceObjectId)).Result;
                    Guid notiCaseId = Guid.NewGuid();
                    _commandExecutor.ExecuteAsync(new CreateNotificationCaseCommand
                    {
                        Id = notiCaseId,
                        NotificationChanelSettingId = _notificationChanelSettingId,
                        EmailType = "Email nội bộ",
                        ToUserId = requestTicketData == null || requestTicketData.OwnerId.IsNullOrEmpty() ? tbCallback.CreatedBy : requestTicketData.OwnerId.Value,
                        ToUserType = NotificationCaseToUserType.ApplicationUser,
                        RootEntityId = tbCallback.Id,
                        NotificationTypeId = NotificationType.Callback,
                        NotifiedDate = tbCallback.NextReminderTime.AddMinutes(-1 * callbackCustomerInTicketBeforeMinutes),
                        ReferenceObjectId = tbCallback.Id,
                        ReferenceObjectType = "TbCallback",
                        Link = string.Format("{0}/RequestTicket/Edit?RequestTicketId={1}", ApplicationUrl.Trim('/'), tbCallback.ReferenceObjectId)
                    }).Wait();
                    return new List<Guid> { notiCaseId };
                }
            }
            return new List<Guid> { Guid.Empty };
        }

        public List<Guid> CreateWithId(Guid rootEntityId, Dictionary<string, string> extendedTemplateParams = null)
        {
            var tbCallbackData = _queryExecutor.ExecuteOneAsync(new GetTbCallbackByIdQuery { Id = rootEntityId }).Result;
            if (tbCallbackData != null)
            {
                return Create(tbCallbackData);
            }
            return new List<Guid> { Guid.Empty };
        }
    }
}

﻿using AutoMapper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TinyCRM.TaskType;
using Webaby;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.BusinessResult.Commands
{
    public class DeleteTaskTypeBusinessResultCommand : CommandBase
    {
        public Guid TaskTypeId { get; set; }

        public Guid BusinessResultId { get; set; }
    }

    internal class DeleteTaskTypeBusinessResultCommandHandler : CommandHandlerBase<DeleteTaskTypeBusinessResultCommand>
    {
        public DeleteTaskTypeBusinessResultCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task ExecuteAsync(DeleteTaskTypeBusinessResultCommand command)
        {
            BusinessResultReferenceEntity taskTypeBusinessResultEntity = EntitySet.Get<BusinessResultReferenceEntity>().Where(ttbr => ttbr.ReferenceObjectId == command.TaskTypeId && ttbr.BusinessResultId == command.BusinessResultId).SingleOrDefault();
            if (taskTypeBusinessResultEntity != null)
            {
                await Repository.DeleteAsync(taskTypeBusinessResultEntity);
            }
        }
    }
}
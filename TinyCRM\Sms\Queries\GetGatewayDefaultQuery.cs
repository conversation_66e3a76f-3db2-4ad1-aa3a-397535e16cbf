﻿using System.Linq;
using System.Threading.Tasks;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;
using Webaby;

namespace TinyCRM.Sms.Queries
{
    public class GetGatewayDefaultQuery : QueryBase<GatewayData>
    {
    }

    internal class GetGatewayDefaultQueryHandler : QueryHandlerBase<GetGatewayDefaultQuery, GatewayData>
    {
        public GetGatewayDefaultQueryHandler(IEntitySet entitySet, IRepository repository, IText text, IMapper mapper)
            : base(entitySet, repository, text, mapper) { }

        public override async Task<QueryResult<GatewayData>> ExecuteAsync(GetGatewayDefaultQuery query)
        {
            var mainQuery = (await EntitySet.GetAsync<GatewayEntity>()).Where(x => x.IsDefault == true);
            return QueryResult.Create(mainQuery, x => Mapper.Map<GatewayData>(x));
        }
    }
}

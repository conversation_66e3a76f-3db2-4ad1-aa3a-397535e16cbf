﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TinyCRM.Enums;
using Webaby;
using Webaby.Core.File.Queries;
using Webaby.Data;

namespace TinyCRM.Campaign.Queries
{
    public class GetCampaignFileFromEntityLinkQuery : QueryBase<FileData>
    {
        public Guid FromEntityId { get; set; }
    }

    internal class GetCampaignFileFromEntityLinkQueryHandler : QueryHandlerBase<GetCampaignFileFromEntityLinkQuery, FileData>
    {
        public GetCampaignFileFromEntityLinkQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<FileData>> ExecuteAsync(GetCampaignFileFromEntityLinkQuery query)
        {
            var cmd = EntitySet.CreateDbCommand();
            cmd.Parameters.AddRange(new[]
            {
                DbParameterHelper.AddNullableGuid(cmd, "@FromEntityId", query.FromEntityId),
            });
            cmd.CommandText = "dbo.GetCampaignFileFromEntityLink";
            cmd.CommandType = CommandType.StoredProcedure;
            var mainQuery = await EntitySet.ExecuteReadCommandAsync<FileData>(cmd);
            return new QueryResult<FileData>(mainQuery);
        }
    }
}

﻿using System;
using System.Data.SqlClient;
using Webaby.Data;
using Webaby;
using System.Data;
using System.Threading.Tasks;
using AutoMapper;
using Webaby.Localization;

namespace TinyCRM.DigitalCampaign.Queries
{
    public class GetCampaignDataSummaryForCostEstimationQuery : QueryBase<CampaignSummaryForCostEstimationData>
    {
        public Guid CampaignId { get; set; } 
    }

    internal class GetCampaignDataSummaryForCostEstimationQueryHandler : QueryHandlerBase<GetCampaignDataSummaryForCostEstimationQuery, CampaignSummaryForCostEstimationData>
    {
        public GetCampaignDataSummaryForCostEstimationQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<CampaignSummaryForCostEstimationData>> ExecuteAsync(GetCampaignDataSummaryForCostEstimationQuery query)
        {
            var cmd = EntitySet.CreateDbCommand();
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@CampaignId", query.CampaignId));
            cmd.CommandText = "dbo.GetCampaignDataSummaryForCostEstimation";
            cmd.CommandType = CommandType.StoredProcedure;
            var mainQuery = await EntitySet.ExecuteReadCommandAsync<CampaignSummaryForCostEstimationData>(cmd);
            return new QueryResult<CampaignSummaryForCostEstimationData>(mainQuery);
        }
    }
}
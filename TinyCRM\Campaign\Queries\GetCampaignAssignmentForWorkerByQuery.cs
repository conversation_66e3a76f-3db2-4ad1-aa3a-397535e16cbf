﻿using Webaby.Data;
using AutoMapper;
using Webaby.Localization;
using Webaby;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace TinyCRM.Campaign.Queries
{
    public class GetCampaignAssignmentForWorkerByQuery : QueryBase<CampaignAssignmentData>
    {
        public Guid WorkerId { get; set; }
    }

    internal class GetCampaignAssignmentForWorkerQueryHandler : QueryHandlerBase<GetCampaignAssignmentForWorkerByQuery, CampaignAssignmentData>
    {
        public GetCampaignAssignmentForWorkerQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<CampaignAssignmentData>> ExecuteAsync(GetCampaignAssignmentForWorkerByQuery query)
        {
            var campWorkerList = await EntitySet.GetAsync<CampaignWorkerEntity>();
            var campAssignList = await EntitySet.GetAsync<CampaignAssignmentEntity>();
            var assignQuery = from campWorker in campWorkerList
                              join campAssign in campAssignList on campWorker.UserId equals campAssign.OwnerId
                              where campWorker.Id == query.WorkerId
                              select campAssign;
            return QueryResult.Create(assignQuery, x => Mapper.Map<CampaignAssignmentData>(x));
        }
    }
}

﻿using System;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.EntityFrameworkCore;
using TinyCRM.Outbound.ContactCall;
using Webaby;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.Outbound.Appointment.Queries
{
    public class GetAppointmentListByProspectAssignmentQuery : QueryBase<AppointmentInfo>
    {
        public Guid ProspectAssignmentId { get; set; }
    }

    internal class GetAppointmentListByProspectAssignmentQueryHandler : QueryHandlerBase<GetAppointmentListByProspectAssignmentQuery, AppointmentInfo>
    {
        public GetAppointmentListByProspectAssignmentQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<AppointmentInfo>> ExecuteAsync(GetAppointmentListByProspectAssignmentQuery query)
        {
            var appointmentQuery = EntitySet.Get<AppointmentEntity>();
            var contactCallQuery = EntitySet.Get<ContactCallEntity>();

            var mainQuery = (from ap in appointmentQuery
                             join cc in contactCallQuery on ap.ContactCallId equals cc.Id
                             where cc.ProspectAssignmentId == query.ProspectAssignmentId
                             orderby ap.CreatedDate descending
                             select ap);

            var entities = await mainQuery.ToListAsync();
            var mapped = entities.Select(x => Mapper.Map<AppointmentInfo>(x));
            return QueryResult.Create(mapped);
        }
    }
}
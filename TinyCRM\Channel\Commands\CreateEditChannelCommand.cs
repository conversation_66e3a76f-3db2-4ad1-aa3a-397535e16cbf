﻿using AutoMapper;
using Webaby.Data;
using Webaby;
using Webaby.Localization;

namespace TinyCRM.Channel.Commands
{
    public class CreateEditChannelCommand : CommandBase
    {
        public Guid Id { get; set; }

        public string Name { get; set; }

        public int Code { get; set; }

        public bool IsDisabled { get; set; }
    }

    internal class CreateEditChannelCommandHandler : CommandHandlerBase<CreateEditChannelCommand>
    {
        public CreateEditChannelCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task ExecuteAsync(CreateEditChannelCommand command)
        {
            var entity = await EntitySet.GetAsync<ChannelEntity>(command.Id);
            bool isSystem = false;
            if (entity == null)
            {
                entity = new ChannelEntity();
            }
            else
            {
                isSystem = entity.IsSystem;
            }

            Mapper.Map(command, entity);
            if (isSystem)
            {
                entity.IsDisabled = command.IsDisabled;
            }

            await Repository.SaveAsync(entity);
        }
    }
}
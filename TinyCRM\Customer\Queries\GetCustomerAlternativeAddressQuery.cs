﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TinyCRM.RequestTicket.Queries;
using Webaby;
using Webaby.Data;

namespace TinyCRM.Customer.Queries
{
    public class GetCustomerAlternativeAddressQuery : QueryBase<CustomerAlternativeAddressData>
    {
        public string Name { get; set; }

        public Guid CustomerId { get; set; }

        public Guid? RegionId { get; set; }

        public Guid? AreaId { get; set; }

        public Guid? ProvinceId { get; set; }

        public Guid? DistrictId { get; set; }

        public Guid? WardId { get; set; }
    }

    internal class SearchCustomerAlternativeAddressQueryHandler :
        QueryHandlerBase<GetCustomerAlternativeAddressQuery, CustomerAlternativeAddressData>
    {
        public SearchCustomerAlternativeAddressQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<CustomerAlternativeAddressData>> ExecuteAsync(GetCustomerAlternativeAddressQuery query)
        {
            int startRow = query.Pagination.Index * query.Pagination.Size + 1;
            int endRow = query.Pagination.Index * query.Pagination.Size + query.Pagination.Size;
            var cmd = EntitySet.CreateDbCommand();
            cmd.Parameters.AddRange(new[]
            {
                DbParameterHelper.AddNullableGuid(cmd, "@customerId", query.CustomerId), 
                DbParameterHelper.AddNullableString(cmd, "@name", query.Name),
                DbParameterHelper.AddNullableGuid(cmd, "@regionId", query.RegionId),
                DbParameterHelper.AddNullableGuid(cmd, "@areaId", query.AreaId),
                DbParameterHelper.AddNullableGuid(cmd, "@provinceid", query.ProvinceId),
                DbParameterHelper.AddNullableGuid(cmd, "@districtId", query.DistrictId),
                DbParameterHelper.AddNullableGuid(cmd, "@wardId", query.WardId),
                DbParameterHelper.AddNullableInt(cmd, "@startRow", startRow),
                DbParameterHelper.AddNullableInt(cmd, "@endRow", endRow),
            });
            cmd.CommandText = "SearchCustomerAlternativeAddress";
            cmd.CommandType = CommandType.StoredProcedure;
            var mainQuery = await EntitySet.ExecuteReadCommandAsync<CustomerAlternativeAddressData>(cmd);
            return new QueryResult<CustomerAlternativeAddressData>(mainQuery);

        }
    }
}

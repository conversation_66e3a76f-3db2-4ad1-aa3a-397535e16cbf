﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using TinyCRM.Campaign;
using TinyCRM.Customer;
using TinyCRM.DigitalChannel;
using Webaby;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.DigitalContact.Queries
{
    public class GetHistoryCustomerLinkDigitalContactByCustomerIdQuery : QueryBase<DigitalContactInfoReuslt>
    {
        public Guid CustomerId { get; set; }
    }

    internal class GetHistoryCustomerLinkDigitalContactByCustomerIdQueryHandler : QueryHandlerBase<GetHistoryCustomerLinkDigitalContactByCustomerIdQuery, DigitalContactInfoReuslt>
    { 
        public GetHistoryCustomerLinkDigitalContactByCustomerIdQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<DigitalContactInfoReuslt>> ExecuteAsync(GetHistoryCustomerLinkDigitalContactByCustomerIdQuery query)
        {
            var mainquery = new List<DigitalContactInfoReuslt>();
            var entitychangeList = await EntitySet.GetAsync<AuditEntityChangeEntity>();
            var fieldchangeList = await EntitySet.GetAsync<AuditFieldChangeEntity>();
            var dcList = await EntitySet.GetAsync<DigitalContactEntity>();
            var dctList = await EntitySet.GetAsync<DigitalContactTypeEntity>();
            mainquery = (from entitychange in entitychangeList
                         join fieldchange in fieldchangeList on entitychange.Id equals fieldchange.AuditId                         
                         join dc in dcList on entitychange.KeyValue equals dc.Id
                         join dct in dctList on dc.DigitalContactTypeId equals dct.Id
                         where entitychange.TableName == "dbo.DigitalContact" && fieldchange.OldValue.ToLower() == query.CustomerId.ToString().ToLower() && entitychange.Action.ToString() == "Update"
                         select new DigitalContactInfoReuslt
                         {
                             ContactTypeId = dct.Id,
                             UserId = dc.UserId,
                             DigitalContactTypeName = dct.Name
                         }).ToList();
            return new QueryResult<DigitalContactInfoReuslt>(mainquery);
        }
    }
}
﻿using System;
using Webaby;

namespace TinyCRM.Outbound.CallResult.Queries
{
    public class GetResultCodeSuiteByIdQuery : QueryBase<ResultCodeSuiteData>
    {
        public Guid Id
        {
            get;
            set;
        }
    }

    internal class GetResultCodeSuiteByIdQueryHandler : QueryHandlerBase<GetResultCodeSuiteByIdQuery, ResultCodeSuiteData>
    {
        public override QueryResult<ResultCodeSuiteData> Execute(GetResultCodeSuiteByIdQuery query)
        {
            var suiteResult = EntitySet.Get<ResultCodeSuiteEntity>(query.Id);

            if (suiteResult == null) throw new InvalidOperationException(T["Không tìm  thấy bộ ResultCode có id '{0}'", query.Id]);
            return new QueryResult<ResultCodeSuiteData>(ResultCodeSuiteData.FromEntity(suiteResult));
        }
    }
}
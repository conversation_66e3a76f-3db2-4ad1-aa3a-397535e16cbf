﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Threading.Tasks;
using TinyCRM.Enums;
using Webaby;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;

namespace TinyCRM.Campaign.Commands
{
    public class AddWorkTicketsToCampaignCommand : CommandBase
    {
        public Guid CampaignId { get; set; }

        public Guid? OrganizationId { get; set; }

        public DateTime? CreatedFromDate { get; set; }

        public DateTime? CreatedToDate { get; set; }

        public TimeSpan? CreatedFromTime { get; set; }

        public TimeSpan? CreatedToTime { get; set; }

        public DateTime? ClosedFromDate { get; set; }

        public DateTime? ClosedToDate { get; set; }

        public TimeSpan? ClosedFromTime { get; set; }

        public TimeSpan? ClosedToTime { get; set; }

        public Guid? CreatedBy { get; set; }

        public Guid? ServiceTypeId { get; set; }

        public RequestTicketStatus? Status { get; set; }

        public int? DoInsertNumber { get; set; }

        public List<Guid> SelectedTicketIds { get; set; }

        public Guid DoInsertBy { get; set; }
    }

    internal class AddWorkTicketsToCampaignCommandHandler : CommandHandlerBase<AddWorkTicketsToCampaignCommand>
    {
        public AddWorkTicketsToCampaignCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task ExecuteAsync(AddWorkTicketsToCampaignCommand command)
        {
            var cmd = EntitySet.CreateDbCommand();
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@CampaignId", command.CampaignId));
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@OrganizationId", command.OrganizationId));
            cmd.Parameters.Add(DbParameterHelper.NewNullableDateTimeParameter(cmd, "@CreatedDateFrom", command.CreatedFromDate));
            cmd.Parameters.Add(DbParameterHelper.NewNullableDateTimeParameter(cmd, "@CreatedDateTo", command.CreatedToDate));
            cmd.Parameters.Add(DbParameterHelper.NewNullableDateTimeParameter(cmd, "@ClosedDateFrom", command.ClosedFromDate));
            cmd.Parameters.Add(DbParameterHelper.NewNullableDateTimeParameter(cmd, "@ClosedDateTo", command.ClosedToDate));
            cmd.Parameters.Add(DbParameterHelper.NewNullableTimeSpanParameter(cmd, "@CreatedTimeFrom", command.CreatedFromTime));
            cmd.Parameters.Add(DbParameterHelper.NewNullableTimeSpanParameter(cmd, "@CreatedTimeTo", command.CreatedToTime));
            cmd.Parameters.Add(DbParameterHelper.NewNullableTimeSpanParameter(cmd, "@ClosedTimeFrom", command.ClosedFromTime));
            cmd.Parameters.Add(DbParameterHelper.NewNullableTimeSpanParameter(cmd, "@ClosedTimeTo", command.ClosedToTime));
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@CreatedBy", command.CreatedBy));
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@ServiceTypeId", command.ServiceTypeId));
            cmd.Parameters.Add(DbParameterHelper.AddNullableString(cmd, "@Status", command.Status.HasValue ? command.Status.Value.ToString() : string.Empty));
            cmd.Parameters.Add(DbParameterHelper.AddNullableInt(cmd, "@DoInsertNumber", command.DoInsertNumber));
            cmd.Parameters.Add(DbParameterHelper.NewIdListParameter("@SelectedTicketIds", command.SelectedTicketIds));
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@DoInsertBy", command.DoInsertBy));

            cmd.CommandText = "Campaign_AddWorks_AddTickets";
            cmd.CommandType = CommandType.StoredProcedure;

            await EntitySet.ExecuteNonQueryAsync(cmd);
        }
    }
}

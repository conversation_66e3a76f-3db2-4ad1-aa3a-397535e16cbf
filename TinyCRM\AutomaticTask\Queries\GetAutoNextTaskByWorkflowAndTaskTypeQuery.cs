﻿using Webaby.Data;
using AutoMapper;
using Webaby.Localization;
using Webaby;
using System.Threading.Tasks;
using System;
using System.Linq;
using TinyCRM.BusinessResult;
using TinyCRM.Workflow;

namespace TinyCRM.AutomaticTask.Queries
{
    public class GetAutoNextTaskByWorkflowAndTaskTypeQuery : QueryBase<AutoNextTaskData>
    {
        public Guid WorkflowId { get; set; }
        public Guid TaskTypeId { get; set; }
        public Guid? TaskBusinessResultId { get; set; }
    }

    internal class GetAutoNextTaskByWorkflowAndTaskTypeQueryHandler : QueryHandlerBase<GetAutoNextTaskByWorkflowAndTaskTypeQuery, AutoNextTaskData>
    {
        public GetAutoNextTaskByWorkflowAndTaskTypeQueryHandler(IServiceProvider serviceProvider)
            : base(serviceProvider) { }

        public override async Task<QueryResult<AutoNextTaskData>> ExecuteAsync(GetAutoNextTaskByWorkflowAndTaskTypeQuery query)
        {
            var autoNextTaskQuery = EntitySet.Get<AutoNextTaskEntity>();
            if (query.TaskBusinessResultId.IsNotNullOrEmpty())
            {
                autoNextTaskQuery = autoNextTaskQuery.Where(atnt => atnt.TaskBusinessResultId.HasValue == false || atnt.TaskBusinessResultId == query.TaskBusinessResultId);
            }
            var mainQuery = from workflowTaskType in EntitySet.Get<WorkflowTaskTypeEntity>()
                            join autoNextTask in autoNextTaskQuery on workflowTaskType.Id equals autoNextTask.ReferenceObjectId
                            join _userPathSelector in EntitySet.Get<UserPathSelectorEntity>() on autoNextTask.AssignedUserPathSelectorId equals _userPathSelector.Id into _tempUserPathSelector
                            from userPathSelector in _tempUserPathSelector.DefaultIfEmpty()
                            join _autoCondition in EntitySet.Get<AutoConditionEntity>() on autoNextTask.AutoConditionId equals _autoCondition.Id into _tempAutoCondition
                            from autoCondition in _tempAutoCondition.DefaultIfEmpty()
                            where workflowTaskType.WorkflowId == query.WorkflowId && workflowTaskType.TaskTypeId == query.TaskTypeId
                            orderby autoNextTask.EventOrder
                            select new AutoNextTaskData
                            {
                                Id = autoNextTask.Id,
                                AutoAction = autoNextTask.AutoAction,
                                ReferenceObjectId = autoNextTask.ReferenceObjectId,
                                ReferenceType = autoNextTask.ReferenceType,
                                EventOrder = autoNextTask.EventOrder,
                                EventCondition = autoNextTask.EventCondition,
                                TriggeredEvent = autoNextTask.TriggeredEvent,
                                NextTaskFormula = autoNextTask.NextTaskFormula,
                                AssignedUserPathSelectorId = autoNextTask.AssignedUserPathSelectorId,
                                AssignedUserPathSelector = userPathSelector.Path,
                                AssignedUserPathSelectorName = userPathSelector.Name,
                                AutoConditionId = autoNextTask.AutoConditionId,
                                AutoCondition = autoCondition.Condition,
                                MultiTaskTriggered = autoNextTask.MultiTaskTriggered,
                                NextTaskId = autoNextTask.NextTaskId,
                                DynamicFieldConditionId = autoNextTask.DynamicFieldConditionId,
                                DynamicFieldConditionValue = autoNextTask.DynamicFieldConditionValue,
                                RequestTicketClosedBusinessResultId = autoNextTask.RequestTicketClosedBusinessResultId,
                                FreeConditionalStatement = autoNextTask.FreeConditionalStatement,
                                WorkflowTaskTypeClosedBusinessResultId = autoNextTask.WorkflowTaskTypeClosedBusinessResultId,
                                ObjectApproveType = autoNextTask.ObjectApproveType,
                                ObjectApproveAction = autoNextTask.ObjectApproveAction,
                                TaskAssignmentRouting = autoNextTask.TaskAssignmentRouting
                            };
            var result = mainQuery.ToList();
            return new QueryResult<AutoNextTaskData>(result);
        }
    }
}

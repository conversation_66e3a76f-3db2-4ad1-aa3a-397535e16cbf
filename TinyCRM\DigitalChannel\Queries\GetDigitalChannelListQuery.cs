﻿using System.Linq;
using TinyCRM.DigitalChannel;
using TinyCRM.DigitalChannel.Queries;
using Webaby;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;
using System.Threading.Tasks;

namespace TinyCRM.Channel.Queries
{
    public class GetDigitalChannelListQuery : QueryBase<DigitalChannelData>
    { 
    }

    internal class GetDigitalChannelListQueryHandler : QueryHandlerBase<GetDigitalChannelListQuery, DigitalChannelData>
    {
        public GetDigitalChannelListQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<DigitalChannelData>> ExecuteAsync(GetDigitalChannelListQuery query)
        {
            var entity = (await EntitySet.GetAsync<DigitalChannelEntity>()).Where(x => x.Deleted == false);
            return QueryResult.Create(entity, x => Mapper.Map<DigitalChannelData>(x));
        }
    }
}
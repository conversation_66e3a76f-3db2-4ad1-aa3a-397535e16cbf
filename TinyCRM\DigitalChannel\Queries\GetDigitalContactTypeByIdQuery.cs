﻿using System;
using System.Linq;
using System.Collections.Generic;
using TinyCRM.DigitalChannel;
using TinyCRM.DigitalChannel.Queries;
using Webaby;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;
using System.Threading.Tasks;

namespace TinyCRM.Channel.Queries
{
    public class GetDigitalContactTypeByIdQuery : QueryBase<DigitalContactTypeData>
    { 
        public Guid Id { get; set; }
    }

    internal class GetDigitalContactTypeByIdQueryHandler : QueryHandlerBase<GetDigitalContactTypeByIdQuery, DigitalContactTypeData>
    {
        public GetDigitalContactTypeByIdQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<DigitalContactTypeData>> ExecuteAsync(GetDigitalContactTypeByIdQuery query)
        {
            var entity = (await EntitySet.GetAsync<DigitalContactTypeEntity>()).Where(x => x.Id == query.Id);
            return QueryResult.Create(entity, x => Mapper.Map<DigitalContactTypeData>(x));
        }
    }
}
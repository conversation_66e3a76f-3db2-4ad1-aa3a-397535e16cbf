﻿using AutoMapper;
using System;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.AutomaticTask.Command
{
    public class CreateEditUserPathSelectorCommand : CommandBase
    {
        public Guid Id { get; set; }

        public string Name { get; set; }

        public string Path { get; set; } 
    }

    internal class CreateEditUserPathSelectorCommandHandler : CommandHandlerBase<CreateEditUserPathSelectorCommand>
    {
        public CreateEditUserPathSelectorCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task ExecuteAsync(CreateEditUserPathSelectorCommand command)
        {
            var entity = await EntitySet.GetAsync<UserPathSelectorEntity>(command.Id);
            if (entity == null)
            {
                entity = new UserPathSelectorEntity();
            }
            Mapper.Map(command, entity);
            await Repository.SaveAsync(entity);
        }
    }
}

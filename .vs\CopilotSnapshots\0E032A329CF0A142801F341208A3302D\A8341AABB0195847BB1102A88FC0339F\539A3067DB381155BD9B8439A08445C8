﻿using System;
using Webaby;

namespace TinyCRM.Outbound.CallResult.Queries
{
    public class GetCallResultByIdQuery : QueryBase<CallResultData>
    {
        public Guid Id
        {
            get;
            set;
        }
    }

    internal class GetCallResultByIdQueryHandler : QueryHandlerBase<GetCallResultByIdQuery, CallResultData>
    {
        public override QueryResult<CallResultData> Execute(GetCallResultByIdQuery query)
        {
            var callResult = EntitySet.Get<CallResultEntity>(query.Id);

            if (callResult == null) throw new InvalidOperationException(T["Không tìm  thấy CallResult có id '{0}'", query.Id]);
            return new QueryResult<CallResultData>(CallResultData.FromEntity(callResult));
        }
    }
}
﻿using System;
using System.Data;
using System.Data.SqlClient;
using AutoMapper;
using TinyCRM.Enums;
using Webaby;
using Webaby.Data;
using Webaby.Localization;
using Webaby.Validation.PhoneNumber;
using System.Threading.Tasks;

namespace TinyCRM.Customer.Commands
{
    public class UpdateCustomerExtendedFieldsCommand : CommandBase
    {
        public Guid Id { get; set; }

        #region Currently, ONLY update Phone2, Phone3 if Backend Customer

        public string Phone2 { get; set; }

        public string Phone3 { get; set; }

        #endregion
    }

    internal class UpdateCustomerExtendedFieldsCommandHandler : CommandHandlerBase<UpdateCustomerExtendedFieldsCommand>
    {
        IText _text { get; set; }
        public UpdateCustomerExtendedFieldsCommandHandler(IServiceProvider serviceProvider, IText text) : base(serviceProvider) { _text = text; }

        public override async Task ExecuteAsync(UpdateCustomerExtendedFieldsCommand command)
        {
            var entity = await EntitySet.GetAsync<CustomerEntity>(command.Id);
            if (entity == null)
            {
                throw new Exception(T["Không tìm thấy khách hàng có Id: {0}", command.Id]);
            }

            string phone2 = PhoneNumberValidator.Input(command.Phone2, _text).Output;
            string phone3 = PhoneNumberValidator.Input(command.Phone3, _text).Output;

            entity.Phone2 = phone2.IsNullOrEmpty() ? string.Empty : phone2.Trim();
            entity.Phone3 = phone3.IsNullOrEmpty() ? string.Empty : phone3.Trim();

            await Repository.SaveAsync(entity);
        }
    }
}
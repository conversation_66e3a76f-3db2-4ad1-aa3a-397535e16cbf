﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Webaby;

namespace TinyCRM.DigitalChannel.Commands
{
    public class DeleteDigitalChannelCommand : CommandBase
    {
        public Guid Id { get; set; }
    }

    internal class DeleteDigitalChannelCommandHandler : CommandHandlerBase<DeleteDigitalChannelCommand>
    {
        public DeleteDigitalChannelCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task ExecuteAsync(DeleteDigitalChannelCommand command)
        {
            var entity = await EntitySet.GetAsync<DigitalChannelEntity>(command.Id);
            await Repository.DeleteAsync(entity);
        }
    }
}

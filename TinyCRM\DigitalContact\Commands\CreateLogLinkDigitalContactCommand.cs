﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;

namespace TinyCRM.DigitalContact.Commands
{
    public class CreateLogLinkDigitalContactCommand : CommandBase
    {
        public Guid CustomerId { get; set; }
        public string PhoneNumber { get; set; }
        public Guid ContactTypePhone { get; set; }
        public string UID { get; set; }
        public Guid ContactTypeUID { get; set; }
    }

    internal class CreateLogLinkDigitalContactCommandHandler : CommandHandlerBase<CreateLogLinkDigitalContactCommand>
    {
        public CreateLogLinkDigitalContactCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task ExecuteAsync(CreateLogLinkDigitalContactCommand command)
        {
            var cmd = EntitySet.CreateDbCommand();
            cmd.CommandTimeout = 90;
            cmd.Parameters.AddRange(new[]
            {
                DbParameterHelper.AddNullableGuid(cmd, "@CustomerId", command.CustomerId),
                DbParameterHelper.AddNullableString(cmd, "@PhoneNumber", command.PhoneNumber),
                DbParameterHelper.AddNullableGuid(cmd, "@ContactTypePhone", command.ContactTypePhone),
                DbParameterHelper.AddNullableString(cmd, "@UID", command.UID),
                DbParameterHelper.AddNullableGuid(cmd, "@ContactTypeUID", command.ContactTypeUID)
            });

            cmd.CommandText = "dbo.CreateLogLinkDigitalContact";
            cmd.CommandType = CommandType.StoredProcedure;
            await EntitySet.ExecuteNonQueryAsync(cmd);
        }
    }
}
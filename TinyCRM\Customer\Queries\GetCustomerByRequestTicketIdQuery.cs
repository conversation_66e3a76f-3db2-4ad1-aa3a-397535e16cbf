﻿using System;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using TinyCRM.RequestTicket;
using Webaby;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.Customer.Queries
{
    public class GetCustomerByRequestTicketIdQuery : QueryBase<CustomerData>
    {
        public GetCustomerByRequestTicketIdQuery(Guid requestTicketId)
        {
            RequestTicketId = requestTicketId;
        }

        public Guid RequestTicketId { get; set; }
    }

    internal class GetCustomerByRequestTicketIdQueryHandler : QueryHandlerBase<GetCustomerByRequestTicketIdQuery, CustomerData>
    {
        public GetCustomerByRequestTicketIdQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<CustomerData>> ExecuteAsync(GetCustomerByRequestTicketIdQuery query)
        {
            var customerEntities = from cus in EntitySet.Get<CustomerEntity>()
                                   join ticket in EntitySet.Get<RequestTicketEntity>() on cus.Id equals ticket.CustomerId
                                   where ticket.Id == query.RequestTicketId
                                   select cus;
            return QueryResult.Create(customerEntities, Mapper.Map<CustomerData>);
        }
    }
}

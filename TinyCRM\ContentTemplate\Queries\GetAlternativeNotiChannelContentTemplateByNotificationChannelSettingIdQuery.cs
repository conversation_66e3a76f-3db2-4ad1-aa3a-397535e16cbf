﻿using System;
using System.Data;
using System.Data.SqlClient;
using System.Threading.Tasks;
using AutoMapper;
using Webaby;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.ContentTemplate.Queries
{
    public class GetAlternativeNotiChannelContentTemplateByNotificationChannelSettingIdQuery : QueryBase<AlternativeNotiChannelContentTemplateItem>
    {
        public Guid NotificationChannelSettingId { get; set; }
        
    }

    internal class GetAlternativeNotiChannelContentTemplateByNotificationChannelSettingIdQueryHandler : QueryHandlerBase<GetAlternativeNotiChannelContentTemplateByNotificationChannelSettingIdQuery, AlternativeNotiChannelContentTemplateItem>
    {
        public GetAlternativeNotiChannelContentTemplateByNotificationChannelSettingIdQueryHandler(IServiceProvider serviceProvider)
            : base(serviceProvider) { }

        public override async Task<QueryResult<AlternativeNotiChannelContentTemplateItem>> ExecuteAsync(GetAlternativeNotiChannelContentTemplateByNotificationChannelSettingIdQuery query)
        {
            var cmd = EntitySet.CreateDbCommand();
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@NotificationChannelSettingId", query.NotificationChannelSettingId));
            
            cmd.CommandText = "GetAlternativeNotiChannelContentTemplateByNotificationChannelSettingId";
            cmd.CommandType = CommandType.StoredProcedure;
            var mainQuery = await EntitySet.ExecuteReadCommandAsync<AlternativeNotiChannelContentTemplateItem>(cmd);
            return new QueryResult<AlternativeNotiChannelContentTemplateItem>(mainQuery);
        }
    }
}

﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using TinyCRM.Campaign;
using TinyCRM.Outbound.CallResult;
using Webaby;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;

namespace TinyCRM.DigitalContact.Queries
{
    public class GetDigitalResultInCampaignQuery : QueryBase<DataSet>
    {
        public Guid CampaignId { get; set; }

        public List<Guid> CallResultId { get; set; }

        public string ReportMode { get; set; }
    }

    internal class GetDigitalResultInCampaignQueryHandler : QueryHandlerBase<GetDigitalResultInCampaignQuery, DataSet>
    {
        public GetDigitalResultInCampaignQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<DataSet>> ExecuteAsync(GetDigitalResultInCampaignQuery query)
        {
            var listPivotColumn = new List<string>();
            var callResultList = await EntitySet.GetAsync<CallResultEntity>();
            foreach (var id in query.CallResultId)
            {
                var entity = callResultList.FirstOrDefault(x => x.Id == id);
                var addString = string.Format("[{0}] ", entity?.VietnameseDescription);
                listPivotColumn.Add(addString);
            }
            var queryStringPivot = string.Join(",", listPivotColumn);
            var sqlCommand = EntitySet.CreateDbCommand();
            sqlCommand.CommandType = CommandType.Text;
            string renderQuery = "";
            if (query.ReportMode == "digitalmessage")
            {
                renderQuery = $@"WITH CTE AS (SELECT pdc.PriorityIndex, dc.ChannelCode, dc.Id AS ChannelId, cr.VietnameseDescription, COUNT(*) OVER (PARTITION BY dc.Id) AS Total
                                              FROM dbo.ProspectDigitalContact pdc
                                                JOIN dbo.DigitalChannel dc ON dc.Id=pdc.DigitalChannelId
                                                JOIN dbo.CallResult cr ON cr.Id=pdc.CallResultId
                                                JOIN dbo.Prospect p ON p.Id=pdc.ProspectId
                                              WHERE p.CampaignId=@CampaignId AND pdc.ReferenceResultId IS NOT NULL)
                                 SELECT PriorityIndex, ChannelCode, Total, {queryStringPivot}
                                 FROM CTE
                                    PIVOT(COUNT(VietnameseDescription)
                                        FOR VietnameseDescription IN({queryStringPivot})) AS PivotTable
                                 ORDER BY PriorityIndex";
            }
            else
            {
                renderQuery = $@"SELECT {queryStringPivot}
                                       FROM( 
                                           SELECT rs.VietnameseDescription FROM dbo.Prospect pros
                                           JOIN  dbo.CallResult rs ON rs.Id = pros.CallResultId
                                           WHERE pros.CampaignId = @CampaignId
                                       ) AS BangNguon
                                       PIVOT (
                                         COUNT(VietnameseDescription)
                                         FOR VietnameseDescription IN ({queryStringPivot})
                                       ) AS BangChuyen
                                       GROUP BY {queryStringPivot} ";
            }
            sqlCommand.CommandText = renderQuery;
            sqlCommand.Parameters.Add(DbParameterHelper.AddNullableGuid(sqlCommand, "@CampaignId", query.CampaignId));
            var mainQuery = await EntitySet.ExecuteReadCommandAsync<DataSet>(sqlCommand);
            return QueryResult.Create(mainQuery);
        }
    }
}
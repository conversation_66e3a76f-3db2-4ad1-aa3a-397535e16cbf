﻿using System;
using AutoMapper;
using Webaby;
using Webaby.Data;
using Webaby.Localization;
using System.Threading.Tasks;

namespace TinyCRM.SMSLog.Commands
{
    public class CreateEditSMSLogCommand : CommandBase
    {
        public Guid Id { get; set; }

        public string PhoneNumber { get; set; }

        public string SMSContent { get; set; }

        public Guid? SentBy { get; set; }

        public DateTime SentDate { get; set; }

        public string ReturnedCode { get; set; }

        public string ReturnedMessage { get; set; }

        public Guid? ReferenceObjectId { get; set; }

        public Guid? SentGatewayId { get; set; }
    }

    internal class CreateEditSMSLogCommandHandler : CommandHandlerBase<CreateEditSMSLogCommand>
    {
        public CreateEditSMSLogCommandHandler(
            IText text,
            IMapper mapper,
            IRepository repository,
            IEntitySet entitySet,
            IQueryExecutor queryExecutor,
            ICommandExecutor commandExecutor,
            ILocalTransactionManager transactionManager,
            IEventBus eventBus
        ) : base(text, mapper, repository, entitySet, queryExecutor, commandExecutor, transactionManager, eventBus) { }

        public override async Task ExecuteAsync(CreateEditSMSLogCommand command)
        {
            var entity = await EntitySet.GetAsync<SMSLogEntity>(command.Id) ?? new SMSLogEntity();
            Mapper.Map(command, entity);
            await Repository.SaveAsync(entity);
        }
    }
}

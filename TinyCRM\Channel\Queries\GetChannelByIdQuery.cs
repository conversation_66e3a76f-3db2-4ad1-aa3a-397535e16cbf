﻿using AutoMapper;
using Webaby;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.Channel.Queries
{
    public class GetChannelByIdQuery : QueryBase<ChannelData>
    {
        public Guid Id { get; set; }
    }

    internal class GetChannelByIdQueryHandler : QueryHandlerBase<GetChannelByIdQuery, ChannelData>
    {
        public GetChannelByIdQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<ChannelData>> ExecuteAsync(GetChannelByIdQuery query)
        {
            var channelEntity = await EntitySet.GetAsync<ChannelEntity>(query.Id);
            return new QueryResult<ChannelData>(Mapper.Map<ChannelData>(channelEntity));
        }
    }
}
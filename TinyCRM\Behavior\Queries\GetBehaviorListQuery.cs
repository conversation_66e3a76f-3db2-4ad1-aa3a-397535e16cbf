﻿using Webaby.Data;
using AutoMapper;
using Webaby.Localization;
using Webaby;
using System.Threading.Tasks;
using System.Linq;

namespace TinyCRM.Behavior.Queries
{
    public class GetBehaviorListQuery : QueryBase<BehaviorData>
    {
        public bool IsDisabled { get; set; }
    }

    internal class GetBehaviorListQueryHandler : QueryHandlerBase<GetBehaviorListQuery, BehaviorData>
    {
        public GetBehaviorListQueryHandler(IServiceProvider serviceProvider)
            : base(serviceProvider) { }

        public override async Task<QueryResult<BehaviorData>> ExecuteAsync(GetBehaviorListQuery query)
        {
            var entity = (await EntitySet.GetAsync<BehaviorEntity>()).Where(x => x.IsDisabled == query.IsDisabled).OrderBy(x => x.Code);
            return QueryResult.Create(entity, Mapper.Map<BehaviorData>);
        }
    }
}

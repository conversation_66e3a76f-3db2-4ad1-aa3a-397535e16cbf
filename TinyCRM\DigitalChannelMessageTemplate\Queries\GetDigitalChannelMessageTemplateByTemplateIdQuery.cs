﻿using System;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using TinyCRM.DigitalChannel;
using Webaby;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.DigitalChannelMessageTemplate.Queries
{
    public class GetDigitalChannelMessageTemplateByTemplateIdQuery : QueryBase<DigitalChannelMessageTemplateData>
    {
        public Guid? Id { get; set; }

        public Guid? DigitalMessageTemplateId { get; set; }        
    }

    internal class GetDigitalChannelMessageTemplateByTemplateIdQueryHandler : QueryHandlerBase<GetDigitalChannelMessageTemplateByTemplateIdQuery, DigitalChannelMessageTemplateData>
    {
        public GetDigitalChannelMessageTemplateByTemplateIdQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<DigitalChannelMessageTemplateData>> ExecuteAsync(GetDigitalChannelMessageTemplateByTemplateIdQuery query)
        {
            var dcmList = await EntitySet.GetAsync<DigitalChannelMessageTemplateEntity>();
            var dctList = await EntitySet.GetAsync<DigitalContactTypeEntity>();
            var mainQuery = (from dcm in dcmList
                             join dct in dctList on dcm.DigitalContactTypeId equals dct.Id into _tempDct
                             from dct in _tempDct.DefaultIfEmpty()
                             where dcm.DigitalMessageTemplateId == query.DigitalMessageTemplateId
                             select new DigitalChannelMessageTemplateData
                             {
                                 Id = dcm.Id,
                                 DigitalMessageTemplateId = dcm.DigitalMessageTemplateId,
                                 DigitalChannelId = dcm.DigitalChannelId,
                                 ChannelSpecificJsonData = dcm.ChannelSpecificJsonData,
                                 Content = dcm.Content,
                                 Header = dcm.Header,
                                 Language = dcm.Language,
                                 Link = dcm.Link,
                                 DigitalContactTypeId = dcm.DigitalContactTypeId,
                                 DigitalContactType = dct != null ? dct.Name : null
                             });
            if (query.Id.HasValue)
            {
                mainQuery = mainQuery.Where(dcmt => dcmt.DigitalChannelId == query.Id.Value);
            }
            return QueryResult.Create(mainQuery);
        }
    }
}
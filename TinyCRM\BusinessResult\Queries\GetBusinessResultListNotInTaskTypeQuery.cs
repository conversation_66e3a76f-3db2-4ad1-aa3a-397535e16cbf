﻿using System;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using Webaby;
using Webaby.Data;
using Webaby.Localization;
using TinyCRM.TaskType;

namespace TinyCRM.BusinessResult.Queries
{
    public class GetBusinessResultListNotInTaskTypeQuery : QueryBase<BusinessResultData>
    {
        public Guid TaskTypeId { get; set; }
    }

    public class GetBusinessResultListNotInTaskTypeQueryHandler : QueryHandlerBase<GetBusinessResultListNotInTaskTypeQuery, BusinessResultData>
    {
        public GetBusinessResultListNotInTaskTypeQueryHandler(IServiceProvider serviceProvider)
            : base(serviceProvider) { }

        public override async Task<QueryResult<BusinessResultData>> ExecuteAsync(GetBusinessResultListNotInTaskTypeQuery query)
        {
            var tempBusinessResultQuery = (await EntitySet.GetAsync<BusinessResultReferenceEntity>()).Where(ttbr => ttbr.ReferenceObjectId == query.TaskTypeId);
            var entityList = (from br in await EntitySet.GetAsync<BusinessResultEntity>()
                              join _ttbr in tempBusinessResultQuery on br.Id equals _ttbr.BusinessResultId into tempTtbr
                              from ttbr in tempTtbr.DefaultIfEmpty()
                              where ttbr == null && br.ParentId == null
                              orderby br.DisplayOrder
                              select br);
            return QueryResult.Create(entityList, Mapper.Map<BusinessResultData>);
        }
    }
}
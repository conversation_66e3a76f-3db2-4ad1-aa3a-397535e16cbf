﻿using System;
using AutoMapper;
using Webaby;
using Webaby.Data;
using Webaby.Localization;
using System.Threading.Tasks;

namespace TinyCRM.Sms.Commands
{
    public class CreateEditGatewayCommand : CommandBase
    {
        public Guid Id { get; set; }

        public string Name { get; set; }

        public string Endpoint { get; set; }

        public string PhoneNumberPrefix { get; set; }

        public bool IsDefault { get; set; }

        public Guid GatewayIntergrationId { get; set; }

        public bool IsNew { get; set; }
    }

    internal class CreateEditGatewayCommandHandler : CommandHandlerBase<CreateEditGatewayCommand>
    {
        public CreateEditGatewayCommandHandler(
            IText text,
            IMapper mapper,
            IRepository repository,
            IEntitySet entitySet,
            IQueryExecutor queryExecutor,
            ICommandExecutor commandExecutor,
            ILocalTransactionManager transactionManager,
            IEventBus eventBus
        ) : base(text, mapper, repository, entitySet, queryExecutor, commandExecutor, transactionManager, eventBus) { }

        public override async Task ExecuteAsync(CreateEditGatewayCommand command)
        {
            var entity = command.IsNew ? new GatewayEntity() : await EntitySet.GetAsync<GatewayEntity>(command.Id) ?? new GatewayEntity();
            Mapper.Map(command, entity);
            await Repository.SaveAsync(entity);
        }
    }
}

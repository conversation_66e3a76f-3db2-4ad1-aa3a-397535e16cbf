﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using AutoMapper;
using TinyCRM.AutomaticTask.Command;
using TinyCRM.Enums;
using TinyCRM.Workflow;
using Webaby;
using Webaby.Data;
using Webaby.Localization;
using TaskStatus = TinyCRM.Enums.TaskStatus;

namespace TinyCRM.AutomaticTask.Queries
{
    public class GetOriginOrgIdQuery : QueryBase<Guid>
    {
        public Guid RequestTicketId { get; set; }

        public Guid? TaskId { get; set; }

        public string UserPathSelector { get; set; }

        public Dictionary<string, string> StaticParams { get; set; }
    }

    internal class GetOriginOrgIdQueryHandler : QueryHandlerBase<GetOriginOrgIdQuery, Guid>
    {
        public IQueryExecutor _queryExecutor { get; set; }
        public ICommandExecutor _commandExecutor { get; set; }

        public GetOriginOrgIdQueryHandler(IServiceProvider serviceProvider, IQueryExecutor queryExecutor,
            ICommandExecutor commandExecutor)
            : base(serviceProvider) { _queryExecutor = queryExecutor; _commandExecutor = commandExecutor; }

        public override async Task<QueryResult<Guid>> ExecuteAsync(GetOriginOrgIdQuery query)
        {
            Func<string, string, DataRow, string> Parser = (table, field, dr) =>
            {
                if (table == "dbo.Task" && field == "Status")
                {
                    TaskStatus? ts = (TaskStatus?)(dr[table + "." + field] as int?);
                    return ts.HasValue ? ts.Value.ToString() : "";
                }
                if (table == "dbo.Task" && field == "CompleteReason")
                {
                    TaskCompleteReason? ts = (TaskCompleteReason?)(dr[table + "." + field] as int?);
                    return ts.HasValue ? ts.Value.ToString() : "";
                }
                if (table == "dbo.RequestTicket" && field == "DifficultyDegree")
                {
                    Difficulty? ts = (Difficulty?)(dr[table + "." + field] as int?);
                    return ts.HasValue ? ts.Value.GetText() : "";
                }
                return dr[table + "." + field].ToString();
            };

            Guid referenceObjectId = query.RequestTicketId;
            string linkEntityParamsType = "RequestTicket";
            string storedProcedureHandle = "GetDataSetFromTicketKey";
            if (query.TaskId.IsNotNullOrEmpty())
            {
                referenceObjectId = query.TaskId.Value;
                linkEntityParamsType = "Task";
                storedProcedureHandle = "GetDataSetFromTaskKey";
            }

            Guid originOrgId = Guid.Empty;

            string originOrgPathSelector = string.Empty;
            await Task.Run(() =>
            {
                _commandExecutor.ExecuteAsync(new ParseAutoNextTaskUserPathSelectorCommand
                {
                    ReferenceObjectId = referenceObjectId,
                    LinkEntityParamsType = linkEntityParamsType,
                    StoredProcedureHandle = storedProcedureHandle,
                    UserPathSelector = query.UserPathSelector,

                    Parser = Parser,
                    Handle = (path, dr) =>
                    {
                        originOrgPathSelector = path;
                        foreach (string keyParam in query.StaticParams.Keys)
                        {
                            originOrgPathSelector = originOrgPathSelector.Replace(keyParam, query.StaticParams[keyParam]);
                        }

                        // Nếu Parse content trả về GUID => OriginOrgId
                        if (!Guid.TryParse(originOrgPathSelector, out originOrgId))
                        {
                            Guid originUserId = Guid.Empty;
                            if (Guid.TryParse(originOrgPathSelector.Split('.')[0], out originUserId))
                            {
                                var selectorPathString = originOrgPathSelector.Substring(originOrgPathSelector.IndexOf('.') + 1);
                                var originOrgIdsAsync =  _queryExecutor.ExecuteManyAsync(new GetIdsFromSelectorPathQuery { OriginId = originUserId, Path = selectorPathString });
                                var originOrgIds = originOrgIdsAsync.Result.ToList();
                                if (originOrgIds.Count() > 0)
                                {
                                    originOrgId = originOrgIds.FirstOrDefault();
                                }
                            }
                        }
                    }
                });
            });

            return new QueryResult<Guid>(originOrgId);
        }
    }
}
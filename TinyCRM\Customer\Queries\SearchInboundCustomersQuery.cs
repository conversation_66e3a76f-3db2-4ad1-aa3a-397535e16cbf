﻿using System.Data;
using System.Data.SqlClient;
using Webaby;
using Webaby.Data;

namespace TinyCRM.Customer.Queries
{
    public class SearchInboundCustomersQuery : QueryBase<CustomerData>
    {
        public string PhoneNumber
        {
            get; set;
        }

        public string Email
        {
            get; set;
        }

        public string FacebookId
        {
            get; set;
        }
    }

    internal class SearchInboundCustomersQueryHandler : QueryHandlerBase<SearchInboundCustomersQuery, CustomerData>
    {
        public SearchInboundCustomersQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }
        public override async Task<QueryResult<CustomerData>> ExecuteAsync(SearchInboundCustomersQuery query)
        {
            var cmd = EntitySet.CreateDbCommand();
            cmd.Parameters.AddRange(new[]
            {
                DbParameterHelper.AddNullableString(cmd, "@PhoneNumber", query.PhoneNumber.IsNullOrEmpty() ? string.Empty : query.PhoneNumber),
                DbParameterHelper.AddNullableString(cmd, "@Email", query.Email.IsNullOrEmpty() ? string.Empty : query.Email),
                DbParameterHelper.AddNullableString(cmd, "@FacebookId", query.FacebookId.IsNullOrEmpty() ? string.Empty : query.FacebookId),
            });

            cmd.CommandText = "SearchInboundCustomers";
            cmd.CommandType = CommandType.StoredProcedure;
            var mainQuery = await EntitySet.ExecuteReadCommandAsync<CustomerData>(cmd);
            return new QueryResult<CustomerData>(mainQuery);
        }
    }
}

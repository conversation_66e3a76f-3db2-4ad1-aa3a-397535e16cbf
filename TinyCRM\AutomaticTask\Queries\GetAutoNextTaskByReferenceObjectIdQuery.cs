﻿using Webaby.Data;
using AutoMapper;
using Webaby.Localization;
using Webaby;
using System.Threading.Tasks;
using System;
using System.Linq;
using TinyCRM.BusinessResult;
using TinyCRM.TaskType;
using Webaby.Core.DynamicForm;

namespace TinyCRM.AutomaticTask.Queries
{
    public class GetAutoNextTaskByReferenceObjectIdQuery : QueryBase<AutoNextTaskData>
    {
        public Guid ReferenceObjectId { get; set; }
        public Guid? AutoNextTaskId { get; set; }
    }

    internal class GetAutoNextTaskByWorkflowQueryHandler : QueryHandlerBase<GetAutoNextTaskByReferenceObjectIdQuery, AutoNextTaskData>
    {
        public GetAutoNextTaskByWorkflowQueryHandler(IServiceProvider serviceProvider)
            : base(serviceProvider) { }

        public override async Task<QueryResult<AutoNextTaskData>> ExecuteAsync(GetAutoNextTaskByReferenceObjectIdQuery query)
        {
            var mainQuery = from autoNextTask in EntitySet.Get<AutoNextTaskEntity>()
                            join _tbr in EntitySet.Get<BusinessResultEntity>() on autoNextTask.TaskBusinessResultId equals _tbr.Id into _tempTbr
                            from tbr in _tempTbr.DefaultIfEmpty()
                            join _userPathSelector in EntitySet.Get<UserPathSelectorEntity>() on autoNextTask.AssignedUserPathSelectorId equals _userPathSelector.Id into _tempUserPathSelector
                            from userPathSelector in _tempUserPathSelector.DefaultIfEmpty()
                            join _autoCondition in EntitySet.Get<AutoConditionEntity>() on autoNextTask.AutoConditionId equals _autoCondition.Id into _tempAutoCondition
                            from autoCondition in _tempAutoCondition.DefaultIfEmpty()
                            join _nextTaskType in EntitySet.Get<TaskTypeEntity>() on autoNextTask.NextTaskId equals _nextTaskType.Id into _tempNextTaskType
                            from nextTaskType in _tempNextTaskType.DefaultIfEmpty()
                            join _dfd in EntitySet.Get<DynamicFieldDefinitionEntity>() on autoNextTask.DynamicFieldConditionId equals _dfd.Id into _tempDfd
                            from dfd in _tempDfd.DefaultIfEmpty()
                            join _ticketBr in EntitySet.Get<BusinessResultEntity>() on autoNextTask.RequestTicketClosedBusinessResultId equals _ticketBr.Id into _tempTicketBr
                            from ticketBr in _tempTicketBr.DefaultIfEmpty()
                            where (!query.AutoNextTaskId.HasValue && autoNextTask.ReferenceObjectId == query.ReferenceObjectId)
                                  || (query.AutoNextTaskId.HasValue && autoNextTask.Id == query.AutoNextTaskId)
                            orderby autoNextTask.EventOrder
                            select new AutoNextTaskData
                            {
                                Id = autoNextTask.Id,
                                AutoAction = autoNextTask.AutoAction,
                                TaskBusinessResultId = autoNextTask.TaskBusinessResultId,
                                TaskBusinessResult = tbr.Name,
                                ReferenceObjectId = autoNextTask.ReferenceObjectId,
                                ReferenceType = autoNextTask.ReferenceType,
                                EventOrder = autoNextTask.EventOrder,
                                EventCondition = autoNextTask.EventCondition,
                                TriggeredEvent = autoNextTask.TriggeredEvent,
                                NextTaskFormula = autoNextTask.NextTaskFormula,
                                MultiTaskTriggered = autoNextTask.MultiTaskTriggered,
                                AssignedUserPathSelectorId = autoNextTask.AssignedUserPathSelectorId,
                                AssignedUserPathSelector = userPathSelector.Path,
                                AssignedUserPathSelectorName = userPathSelector.Name,
                                AutoConditionId = autoNextTask.AutoConditionId,
                                AutoCondition = autoCondition.Condition,
                                AutoConditionName = autoCondition.Name,
                                DynamicFieldConditionId = autoNextTask.DynamicFieldConditionId,
                                DynamicFieldCondition = dfd.DisplayName,
                                DynamicFieldConditionValue = autoNextTask.DynamicFieldConditionValue,
                                NextTaskId = autoNextTask.NextTaskId,
                                NextTaskTypeName = nextTaskType.TaskType,
                                RequestTicketClosedBusinessResultId = autoNextTask.RequestTicketClosedBusinessResultId,
                                RequestTicketClosedBusinessResult = ticketBr.Name,
                                FreeConditionalStatement = autoNextTask.FreeConditionalStatement,
                                WorkflowTaskTypeClosedBusinessResultId = autoNextTask.WorkflowTaskTypeClosedBusinessResultId,
                                TaskConditionName = autoNextTask.TaskConditionName,
                                ObjectApproveType = autoNextTask.ObjectApproveType,
                                ObjectApproveAction = autoNextTask.ObjectApproveAction,
                                TaskAssignmentRouting = autoNextTask.TaskAssignmentRouting
                            };
            var result = mainQuery.ToList();
            return new QueryResult<AutoNextTaskData>(result);
        }
    }
}

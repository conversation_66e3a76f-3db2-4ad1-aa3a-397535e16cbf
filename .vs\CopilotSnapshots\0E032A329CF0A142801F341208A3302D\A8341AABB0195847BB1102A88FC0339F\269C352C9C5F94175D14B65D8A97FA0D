﻿using System;
using Webaby;

namespace TinyCRM.Outbound.Brand.Queries
{
    public class GetBrandByIdQuery : QueryBase<BrandData>
    {
        public Guid Id
        {
            get;
            set;
        }
    }

    internal class GetBrandByIdQueryHandler : QueryHandlerBase<GetBrandByIdQuery, BrandData>
    {
        public override QueryResult<BrandData> Execute(GetBrandByIdQuery query)
        {
            var brand = EntitySet.Get<BrandEntity>(query.Id);

            if (brand == null) throw new InvalidOperationException(T["Không tìm  thấy Sản phẩm có id '{0}'", query.Id]);
            return new QueryResult<BrandData>(BrandData.FromEntity(brand));
        }
    }
}
﻿using System;
using System.Linq;
using System.Collections.Generic;
using Webaby;
using System.Data.SqlClient;
using System.Data;
using Webaby.Data;

namespace TinyCRM.Customer.Queries
{
    public class GetCustomerAppartmentInfoMultiQuery : QueryBase<CustomerAppartmentInfo>
    {
        public IEnumerable<Guid> PartCustomerIds { get; set; }

        public IEnumerable<CustomerAppartmentInfoFilter> Filters { get; set; }
    }

    internal class GetCustomerAppartmentInfoMultiQueryHandler : QueryHandlerBase<GetCustomerAppartmentInfoMultiQuery, CustomerAppartmentInfo>
    {
        public GetCustomerAppartmentInfoMultiQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<CustomerAppartmentInfo>> ExecuteAsync(GetCustomerAppartmentInfoMultiQuery query)
        {
            var singleFilters = new List<CustomerAppartmentSingleFilter>();
            var multiFilters = new List<CustomerAppartmentMultiFilter>();
            if(query.Filters != null && query.Filters.Any())
            {
                foreach(var filter in query.Filters)
                {
                    var id = Guid.NewGuid();
                    singleFilters.Add(new CustomerAppartmentSingleFilter
                    {
                        Id = id,
                        Name = filter.Name,
                        CMND = filter.CMND,
                        PhoneNumber = filter.PhoneNumber
                    });

                    foreach(var partId in filter.PartIds)
                    {
                        multiFilters.Add(new CustomerAppartmentMultiFilter
                        {
                            Id = id,
                            PartId = partId
                        });
                    }
                }
            }
            if (query.PartCustomerIds == null) query.PartCustomerIds = new List<Guid>();
            var partCustomerIds = query.PartCustomerIds.ToList();
            var cmd = EntitySet.CreateDbCommand();
            cmd.CommandText = "dbo.GetCustomerAppartmentInfoMulti";
            cmd.CommandType = CommandType.StoredProcedure;
            cmd.Parameters.AddRange(new[]
            {
                DbParameterHelper.NewStructuredParameter("@SingleFilters", "dbo.CustomerAppartmentSingleFilter", singleFilters),
                DbParameterHelper.NewStructuredParameter("@MultiFilters", "dbo.CustomerAppartmentMultiFilter", multiFilters),
                DbParameterHelper.NewIdListParameter("@PartCustomerIds", partCustomerIds),
                DbParameterHelper.AddNullableInt(cmd, "@StartRow", query.Pagination.StartRow),
                DbParameterHelper.AddNullableInt(cmd, "@EndRow", query.Pagination.EndRow)
            });

            var mainQuery = await EntitySet.ExecuteReadCommandAsync<CustomerAppartmentInfo>(cmd);
            return new QueryResult<CustomerAppartmentInfo>(mainQuery);
        }
    }

    public class CustomerAppartmentInfoFilter
    {
        public string Name { get; set; }

        public string PhoneNumber { get; set; }

        public string CMND { get; set; }

        public IEnumerable<Guid> PartIds { get; set; }
    }

    public class CustomerAppartmentSingleFilter
    {
        public Guid Id { get; set; }

        public string Name { get; set; }

        public string PhoneNumber { get; set; }

        public string CMND { get; set; }
    }

    public class CustomerAppartmentMultiFilter
    {
        public Guid Id { get; set; }

        public Guid PartId { get; set; }
    }
}

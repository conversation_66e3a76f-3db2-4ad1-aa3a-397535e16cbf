﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Webaby.Data;
using Webaby.Localization;
using Webaby;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace TinyCRM.Outbound.Brand.Queries
{
    public class GetParentBrandListByCompanyQuery : QueryBase<BrandData>
    {
        public Guid CompanyId { get; set; }
    }

    internal class GetParentBrandListByCompanyQueryHandler : QueryHandlerBase<GetParentBrandListByCompanyQuery, BrandData>
    {
        public GetParentBrandListByCompanyQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<BrandData>> ExecuteAsync(GetParentBrandListByCompanyQuery query)
        {
            var brandQuery = EntitySet.Get<BrandEntity>();
            brandQuery = brandQuery.Where(b => b.ParentBrandId.HasValue == false && b.CompanyId == query.CompanyId);
            var entities = await brandQuery.ToListAsync();
            var mapped = entities.Select(x => Mapper.Map<BrandData>(x));
            return QueryResult.Create(mapped);
        }
    }
}
﻿using Microsoft.Data.SqlClient;
using Webaby.Data;
using Webaby;
using AutoMapper;
using Webaby.Localization;
using System.Data;

namespace TinyCRM.Channel.Queries
{
    public class SearchChannelQuery : QueryBase<ChannelData>
    {
        public string Name { get; set; }

        public int? Code { get; set; }

        public bool IsDisabled { get; set; }

        public bool? IsSystem { get; set; }
    }

    internal class SearchChannelQueryHandler : QueryHandlerBase<SearchChannelQuery, ChannelData>
    {
        public SearchChannelQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<ChannelData>> ExecuteAsync(SearchChannelQuery query)
        {
            int startRow = query.Pagination.Index * query.Pagination.Size + 1;
            int endRow = query.Pagination.Index * query.Pagination.Size + query.Pagination.Size;

            var cmd = EntitySet.CreateDbCommand();
            cmd.Parameters.AddRange(new[]
            {
                DbParameterHelper.AddNullableString(cmd, "@Name", query.Name),
                DbParameterHelper.AddNullableInt(cmd, "@Code", query.Code),
                DbParameterHelper.NewNullableBooleanParameter(cmd, "@IsDisabled", query.IsDisabled),
                DbParameterHelper.NewNullableBooleanParameter(cmd, "@IsSystem", query.IsSystem),
                DbParameterHelper.AddNullableInt(cmd, "@StartRow", startRow),
                DbParameterHelper.AddNullableInt(cmd, "@EndRow", endRow),
            });

            cmd.CommandText = "dbo.SearchChannels";
            cmd.CommandType = CommandType.StoredProcedure;

            var mainQuery = await EntitySet.ExecuteReadCommandAsync<ChannelData>(cmd);
            return new QueryResult<ChannelData>(mainQuery);
        }
    }
}
﻿using AutoMapper;
using System;
using Webaby;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.AutomaticTask.Command
{
    public class DeleteAutoNextTaskErrorLogCommand : CommandBase
    {
        public Guid Id { get; set; }
    }

    internal class DeleteAutoNextTaskErrorLogCommandHandler : CommandHandlerBase<DeleteAutoNextTaskErrorLogCommand>
    {
        public DeleteAutoNextTaskErrorLogCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task ExecuteAsync(DeleteAutoNextTaskErrorLogCommand command)
        {
            await Repository.DeleteAsync<AutoNextTaskErrorLogEntity>(command.Id);
        }
    }
}

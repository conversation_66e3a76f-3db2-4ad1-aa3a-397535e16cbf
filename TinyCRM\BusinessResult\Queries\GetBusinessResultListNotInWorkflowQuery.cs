﻿using System;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using Webaby;
using Webaby.Data;
using Webaby.Localization;
using TinyCRM.TaskType;
using TinyCRM.Workflow;

namespace TinyCRM.BusinessResult.Queries
{
    public class GetBusinessResultListNotInWorkflowQuery : QueryBase<BusinessResultData>
    {
        public Guid WorkflowId { get; set; }
    }

    public class GetBusinessResultListNotInWorkflowQueryHandler : QueryHandlerBase<GetBusinessResultListNotInWorkflowQuery, BusinessResultData>
    {
        public GetBusinessResultListNotInWorkflowQueryHandler(IServiceProvider serviceProvider)
            : base(serviceProvider) { }

        public override async Task<QueryResult<BusinessResultData>> ExecuteAsync(GetBusinessResultListNotInWorkflowQuery query)
        {
            var tempBusinessResultQuery = (await EntitySet.GetAsync<BusinessResultReferenceEntity>()).Where(ttbr => ttbr.ReferenceObjectId == query.WorkflowId);
            var entityList = (from br in await EntitySet.GetAsync<BusinessResultEntity>()
                              join _ttbr in tempBusinessResultQuery on br.Id equals _ttbr.BusinessResultId into tempTtbr
                              from ttbr in tempTtbr.DefaultIfEmpty()
                              where ttbr == null
                              orderby br.DisplayOrder
                              select br);
            return QueryResult.Create(entityList, Mapper.Map<BusinessResultData>);
        }
    }
}
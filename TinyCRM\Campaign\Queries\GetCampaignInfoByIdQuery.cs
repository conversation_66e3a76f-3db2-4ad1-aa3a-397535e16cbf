﻿using Webaby.Data;
using AutoMapper;
using Webaby.Localization;
using Webaby;
using System;
using System.Data;
using System.Threading.Tasks;

namespace TinyCRM.Campaign.Queries
{
    public class GetCampaignInfoByIdQuery : QueryBase<CampaignInfo>
    {
        public Guid CampaignId { get; set; }
    }

    internal class GetCampaignInfoByIdQueryHandler : QueryHandlerBase<GetCampaignInfoByIdQuery, CampaignInfo>
    {
        public GetCampaignInfoByIdQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<CampaignInfo>> ExecuteAsync(GetCampaignInfoByIdQuery query)
        {
            var cmd = EntitySet.CreateDbCommand();
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@CampaignId", query.CampaignId));            
            cmd.CommandText = "GetCampaignInfo";
            cmd.CommandType = CommandType.StoredProcedure;
            var mainQuery = await EntitySet.ExecuteReadCommandAsync<CampaignInfo>(cmd);
            return new QueryResult<CampaignInfo>(mainQuery);
        }
    }
}
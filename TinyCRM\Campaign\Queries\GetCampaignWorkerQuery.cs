﻿using System;
using System.Data;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;

namespace TinyCRM.Campaign.Queries
{
    public class GetCampaignWorkerQuery : QueryBase<CampaignWorkerListItem>
    {
        public Guid CampaignId { get; set; }
        public Guid? OrganizationId { get; set; }
        public Boolean Deleted { get; set; }
    }

    internal class GetCampaignWorkerQueryHandler : QueryHandlerBase<GetCampaignWorkerQuery, CampaignWorkerListItem>
    {
        public GetCampaignWorkerQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<CampaignWorkerListItem>> ExecuteAsync(GetCampaignWorkerQuery query)
        {
            var cmd = EntitySet.CreateDbCommand();
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@campaignId", query.CampaignId));
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@organizationId", query.OrganizationId));
            cmd.Parameters.Add(DbParameterHelper.NewNullableBooleanParameter(cmd, "@deleted", query.Deleted));
            cmd.Parameters.Add(DbParameterHelper.AddNullableInt(cmd, "@startRow", query.Pagination.StartRow));
            cmd.Parameters.Add(DbParameterHelper.AddNullableInt(cmd, "@endRow", query.Pagination.EndRow));
            cmd.Parameters.Add(DbParameterHelper.AddNullableString(cmd, "@orderby", query.Sorting.Order(typeof(CampaignWorkerListItem))));
            cmd.Parameters.Add(DbParameterHelper.AddNullableString(cmd, "@sortorder", query.Sorting.SortOrder));
            cmd.CommandText = "GetCampaignWorker";
            cmd.CommandType = CommandType.StoredProcedure;
            var mainQuery = await EntitySet.ExecuteReadCommandAsync<CampaignWorkerListItem>(cmd);
            return new QueryResult<CampaignWorkerListItem>(mainQuery);
        }
    }
}

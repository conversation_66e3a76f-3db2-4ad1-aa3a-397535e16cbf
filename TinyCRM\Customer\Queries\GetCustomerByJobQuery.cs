﻿using Webaby.Data;
using AutoMapper;
using Webaby.Localization;
using Webaby;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace TinyCRM.Customer.Queries
{
    public class GetCustomerByJobQuery : QueryBase<CustomerData>
    {
        public string Job { get; set; }
    }

    internal class GetCustomerByJobQueryHandler : QueryHandlerBase<GetCustomerByJobQuery, CustomerData>
    {
        public GetCustomerByJobQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<CustomerData>> ExecuteAsync(GetCustomerByJobQuery query)
        {
            var entity = (await EntitySet.GetAsync<CustomerEntity>()).Where(x => x.Job == query.Job);
            return QueryResult.Create(entity, Mapper.Map<CustomerData>);
        }
    }
}
{"version": 3, "targets": {"net9.0": {"Autofac/8.2.1": {"type": "package", "dependencies": {"System.Diagnostics.DiagnosticSource": "8.0.1"}, "compile": {"lib/net8.0/Autofac.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Autofac.dll": {"related": ".xml"}}}, "Autofac.Extensions.DependencyInjection/10.0.0": {"type": "package", "dependencies": {"Autofac": "8.1.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.1"}, "compile": {"lib/net8.0/Autofac.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Autofac.Extensions.DependencyInjection.dll": {"related": ".xml"}}}, "AutoMapper/14.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Options": "8.0.0"}, "compile": {"lib/net8.0/AutoMapper.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/AutoMapper.dll": {"related": ".xml"}}}, "Azure.Core/1.38.0": {"type": "package", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "1.1.1", "System.ClientModel": "1.0.0", "System.Diagnostics.DiagnosticSource": "6.0.1", "System.Memory.Data": "1.0.2", "System.Numerics.Vectors": "4.5.0", "System.Text.Encodings.Web": "4.7.2", "System.Text.Json": "4.7.2", "System.Threading.Tasks.Extensions": "4.5.4"}, "compile": {"lib/net6.0/Azure.Core.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Azure.Core.dll": {"related": ".xml"}}}, "Azure.Identity/1.11.4": {"type": "package", "dependencies": {"Azure.Core": "1.38.0", "Microsoft.Identity.Client": "4.61.3", "Microsoft.Identity.Client.Extensions.Msal": "4.61.3", "System.Memory": "4.5.4", "System.Security.Cryptography.ProtectedData": "4.7.0", "System.Text.Json": "4.7.2", "System.Threading.Tasks.Extensions": "4.5.4"}, "compile": {"lib/netstandard2.0/Azure.Identity.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Azure.Identity.dll": {"related": ".xml"}}}, "Azure.Storage.Blobs/12.19.1": {"type": "package", "dependencies": {"Azure.Storage.Common": "12.18.1", "System.Text.Json": "4.7.2"}, "compile": {"lib/net6.0/Azure.Storage.Blobs.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Azure.Storage.Blobs.dll": {"related": ".xml"}}}, "Azure.Storage.Common/12.18.1": {"type": "package", "dependencies": {"Azure.Core": "1.36.0", "System.IO.Hashing": "6.0.0"}, "compile": {"lib/net6.0/Azure.Storage.Common.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Azure.Storage.Common.dll": {"related": ".xml"}}}, "CuttingEdge.Conditions.NetStandard/1.2.0": {"type": "package", "compile": {"lib/netstandard2.0/CuttingEdge.Conditions.dll": {}}, "runtime": {"lib/netstandard2.0/CuttingEdge.Conditions.dll": {}}}, "Dapper/2.1.66": {"type": "package", "compile": {"lib/net8.0/Dapper.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Dapper.dll": {"related": ".xml"}}}, "DistributedLock/2.6.0": {"type": "package", "dependencies": {"Azure.Storage.Blobs": "12.19.1", "DistributedLock.Azure": "1.0.2", "DistributedLock.FileSystem": "1.0.3", "DistributedLock.MySql": "1.0.2", "DistributedLock.Oracle": "1.0.4", "DistributedLock.Postgres": "1.3.0", "DistributedLock.Redis": "1.0.3", "DistributedLock.SqlServer": "1.0.6", "DistributedLock.WaitHandles": "1.0.1", "DistributedLock.ZooKeeper": "1.0.0", "Microsoft.Data.SqlClient": "5.2.2", "MySqlConnector": "2.3.5", "Npgsql": "8.0.6", "Oracle.ManagedDataAccess.Core": "23.6.1", "StackExchange.Redis": "2.7.27", "System.Threading.AccessControl": "8.0.0", "ZooKeeperNetEx": "3.4.12.4"}, "compile": {"lib/netstandard2.1/DistributedLock.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/DistributedLock.dll": {"related": ".xml"}}}, "DistributedLock.Azure/1.0.2": {"type": "package", "dependencies": {"Azure.Storage.Blobs": "12.19.1", "DistributedLock.Core": "[1.0.8, 1.1.0)"}, "compile": {"lib/netstandard2.1/DistributedLock.Azure.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/DistributedLock.Azure.dll": {"related": ".xml"}}}, "DistributedLock.Core/1.0.8": {"type": "package", "compile": {"lib/net8.0/DistributedLock.Core.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/DistributedLock.Core.dll": {"related": ".xml"}}}, "DistributedLock.FileSystem/1.0.3": {"type": "package", "dependencies": {"DistributedLock.Core": "[1.0.8, 1.1.0)"}, "compile": {"lib/netstandard2.1/DistributedLock.FileSystem.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/DistributedLock.FileSystem.dll": {"related": ".xml"}}}, "DistributedLock.MySql/1.0.2": {"type": "package", "dependencies": {"DistributedLock.Core": "[1.0.6, 1.1.0)", "MySqlConnector": "2.3.5"}, "compile": {"lib/netstandard2.1/DistributedLock.MySql.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/DistributedLock.MySql.dll": {"related": ".xml"}}}, "DistributedLock.Oracle/1.0.4": {"type": "package", "dependencies": {"DistributedLock.Core": "[1.0.8, 1.1.0)", "Oracle.ManagedDataAccess.Core": "23.6.1"}, "compile": {"lib/netstandard2.1/DistributedLock.Oracle.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/DistributedLock.Oracle.dll": {"related": ".xml"}}}, "DistributedLock.Postgres/1.3.0": {"type": "package", "dependencies": {"DistributedLock.Core": "[1.0.8, 1.1.0)", "Npgsql": "8.0.6"}, "compile": {"lib/net8.0/DistributedLock.Postgres.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/DistributedLock.Postgres.dll": {"related": ".xml"}}}, "DistributedLock.Redis/1.0.3": {"type": "package", "dependencies": {"DistributedLock.Core": "[1.0.6, 1.1.0)", "StackExchange.Redis": "2.7.27"}, "compile": {"lib/netstandard2.1/DistributedLock.Redis.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/DistributedLock.Redis.dll": {"related": ".xml"}}}, "DistributedLock.SqlServer/1.0.6": {"type": "package", "dependencies": {"DistributedLock.Core": "[1.0.8, 1.1.0)", "Microsoft.Data.SqlClient": "5.2.2"}, "compile": {"lib/netstandard2.1/DistributedLock.SqlServer.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/DistributedLock.SqlServer.dll": {"related": ".xml"}}}, "DistributedLock.WaitHandles/1.0.1": {"type": "package", "dependencies": {"DistributedLock.Core": "[1.0.5, 1.1.0)", "System.Threading.AccessControl": "5.0.0"}, "compile": {"lib/netstandard2.1/DistributedLock.WaitHandles.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/DistributedLock.WaitHandles.dll": {"related": ".xml"}}}, "DistributedLock.ZooKeeper/1.0.0": {"type": "package", "dependencies": {"DistributedLock.Core": "[1.0.2, 1.1.0)", "ZooKeeperNetEx": "3.4.12.4"}, "compile": {"lib/netstandard2.1/DistributedLock.ZooKeeper.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/DistributedLock.ZooKeeper.dll": {"related": ".xml"}}}, "DocumentFormat.OpenXml/3.3.0": {"type": "package", "dependencies": {"DocumentFormat.OpenXml.Framework": "3.3.0"}, "compile": {"lib/net8.0/DocumentFormat.OpenXml.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/DocumentFormat.OpenXml.dll": {"related": ".xml"}}}, "DocumentFormat.OpenXml.Framework/3.3.0": {"type": "package", "dependencies": {"System.IO.Packaging": "8.0.1"}, "compile": {"lib/net8.0/DocumentFormat.OpenXml.Framework.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/DocumentFormat.OpenXml.Framework.dll": {"related": ".xml"}}}, "Flee/2.0.0": {"type": "package", "dependencies": {"System.ComponentModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.7.0", "System.Reflection.Emit.ILGeneration": "4.7.0", "System.Reflection.Emit.Lightweight": "4.7.0"}, "compile": {"lib/net6.0/Flee.dll": {}}, "runtime": {"lib/net6.0/Flee.dll": {}}, "contentFiles": {"contentFiles/any/any/_._": {"buildAction": "None", "codeLanguage": "any", "copyToOutput": false}}}, "Microsoft.AspNetCore.Antiforgery/2.3.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.DataProtection": "2.3.0", "Microsoft.AspNetCore.Http.Abstractions": "2.3.0", "Microsoft.AspNetCore.Http.Extensions": "2.3.0", "Microsoft.AspNetCore.WebUtilities": "2.3.0", "Microsoft.Extensions.ObjectPool": "8.0.11"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Antiforgery.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Antiforgery.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Authentication/2.3.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Authentication.Core": "2.3.0", "Microsoft.AspNetCore.DataProtection": "2.3.0", "Microsoft.AspNetCore.Http": "2.3.0", "Microsoft.AspNetCore.Http.Extensions": "2.3.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.2", "Microsoft.Extensions.Options": "8.0.2", "Microsoft.Extensions.WebEncoders": "8.0.11"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Authentication.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Authentication.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Authentication.Abstractions/2.3.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.3.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.2", "Microsoft.Extensions.Options": "8.0.2"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Authentication.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Authentication.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Authentication.Cookies/2.3.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Authentication": "2.3.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Authentication.Cookies.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Authentication.Cookies.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Authentication.Core/2.3.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Authentication.Abstractions": "2.3.0", "Microsoft.AspNetCore.Http": "2.3.0", "Microsoft.AspNetCore.Http.Extensions": "2.3.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Authentication.Core.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Authentication.Core.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Authorization/2.3.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Logging.Abstractions": "8.0.2", "Microsoft.Extensions.Options": "8.0.2"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Authorization.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Authorization.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Authorization.Policy/2.3.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Authentication.Abstractions": "2.3.0", "Microsoft.AspNetCore.Authorization": "2.3.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Authorization.Policy.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Authorization.Policy.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Connections.Abstractions/2.3.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Http.Features": "2.3.0", "System.IO.Pipelines": "8.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Connections.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Connections.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Cors/2.3.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Http.Extensions": "2.3.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Logging.Abstractions": "8.0.2", "Microsoft.Extensions.Options": "8.0.2"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Cors.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Cors.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Cryptography.Internal/9.0.4": {"type": "package", "compile": {"lib/net9.0/Microsoft.AspNetCore.Cryptography.Internal.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Cryptography.Internal.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Cryptography.KeyDerivation/9.0.4": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Cryptography.Internal": "9.0.4"}, "compile": {"lib/net9.0/Microsoft.AspNetCore.Cryptography.KeyDerivation.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Cryptography.KeyDerivation.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.DataProtection/2.3.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Cryptography.Internal": "2.3.0", "Microsoft.AspNetCore.DataProtection.Abstractions": "2.3.0", "Microsoft.AspNetCore.Hosting.Abstractions": "2.3.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Logging.Abstractions": "8.0.2", "Microsoft.Extensions.Options": "8.0.2", "Microsoft.Win32.Registry": "4.5.0", "System.Security.Cryptography.Xml": "8.0.2", "System.Security.Principal.Windows": "5.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.DataProtection.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.DataProtection.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.DataProtection.Abstractions/2.3.0": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.DataProtection.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.DataProtection.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Diagnostics.Abstractions/2.3.0": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Diagnostics.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Diagnostics.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Hosting.Abstractions/2.3.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Hosting.Server.Abstractions": "2.3.0", "Microsoft.AspNetCore.Http.Abstractions": "2.3.0", "Microsoft.Extensions.Hosting.Abstractions": "8.0.1"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Hosting.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Hosting.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Hosting.Server.Abstractions/2.3.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Http.Features": "2.3.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Hosting.Server.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Hosting.Server.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Html.Abstractions/2.3.0": {"type": "package", "dependencies": {"System.Text.Encodings.Web": "8.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Html.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Html.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Http/2.3.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.3.0", "Microsoft.AspNetCore.WebUtilities": "2.3.0", "Microsoft.Extensions.ObjectPool": "8.0.11", "Microsoft.Extensions.Options": "8.0.2", "Microsoft.Net.Http.Headers": "2.3.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Http.Abstractions/2.3.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Http.Features": "2.3.0", "System.Text.Encodings.Web": "8.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Http.Connections/1.2.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Authorization.Policy": "2.3.0", "Microsoft.AspNetCore.Hosting.Abstractions": "2.3.0", "Microsoft.AspNetCore.Http": "2.3.0", "Microsoft.AspNetCore.Http.Connections.Common": "1.2.0", "Microsoft.AspNetCore.Routing": "2.3.0", "Microsoft.AspNetCore.WebSockets": "2.3.0", "Newtonsoft.Json": "11.0.2", "System.Net.WebSockets.WebSocketProtocol": "5.1.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Connections.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Connections.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Http.Connections.Common/1.2.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Connections.Abstractions": "2.3.0", "Newtonsoft.Json": "11.0.2", "System.Buffers": "4.6.0", "System.IO.Pipelines": "8.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Connections.Common.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Connections.Common.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Http.Extensions/2.3.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.3.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Net.Http.Headers": "2.3.0", "System.Buffers": "4.6.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Extensions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Extensions.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Http.Features/2.3.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Features.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Features.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Identity/2.3.1": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Authentication.Cookies": "2.3.0", "Microsoft.AspNetCore.Cryptography.KeyDerivation": "2.3.0", "Microsoft.AspNetCore.Hosting.Abstractions": "2.3.0", "Microsoft.Extensions.Identity.Core": "2.3.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Identity.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Identity.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Identity.EntityFrameworkCore/9.0.4": {"type": "package", "dependencies": {"Microsoft.EntityFrameworkCore.Relational": "9.0.4", "Microsoft.Extensions.Identity.Stores": "9.0.4"}, "compile": {"lib/net9.0/Microsoft.AspNetCore.Identity.EntityFrameworkCore.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Identity.EntityFrameworkCore.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.JsonPatch/2.3.0": {"type": "package", "dependencies": {"Microsoft.CSharp": "4.5.0", "Newtonsoft.Json": "11.0.2"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.JsonPatch.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.JsonPatch.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Localization/2.3.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Http.Extensions": "2.3.0", "Microsoft.Extensions.Localization.Abstractions": "8.0.11", "Microsoft.Extensions.Logging.Abstractions": "8.0.2", "Microsoft.Extensions.Options": "8.0.2"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Localization.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Localization.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Mvc/2.3.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Mvc.ApiExplorer": "2.3.0", "Microsoft.AspNetCore.Mvc.Cors": "2.3.0", "Microsoft.AspNetCore.Mvc.DataAnnotations": "2.3.0", "Microsoft.AspNetCore.Mvc.Formatters.Json": "2.3.0", "Microsoft.AspNetCore.Mvc.Localization": "2.3.0", "Microsoft.AspNetCore.Mvc.Razor.Extensions": "2.3.0", "Microsoft.AspNetCore.Mvc.RazorPages": "2.3.0", "Microsoft.AspNetCore.Mvc.TagHelpers": "2.3.0", "Microsoft.AspNetCore.Mvc.ViewFeatures": "2.3.0", "Microsoft.AspNetCore.Razor.Design": "2.3.0", "Microsoft.Extensions.Caching.Memory": "8.0.1", "Microsoft.Extensions.DependencyInjection": "8.0.1"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Mvc.Abstractions/2.3.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Routing.Abstractions": "2.3.0", "Microsoft.Net.Http.Headers": "2.3.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Mvc.ApiExplorer/2.3.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Mvc.Core": "2.3.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.ApiExplorer.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.ApiExplorer.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Mvc.Core/2.3.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Authentication.Core": "2.3.0", "Microsoft.AspNetCore.Authorization.Policy": "2.3.0", "Microsoft.AspNetCore.Hosting.Abstractions": "2.3.0", "Microsoft.AspNetCore.Http": "2.3.0", "Microsoft.AspNetCore.Http.Extensions": "2.3.0", "Microsoft.AspNetCore.Mvc.Abstractions": "2.3.0", "Microsoft.AspNetCore.ResponseCaching.Abstractions": "2.3.0", "Microsoft.AspNetCore.Routing": "2.3.0", "Microsoft.Extensions.DependencyInjection": "8.0.1", "Microsoft.Extensions.DependencyModel": "2.1.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.2", "System.Diagnostics.DiagnosticSource": "8.0.1", "System.Threading.Tasks.Extensions": "4.6.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Core.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Core.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Mvc.Cors/2.3.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Cors": "2.3.0", "Microsoft.AspNetCore.Mvc.Core": "2.3.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Cors.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Cors.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Mvc.DataAnnotations/2.3.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Mvc.Core": "2.3.0", "Microsoft.Extensions.Localization": "8.0.11", "System.ComponentModel.Annotations": "5.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.DataAnnotations.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.DataAnnotations.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Mvc.Formatters.Json/2.3.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.JsonPatch": "2.3.0", "Microsoft.AspNetCore.Mvc.Core": "2.3.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Formatters.Json.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Formatters.Json.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Mvc.Localization/2.3.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Localization": "2.3.0", "Microsoft.AspNetCore.Mvc.Razor": "2.3.0", "Microsoft.Extensions.DependencyInjection": "8.0.1", "Microsoft.Extensions.Localization": "8.0.11"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Localization.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Localization.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Mvc.Razor/2.3.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Mvc.Razor.Extensions": "2.3.0", "Microsoft.AspNetCore.Mvc.ViewFeatures": "2.3.0", "Microsoft.AspNetCore.Razor.Runtime": "2.3.0", "Microsoft.CodeAnalysis.CSharp": "2.8.2", "Microsoft.CodeAnalysis.Razor": "2.3.0", "Microsoft.Extensions.Caching.Memory": "8.0.1", "Microsoft.Extensions.FileProviders.Composite": "8.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Razor.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Razor.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Mvc.Razor.Extensions/6.0.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Razor.Language": "6.0.0", "Microsoft.CodeAnalysis.Razor": "6.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Razor.Extensions.dll": {}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Razor.Extensions.dll": {}}}, "Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation/9.0.4": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Mvc.Razor.Extensions": "6.0.0", "Microsoft.CodeAnalysis.Razor": "6.0.0", "Microsoft.Extensions.DependencyModel": "9.0.4"}, "compile": {"lib/net9.0/Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.AspNetCore.App"], "build": {"buildTransitive/net9.0/Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation.targets": {}}}, "Microsoft.AspNetCore.Mvc.RazorPages/2.3.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Mvc.Razor": "2.3.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.RazorPages.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.RazorPages.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Mvc.TagHelpers/2.3.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Mvc.Razor": "2.3.0", "Microsoft.AspNetCore.Razor.Runtime": "2.3.0", "Microsoft.AspNetCore.Routing.Abstractions": "2.3.0", "Microsoft.Extensions.Caching.Memory": "8.0.1", "Microsoft.Extensions.FileSystemGlobbing": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.TagHelpers.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.TagHelpers.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Mvc.ViewFeatures/2.3.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Antiforgery": "2.3.0", "Microsoft.AspNetCore.Diagnostics.Abstractions": "2.3.0", "Microsoft.AspNetCore.Html.Abstractions": "2.3.0", "Microsoft.AspNetCore.Mvc.Core": "2.3.0", "Microsoft.AspNetCore.Mvc.DataAnnotations": "2.3.0", "Microsoft.AspNetCore.Mvc.Formatters.Json": "2.3.0", "Microsoft.Extensions.WebEncoders": "8.0.11", "Newtonsoft.Json": "13.0.1", "Newtonsoft.Json.Bson": "1.0.2"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.ViewFeatures.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.ViewFeatures.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Razor/2.3.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Html.Abstractions": "2.3.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Razor.dll": {}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Razor.dll": {}}}, "Microsoft.AspNetCore.Razor.Design/2.3.0": {"type": "package", "build": {"build/netstandard2.0/_._": {}}, "buildMultiTargeting": {"buildMultiTargeting/_._": {}}}, "Microsoft.AspNetCore.Razor.Language/6.0.0": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Razor.Language.dll": {}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Razor.Language.dll": {}}}, "Microsoft.AspNetCore.Razor.Runtime/2.3.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Html.Abstractions": "2.3.0", "Microsoft.AspNetCore.Razor": "2.3.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Razor.Runtime.dll": {}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Razor.Runtime.dll": {}}}, "Microsoft.AspNetCore.ResponseCaching.Abstractions/2.3.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.ResponseCaching.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.ResponseCaching.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Routing/2.3.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Http.Extensions": "2.3.0", "Microsoft.AspNetCore.Routing.Abstractions": "2.3.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.2", "Microsoft.Extensions.ObjectPool": "8.0.11", "Microsoft.Extensions.Options": "8.0.2"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Routing.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Routing.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Routing.Abstractions/2.3.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.3.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Routing.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Routing.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.SignalR/1.2.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Http.Connections": "1.2.0", "Microsoft.AspNetCore.SignalR.Core": "1.2.0", "Microsoft.AspNetCore.WebSockets": "2.3.0", "System.IO.Pipelines": "8.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.SignalR.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.SignalR.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.SignalR.Common/1.2.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Connections.Abstractions": "2.3.0", "Microsoft.Extensions.Options": "8.0.2", "Newtonsoft.Json": "11.0.2", "System.Buffers": "4.6.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.SignalR.Common.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.SignalR.Common.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.SignalR.Core/1.2.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Authorization": "2.3.0", "Microsoft.AspNetCore.SignalR.Common": "1.2.0", "Microsoft.AspNetCore.SignalR.Protocols.Json": "1.2.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Logging.Abstractions": "8.0.2", "System.IO.Pipelines": "8.0.0", "System.Reflection.Emit": "4.7.0", "System.Threading.Channels": "8.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.SignalR.Core.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.SignalR.Core.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.SignalR.Protocols.Json/1.2.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.SignalR.Common": "1.2.0", "Newtonsoft.Json": "11.0.2", "System.IO.Pipelines": "8.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.SignalR.Protocols.Json.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.SignalR.Protocols.Json.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.WebSockets/2.3.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Http.Extensions": "2.3.0", "Microsoft.Extensions.Options": "8.0.2", "System.Net.WebSockets.WebSocketProtocol": "5.1.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.WebSockets.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.WebSockets.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.WebUtilities/2.3.0": {"type": "package", "dependencies": {"Microsoft.Net.Http.Headers": "2.3.0", "System.Text.Encodings.Web": "8.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.WebUtilities.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.WebUtilities.dll": {"related": ".xml"}}}, "Microsoft.Bcl.AsyncInterfaces/1.1.1": {"type": "package", "compile": {"ref/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {}}, "runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"related": ".xml"}}}, "Microsoft.CodeAnalysis.Analyzers/3.3.2": {"type": "package", "build": {"build/_._": {}}}, "Microsoft.CodeAnalysis.Common/4.0.0": {"type": "package", "dependencies": {"Microsoft.CodeAnalysis.Analyzers": "3.3.2", "System.Collections.Immutable": "5.0.0", "System.Memory": "4.5.4", "System.Reflection.Metadata": "5.0.0", "System.Runtime.CompilerServices.Unsafe": "5.0.0", "System.Text.Encoding.CodePages": "4.5.1", "System.Threading.Tasks.Extensions": "4.5.4"}, "compile": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.dll": {"related": ".pdb;.xml"}}, "resource": {"lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.CSharp/4.0.0": {"type": "package", "dependencies": {"Microsoft.CodeAnalysis.Common": "[4.0.0]"}, "compile": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.CSharp.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.CSharp.dll": {"related": ".pdb;.xml"}}, "resource": {"lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.Razor/6.0.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Razor.Language": "6.0.0", "Microsoft.CodeAnalysis.CSharp": "4.0.0", "Microsoft.CodeAnalysis.Common": "4.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.CodeAnalysis.Razor.dll": {}}, "runtime": {"lib/netstandard2.0/Microsoft.CodeAnalysis.Razor.dll": {}}}, "Microsoft.CSharp/4.5.0": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}, "Microsoft.Data.SqlClient/5.2.2": {"type": "package", "dependencies": {"Azure.Identity": "1.11.4", "Microsoft.Data.SqlClient.SNI.runtime": "5.2.0", "Microsoft.Identity.Client": "4.61.3", "Microsoft.IdentityModel.JsonWebTokens": "6.35.0", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "6.35.0", "Microsoft.SqlServer.Server": "1.0.0", "System.Configuration.ConfigurationManager": "8.0.0", "System.Runtime.Caching": "8.0.0"}, "compile": {"ref/net8.0/Microsoft.Data.SqlClient.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Data.SqlClient.dll": {"related": ".xml"}}, "resource": {"lib/net8.0/de/Microsoft.Data.SqlClient.resources.dll": {"locale": "de"}, "lib/net8.0/es/Microsoft.Data.SqlClient.resources.dll": {"locale": "es"}, "lib/net8.0/fr/Microsoft.Data.SqlClient.resources.dll": {"locale": "fr"}, "lib/net8.0/it/Microsoft.Data.SqlClient.resources.dll": {"locale": "it"}, "lib/net8.0/ja/Microsoft.Data.SqlClient.resources.dll": {"locale": "ja"}, "lib/net8.0/ko/Microsoft.Data.SqlClient.resources.dll": {"locale": "ko"}, "lib/net8.0/pt-BR/Microsoft.Data.SqlClient.resources.dll": {"locale": "pt-BR"}, "lib/net8.0/ru/Microsoft.Data.SqlClient.resources.dll": {"locale": "ru"}, "lib/net8.0/zh-Hans/Microsoft.Data.SqlClient.resources.dll": {"locale": "zh-Hans"}, "lib/net8.0/zh-Hant/Microsoft.Data.SqlClient.resources.dll": {"locale": "zh-Han<PERSON>"}}, "runtimeTargets": {"runtimes/unix/lib/net8.0/Microsoft.Data.SqlClient.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/net8.0/Microsoft.Data.SqlClient.dll": {"assetType": "runtime", "rid": "win"}}}, "Microsoft.Data.SqlClient.SNI.runtime/5.2.0": {"type": "package", "runtimeTargets": {"runtimes/win-arm/native/Microsoft.Data.SqlClient.SNI.dll": {"assetType": "native", "rid": "win-arm"}, "runtimes/win-arm64/native/Microsoft.Data.SqlClient.SNI.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-x64/native/Microsoft.Data.SqlClient.SNI.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x86/native/Microsoft.Data.SqlClient.SNI.dll": {"assetType": "native", "rid": "win-x86"}}}, "Microsoft.EntityFrameworkCore/9.0.5": {"type": "package", "dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "9.0.5", "Microsoft.EntityFrameworkCore.Analyzers": "9.0.5", "Microsoft.Extensions.Caching.Memory": "9.0.5", "Microsoft.Extensions.Logging": "9.0.5"}, "compile": {"lib/net8.0/Microsoft.EntityFrameworkCore.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/Microsoft.EntityFrameworkCore.props": {}}}, "Microsoft.EntityFrameworkCore.Abstractions/9.0.5": {"type": "package", "compile": {"lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.EntityFrameworkCore.Analyzers/9.0.5": {"type": "package"}, "Microsoft.EntityFrameworkCore.Relational/9.0.5": {"type": "package", "dependencies": {"Microsoft.EntityFrameworkCore": "9.0.5", "Microsoft.Extensions.Caching.Memory": "9.0.5", "Microsoft.Extensions.Configuration.Abstractions": "9.0.5", "Microsoft.Extensions.Logging": "9.0.5"}, "compile": {"lib/net8.0/Microsoft.EntityFrameworkCore.Relational.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Relational.dll": {"related": ".xml"}}}, "Microsoft.EntityFrameworkCore.SqlServer/9.0.5": {"type": "package", "dependencies": {"Microsoft.Data.SqlClient": "5.1.6", "Microsoft.EntityFrameworkCore.Relational": "9.0.5", "Microsoft.Extensions.Caching.Memory": "9.0.5", "Microsoft.Extensions.Configuration.Abstractions": "9.0.5", "Microsoft.Extensions.Logging": "9.0.5", "System.Formats.Asn1": "9.0.5", "System.Text.Json": "9.0.5"}, "compile": {"lib/net8.0/Microsoft.EntityFrameworkCore.SqlServer.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.SqlServer.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Caching.Abstractions/9.0.5": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "9.0.5"}, "compile": {"lib/net9.0/Microsoft.Extensions.Caching.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Caching.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Caching.Memory/9.0.5": {"type": "package", "dependencies": {"Microsoft.Extensions.Caching.Abstractions": "9.0.5", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5", "Microsoft.Extensions.Logging.Abstractions": "9.0.5", "Microsoft.Extensions.Options": "9.0.5", "Microsoft.Extensions.Primitives": "9.0.5"}, "compile": {"lib/net9.0/Microsoft.Extensions.Caching.Memory.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Caching.Memory.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Configuration/9.0.4": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.4", "Microsoft.Extensions.Primitives": "9.0.4"}, "compile": {"lib/net9.0/Microsoft.Extensions.Configuration.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Configuration.Abstractions/9.0.5": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "9.0.5"}, "compile": {"lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Configuration.Binder/9.0.4": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.4"}, "compile": {"lib/net9.0/Microsoft.Extensions.Configuration.Binder.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Binder.dll": {"related": ".xml"}}, "build": {"buildTransitive/netstandard2.0/Microsoft.Extensions.Configuration.Binder.targets": {}}}, "Microsoft.Extensions.DependencyInjection/9.0.5": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5"}, "compile": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.5": {"type": "package", "compile": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.DependencyModel/9.0.4": {"type": "package", "compile": {"lib/net9.0/Microsoft.Extensions.DependencyModel.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.DependencyModel.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Diagnostics.Abstractions/8.0.1": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Options": "8.0.2"}, "compile": {"lib/net8.0/Microsoft.Extensions.Diagnostics.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Diagnostics.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.FileProviders.Abstractions/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.FileProviders.Composite/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.FileProviders.Composite.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Composite.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.FileSystemGlobbing/8.0.0": {"type": "package", "compile": {"lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Hosting.Abstractions/8.0.1": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Diagnostics.Abstractions": "8.0.1", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.2"}, "compile": {"lib/net8.0/Microsoft.Extensions.Hosting.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Hosting.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Identity.Core/9.0.4": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Cryptography.KeyDerivation": "9.0.4", "Microsoft.Extensions.Logging": "9.0.4", "Microsoft.Extensions.Options": "9.0.4"}, "compile": {"lib/net9.0/Microsoft.Extensions.Identity.Core.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Identity.Core.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Identity.Stores/9.0.4": {"type": "package", "dependencies": {"Microsoft.Extensions.Caching.Abstractions": "9.0.4", "Microsoft.Extensions.Identity.Core": "9.0.4", "Microsoft.Extensions.Logging": "9.0.4"}, "compile": {"lib/net9.0/Microsoft.Extensions.Identity.Stores.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Identity.Stores.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Localization/8.0.11": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Localization.Abstractions": "8.0.11", "Microsoft.Extensions.Logging.Abstractions": "8.0.2", "Microsoft.Extensions.Options": "8.0.2"}, "compile": {"lib/net8.0/Microsoft.Extensions.Localization.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Localization.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Localization.Abstractions/8.0.11": {"type": "package", "compile": {"lib/net8.0/Microsoft.Extensions.Localization.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Localization.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Logging/9.0.5": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection": "9.0.5", "Microsoft.Extensions.Logging.Abstractions": "9.0.5", "Microsoft.Extensions.Options": "9.0.5"}, "compile": {"lib/net9.0/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Logging.Abstractions/9.0.5": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5"}, "compile": {"lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/Microsoft.Extensions.Logging.Abstractions.targets": {}}}, "Microsoft.Extensions.ObjectPool/8.0.11": {"type": "package", "compile": {"lib/net8.0/Microsoft.Extensions.ObjectPool.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.ObjectPool.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Options/9.0.5": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5", "Microsoft.Extensions.Primitives": "9.0.5"}, "compile": {"lib/net9.0/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/Microsoft.Extensions.Options.targets": {}}}, "Microsoft.Extensions.Primitives/9.0.5": {"type": "package", "compile": {"lib/net9.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.WebEncoders/8.0.11": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Options": "8.0.2"}, "compile": {"lib/net8.0/Microsoft.Extensions.WebEncoders.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.WebEncoders.dll": {"related": ".xml"}}}, "Microsoft.Identity.Client/4.61.3": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Abstractions": "6.35.0", "System.Diagnostics.DiagnosticSource": "6.0.1"}, "compile": {"lib/net6.0/Microsoft.Identity.Client.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Identity.Client.dll": {"related": ".xml"}}}, "Microsoft.Identity.Client.Extensions.Msal/4.61.3": {"type": "package", "dependencies": {"Microsoft.Identity.Client": "4.61.3", "System.Security.Cryptography.ProtectedData": "4.5.0"}, "compile": {"lib/net6.0/Microsoft.Identity.Client.Extensions.Msal.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Identity.Client.Extensions.Msal.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Abstractions/8.9.0": {"type": "package", "compile": {"lib/net9.0/Microsoft.IdentityModel.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.JsonWebTokens/6.35.0": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Tokens": "6.35.0", "System.Text.Encoding": "4.3.0", "System.Text.Encodings.Web": "4.7.2", "System.Text.Json": "4.7.2"}, "compile": {"lib/net6.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Logging/8.9.0": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Abstractions": "8.9.0"}, "compile": {"lib/net9.0/Microsoft.IdentityModel.Logging.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.Logging.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Protocols/8.9.0": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Tokens": "8.9.0"}, "compile": {"lib/net9.0/Microsoft.IdentityModel.Protocols.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.Protocols.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/6.35.0": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Protocols": "6.35.0", "System.IdentityModel.Tokens.Jwt": "6.35.0"}, "compile": {"lib/net6.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Tokens/8.9.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Logging.Abstractions": "8.0.2", "Microsoft.IdentityModel.Logging": "8.9.0"}, "compile": {"lib/net9.0/Microsoft.IdentityModel.Tokens.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.Tokens.dll": {"related": ".xml"}}}, "Microsoft.Net.Http.Headers/2.3.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "8.0.0", "System.Buffers": "4.6.0"}, "compile": {"lib/netstandard2.0/Microsoft.Net.Http.Headers.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Net.Http.Headers.dll": {"related": ".xml"}}}, "Microsoft.NETCore.Platforms/2.1.2": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "Microsoft.NETCore.Targets/1.1.0": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "Microsoft.SqlServer.Server/1.0.0": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.SqlServer.Server.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.SqlServer.Server.dll": {"related": ".pdb;.xml"}}}, "Microsoft.Win32.Registry/4.5.0": {"type": "package", "dependencies": {"System.Security.AccessControl": "4.5.0", "System.Security.Principal.Windows": "4.5.0"}, "compile": {"ref/netstandard2.0/Microsoft.Win32.Registry.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Win32.Registry.dll": {}}, "runtimeTargets": {"runtimes/unix/lib/netstandard2.0/Microsoft.Win32.Registry.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.dll": {"assetType": "runtime", "rid": "win"}}}, "MySqlConnector/2.3.5": {"type": "package", "dependencies": {"Microsoft.Extensions.Logging.Abstractions": "7.0.1"}, "compile": {"lib/net8.0/MySqlConnector.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/MySqlConnector.dll": {"related": ".xml"}}}, "Newtonsoft.Json/13.0.3": {"type": "package", "compile": {"lib/net6.0/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"related": ".xml"}}}, "Newtonsoft.Json.Bson/1.0.2": {"type": "package", "dependencies": {"Newtonsoft.Json": "12.0.1"}, "compile": {"lib/netstandard2.0/Newtonsoft.Json.Bson.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Newtonsoft.Json.Bson.dll": {"related": ".pdb;.xml"}}}, "Npgsql/9.0.3": {"type": "package", "dependencies": {"Microsoft.Extensions.Logging.Abstractions": "8.0.2"}, "compile": {"lib/net8.0/Npgsql.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Npgsql.dll": {"related": ".xml"}}}, "Npgsql.EntityFrameworkCore.PostgreSQL/9.0.4": {"type": "package", "dependencies": {"Microsoft.EntityFrameworkCore": "[9.0.1, 10.0.0)", "Microsoft.EntityFrameworkCore.Relational": "[9.0.1, 10.0.0)", "Npgsql": "9.0.3"}, "compile": {"lib/net8.0/Npgsql.EntityFrameworkCore.PostgreSQL.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Npgsql.EntityFrameworkCore.PostgreSQL.dll": {"related": ".xml"}}}, "Oracle.ManagedDataAccess.Core/23.6.1": {"type": "package", "dependencies": {"System.Diagnostics.PerformanceCounter": "8.0.0", "System.DirectoryServices.Protocols": "8.0.0", "System.Formats.Asn1": "8.0.1", "System.Security.Cryptography.Pkcs": "8.0.0"}, "compile": {"lib/net8.0/Oracle.ManagedDataAccess.dll": {}}, "runtime": {"lib/net8.0/Oracle.ManagedDataAccess.dll": {}}}, "Pipelines.Sockets.Unofficial/2.2.8": {"type": "package", "dependencies": {"System.IO.Pipelines": "5.0.1"}, "compile": {"lib/net5.0/Pipelines.Sockets.Unofficial.dll": {"related": ".xml"}}, "runtime": {"lib/net5.0/Pipelines.Sockets.Unofficial.dll": {"related": ".xml"}}}, "RestSharp/112.1.0": {"type": "package", "compile": {"lib/net8.0/RestSharp.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/RestSharp.dll": {"related": ".xml"}}}, "StackExchange.Redis/2.7.27": {"type": "package", "dependencies": {"Microsoft.Extensions.Logging.Abstractions": "6.0.0", "Pipelines.Sockets.Unofficial": "2.2.8"}, "compile": {"lib/net6.0/StackExchange.Redis.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/StackExchange.Redis.dll": {"related": ".xml"}}}, "System.Buffers/4.6.0": {"type": "package", "compile": {"lib/netcoreapp2.1/_._": {}}, "runtime": {"lib/netcoreapp2.1/_._": {}}}, "System.ClientModel/1.0.0": {"type": "package", "dependencies": {"System.Memory.Data": "1.0.2", "System.Text.Json": "4.7.2"}, "compile": {"lib/net6.0/System.ClientModel.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.ClientModel.dll": {"related": ".xml"}}}, "System.Collections.Immutable/5.0.0": {"type": "package", "compile": {"lib/netstandard2.0/System.Collections.Immutable.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Collections.Immutable.dll": {"related": ".xml"}}}, "System.ComponentModel/4.3.0": {"type": "package", "dependencies": {"System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.0/System.ComponentModel.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.ComponentModel.dll": {}}}, "System.ComponentModel.Annotations/5.0.0": {"type": "package", "compile": {"ref/netstandard2.1/System.ComponentModel.Annotations.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/System.ComponentModel.Annotations.dll": {"related": ".xml"}}}, "System.Configuration.ConfigurationManager/9.0.4": {"type": "package", "dependencies": {"System.Diagnostics.EventLog": "9.0.4", "System.Security.Cryptography.ProtectedData": "9.0.4"}, "compile": {"lib/net9.0/System.Configuration.ConfigurationManager.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/System.Configuration.ConfigurationManager.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "System.Diagnostics.DiagnosticSource/8.0.1": {"type": "package", "compile": {"lib/net8.0/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Diagnostics.EventLog/9.0.4": {"type": "package", "compile": {"lib/net9.0/System.Diagnostics.EventLog.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/System.Diagnostics.EventLog.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net9.0/System.Diagnostics.EventLog.Messages.dll": {"assetType": "runtime", "rid": "win"}, "runtimes/win/lib/net9.0/System.Diagnostics.EventLog.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Diagnostics.PerformanceCounter/8.0.0": {"type": "package", "dependencies": {"System.Configuration.ConfigurationManager": "8.0.0"}, "compile": {"lib/net8.0/System.Diagnostics.PerformanceCounter.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Diagnostics.PerformanceCounter.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Diagnostics.PerformanceCounter.dll": {"assetType": "runtime", "rid": "win"}}}, "System.DirectoryServices/9.0.4": {"type": "package", "compile": {"lib/net9.0/System.DirectoryServices.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/System.DirectoryServices.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net9.0/System.DirectoryServices.dll": {"assetType": "runtime", "rid": "win"}}}, "System.DirectoryServices.AccountManagement/9.0.4": {"type": "package", "dependencies": {"System.Configuration.ConfigurationManager": "9.0.4", "System.DirectoryServices": "9.0.4", "System.DirectoryServices.Protocols": "9.0.4"}, "compile": {"lib/net9.0/System.DirectoryServices.AccountManagement.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/System.DirectoryServices.AccountManagement.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net9.0/System.DirectoryServices.AccountManagement.dll": {"assetType": "runtime", "rid": "win"}}}, "System.DirectoryServices.Protocols/9.0.4": {"type": "package", "compile": {"lib/net9.0/System.DirectoryServices.Protocols.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/System.DirectoryServices.Protocols.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}, "runtimeTargets": {"runtimes/linux/lib/net9.0/System.DirectoryServices.Protocols.dll": {"assetType": "runtime", "rid": "linux"}, "runtimes/osx/lib/net9.0/System.DirectoryServices.Protocols.dll": {"assetType": "runtime", "rid": "osx"}, "runtimes/win/lib/net9.0/System.DirectoryServices.Protocols.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Formats.Asn1/9.0.5": {"type": "package", "compile": {"lib/net9.0/System.Formats.Asn1.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/System.Formats.Asn1.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "System.IdentityModel.Tokens.Jwt/6.35.0": {"type": "package", "dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "6.35.0", "Microsoft.IdentityModel.Tokens": "6.35.0"}, "compile": {"lib/net6.0/System.IdentityModel.Tokens.Jwt.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.IdentityModel.Tokens.Jwt.dll": {"related": ".xml"}}}, "System.IO/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}, "compile": {"ref/netstandard1.5/System.IO.dll": {"related": ".xml"}}}, "System.IO.Hashing/6.0.0": {"type": "package", "compile": {"lib/net6.0/System.IO.Hashing.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.IO.Hashing.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "System.IO.Packaging/8.0.1": {"type": "package", "compile": {"lib/net8.0/System.IO.Packaging.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.IO.Packaging.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.IO.Pipelines/8.0.0": {"type": "package", "compile": {"lib/net8.0/System.IO.Pipelines.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.IO.Pipelines.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Memory/4.5.4": {"type": "package", "compile": {"ref/netcoreapp2.1/_._": {}}, "runtime": {"lib/netcoreapp2.1/_._": {}}}, "System.Memory.Data/1.0.2": {"type": "package", "dependencies": {"System.Text.Encodings.Web": "4.7.2", "System.Text.Json": "4.6.0"}, "compile": {"lib/netstandard2.0/System.Memory.Data.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Memory.Data.dll": {"related": ".xml"}}}, "System.Net.WebSockets.WebSocketProtocol/5.1.0": {"type": "package", "compile": {"lib/net6.0/System.Net.WebSockets.WebSocketProtocol.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Net.WebSockets.WebSocketProtocol.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Numerics.Vectors/4.5.0": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}, "System.Reflection/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.5/System.Reflection.dll": {"related": ".xml"}}}, "System.Reflection.Emit/4.7.0": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}, "System.Reflection.Emit.ILGeneration/4.7.0": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}, "System.Reflection.Emit.Lightweight/4.7.0": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}, "System.Reflection.Metadata/5.0.0": {"type": "package", "compile": {"lib/netstandard2.0/System.Reflection.Metadata.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Reflection.Metadata.dll": {"related": ".xml"}}}, "System.Reflection.Primitives/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.0/System.Reflection.Primitives.dll": {"related": ".xml"}}}, "System.Runtime/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}, "compile": {"ref/netstandard1.5/System.Runtime.dll": {"related": ".xml"}}}, "System.Runtime.Caching/8.0.0": {"type": "package", "dependencies": {"System.Configuration.ConfigurationManager": "8.0.0"}, "compile": {"lib/net8.0/_._": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Runtime.Caching.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Runtime.Caching.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Runtime.CompilerServices.Unsafe/5.0.0": {"type": "package", "compile": {"ref/netstandard2.1/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp2.0/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}}, "System.Security.AccessControl/4.5.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "2.0.0", "System.Security.Principal.Windows": "4.5.0"}, "compile": {"ref/netstandard2.0/System.Security.AccessControl.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Security.AccessControl.dll": {}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp2.0/System.Security.AccessControl.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Cryptography.Pkcs/8.0.1": {"type": "package", "compile": {"lib/net8.0/System.Security.Cryptography.Pkcs.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Security.Cryptography.Pkcs.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Security.Cryptography.Pkcs.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Cryptography.ProtectedData/9.0.4": {"type": "package", "compile": {"lib/net9.0/System.Security.Cryptography.ProtectedData.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/System.Security.Cryptography.ProtectedData.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "System.Security.Cryptography.Xml/8.0.2": {"type": "package", "dependencies": {"System.Security.Cryptography.Pkcs": "8.0.1"}, "compile": {"lib/net8.0/System.Security.Cryptography.Xml.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Security.Cryptography.Xml.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Security.Principal.Windows/5.0.0": {"type": "package", "compile": {"ref/netcoreapp3.0/System.Security.Principal.Windows.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Security.Principal.Windows.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Text.Encoding/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Text.Encoding.dll": {"related": ".xml"}}}, "System.Text.Encoding.CodePages/4.5.1": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "2.1.2", "System.Runtime.CompilerServices.Unsafe": "4.5.2"}, "compile": {"lib/netstandard2.0/System.Text.Encoding.CodePages.dll": {}}, "runtime": {"lib/netstandard2.0/System.Text.Encoding.CodePages.dll": {}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp2.0/System.Text.Encoding.CodePages.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Text.Encodings.Web/8.0.0": {"type": "package", "compile": {"lib/net8.0/System.Text.Encodings.Web.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Text.Encodings.Web.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/browser/lib/net8.0/System.Text.Encodings.Web.dll": {"assetType": "runtime", "rid": "browser"}}}, "System.Text.Json/9.0.5": {"type": "package", "compile": {"lib/net9.0/System.Text.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/System.Text.Json.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/System.Text.Json.targets": {}}}, "System.Threading.AccessControl/8.0.0": {"type": "package", "compile": {"lib/net8.0/System.Threading.AccessControl.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Threading.AccessControl.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Threading.AccessControl.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Threading.Channels/8.0.0": {"type": "package", "compile": {"lib/net8.0/System.Threading.Channels.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Threading.Channels.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Threading.Tasks/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Threading.Tasks.dll": {"related": ".xml"}}}, "System.Threading.Tasks.Extensions/4.6.0": {"type": "package", "compile": {"lib/netcoreapp2.1/_._": {}}, "runtime": {"lib/netcoreapp2.1/_._": {}}}, "ZooKeeperNetEx/3.4.12.4": {"type": "package", "compile": {"lib/netstandard2.0/ZooKeeperNetEx.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/ZooKeeperNetEx.dll": {"related": ".xml"}}}, "Webaby/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v9.0", "dependencies": {"AutoMapper": "14.0.0", "Autofac": "8.2.1", "Autofac.Extensions.DependencyInjection": "10.0.0", "CuttingEdge.Conditions.NetStandard": "1.2.0", "Dapper": "2.1.66", "DistributedLock": "2.6.0", "DocumentFormat.OpenXml": "3.3.0", "Flee": "2.0.0", "Microsoft.AspNetCore.Http.Abstractions": "2.3.0", "Microsoft.AspNetCore.Identity": "2.3.1", "Microsoft.AspNetCore.Identity.EntityFrameworkCore": "9.0.4", "Microsoft.AspNetCore.Mvc": "2.3.0", "Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation": "9.0.4", "Microsoft.AspNetCore.SignalR": "1.2.0", "Microsoft.Data.SqlClient": "5.2.2", "Microsoft.EntityFrameworkCore": "9.0.5", "Microsoft.EntityFrameworkCore.SqlServer": "9.0.5", "Microsoft.Extensions.Configuration": "9.0.4", "Microsoft.Extensions.Configuration.Binder": "9.0.4", "Microsoft.Extensions.DependencyInjection": "9.0.5", "Microsoft.IdentityModel.Protocols": "8.9.0", "Newtonsoft.Json": "13.0.3", "Npgsql.EntityFrameworkCore.PostgreSQL": "9.0.4", "RestSharp": "112.1.0", "System.DirectoryServices.AccountManagement": "9.0.4"}, "compile": {"bin/placeholder/Webaby.dll": {}}, "runtime": {"bin/placeholder/Webaby.dll": {}}}}}, "libraries": {"Autofac/8.2.1": {"sha512": "q5xYBykgdvBlcmrXzHcDWcfeVktGEraluypkC0IRTupfxk+r+z+JcXgKj5kldKGe1fflyW3Ya3y9Soe9J2CqEA==", "type": "package", "path": "autofac/8.2.1", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "autofac.8.2.1.nupkg.sha512", "autofac.nuspec", "icon.png", "lib/net6.0/Autofac.dll", "lib/net6.0/Autofac.xml", "lib/net7.0/Autofac.dll", "lib/net7.0/Autofac.xml", "lib/net8.0/Autofac.dll", "lib/net8.0/Autofac.xml", "lib/netstandard2.0/Autofac.dll", "lib/netstandard2.0/Autofac.xml", "lib/netstandard2.1/Autofac.dll", "lib/netstandard2.1/Autofac.xml"]}, "Autofac.Extensions.DependencyInjection/10.0.0": {"sha512": "ZjR/onUlP7BzQ7VBBigQepWLAyAzi3VRGX3pP6sBqkPRiT61fsTZqbTpRUKxo30TMgbs1o3y6bpLbETix4SJog==", "type": "package", "path": "autofac.extensions.dependencyinjection/10.0.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "autofac.extensions.dependencyinjection.10.0.0.nupkg.sha512", "autofac.extensions.dependencyinjection.nuspec", "icon.png", "lib/net6.0/Autofac.Extensions.DependencyInjection.dll", "lib/net6.0/Autofac.Extensions.DependencyInjection.xml", "lib/net7.0/Autofac.Extensions.DependencyInjection.dll", "lib/net7.0/Autofac.Extensions.DependencyInjection.xml", "lib/net8.0/Autofac.Extensions.DependencyInjection.dll", "lib/net8.0/Autofac.Extensions.DependencyInjection.xml", "lib/netstandard2.0/Autofac.Extensions.DependencyInjection.dll", "lib/netstandard2.0/Autofac.Extensions.DependencyInjection.xml", "lib/netstandard2.1/Autofac.Extensions.DependencyInjection.dll", "lib/netstandard2.1/Autofac.Extensions.DependencyInjection.xml"]}, "AutoMapper/14.0.0": {"sha512": "OC+1neAPM4oCCqQj3g2GJ2shziNNhOkxmNB9cVS8jtx4JbgmRzLcUOxB9Tsz6cVPHugdkHgCaCrTjjSI0Z5sCQ==", "type": "package", "path": "automapper/14.0.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "automapper.14.0.0.nupkg.sha512", "automapper.nuspec", "icon.png", "lib/net8.0/AutoMapper.dll", "lib/net8.0/AutoMapper.xml"]}, "Azure.Core/1.38.0": {"sha512": "IuEgCoVA0ef7E4pQtpC3+TkPbzaoQfa77HlfJDmfuaJUCVJmn7fT0izamZiryW5sYUFKizsftIxMkXKbgIcPMQ==", "type": "package", "path": "azure.core/1.38.0", "files": [".nupkg.metadata", ".signature.p7s", "CHANGELOG.md", "README.md", "azure.core.1.38.0.nupkg.sha512", "azure.core.nuspec", "azureicon.png", "lib/net461/Azure.Core.dll", "lib/net461/Azure.Core.xml", "lib/net472/Azure.Core.dll", "lib/net472/Azure.Core.xml", "lib/net6.0/Azure.Core.dll", "lib/net6.0/Azure.Core.xml", "lib/netstandard2.0/Azure.Core.dll", "lib/netstandard2.0/Azure.Core.xml"]}, "Azure.Identity/1.11.4": {"sha512": "Sf4BoE6Q3jTgFkgBkx7qztYOFELBCo+wQgpYDwal/qJ1unBH73ywPztIJKXBXORRzAeNijsuxhk94h0TIMvfYg==", "type": "package", "path": "azure.identity/1.11.4", "files": [".nupkg.metadata", ".signature.p7s", "CHANGELOG.md", "README.md", "azure.identity.1.11.4.nupkg.sha512", "azure.identity.nuspec", "azureicon.png", "lib/netstandard2.0/Azure.Identity.dll", "lib/netstandard2.0/Azure.Identity.xml"]}, "Azure.Storage.Blobs/12.19.1": {"sha512": "x43hWFJ4sPQ23TD4piCwT+KlQpZT8pNDAzqj6yUCqh+WJ2qcQa17e1gh6ZOeT2QNFQTTDSuR56fm2bIV7i11/w==", "type": "package", "path": "azure.storage.blobs/12.19.1", "files": [".nupkg.metadata", ".signature.p7s", "CHANGELOG.md", "README.md", "azure.storage.blobs.12.19.1.nupkg.sha512", "azure.storage.blobs.nuspec", "azureicon.png", "lib/net6.0/Azure.Storage.Blobs.dll", "lib/net6.0/Azure.Storage.Blobs.xml", "lib/netstandard2.0/Azure.Storage.Blobs.dll", "lib/netstandard2.0/Azure.Storage.Blobs.xml", "lib/netstandard2.1/Azure.Storage.Blobs.dll", "lib/netstandard2.1/Azure.Storage.Blobs.xml"]}, "Azure.Storage.Common/12.18.1": {"sha512": "ohCslqP9yDKIn+DVjBEOBuieB1QwsUCz+BwHYNaJ3lcIsTSiI4Evnq81HcKe8CqM8qvdModbipVQKpnxpbdWqA==", "type": "package", "path": "azure.storage.common/12.18.1", "files": [".nupkg.metadata", ".signature.p7s", "CHANGELOG.md", "README.md", "azure.storage.common.12.18.1.nupkg.sha512", "azure.storage.common.nuspec", "azureicon.png", "lib/net6.0/Azure.Storage.Common.dll", "lib/net6.0/Azure.Storage.Common.xml", "lib/netstandard2.0/Azure.Storage.Common.dll", "lib/netstandard2.0/Azure.Storage.Common.xml"]}, "CuttingEdge.Conditions.NetStandard/1.2.0": {"sha512": "CcPWdCfZQTKx24zqFmPVT/LfFRuMw4JVaXluwq3PIQ6wzSfyGxSbrQbvnw0J1py245fUQPP3ZQtIt0lhZeNgEw==", "type": "package", "path": "cuttingedge.conditions.netstandard/1.2.0", "files": [".nupkg.metadata", ".signature.p7s", "cuttingedge.conditions.netstandard.1.2.0.nupkg.sha512", "cuttingedge.conditions.netstandard.nuspec", "lib/netstandard2.0/CuttingEdge.Conditions.dll"]}, "Dapper/2.1.66": {"sha512": "/q77jUgDOS+bzkmk3Vy9SiWMaetTw+NOoPAV0xPBsGVAyljd5S6P+4RUW7R3ZUGGr9lDRyPKgAMj2UAOwvqZYw==", "type": "package", "path": "dapper/2.1.66", "files": [".nupkg.metadata", ".signature.p7s", "Dapper.png", "dapper.2.1.66.nupkg.sha512", "dapper.nuspec", "lib/net461/Dapper.dll", "lib/net461/Dapper.xml", "lib/net8.0/Dapper.dll", "lib/net8.0/Dapper.xml", "lib/netstandard2.0/Dapper.dll", "lib/netstandard2.0/Dapper.xml", "readme.md"]}, "DistributedLock/2.6.0": {"sha512": "smtKmEkaMW18sQhmTYZw0RlBj5TUfptNHyFFkkBnj/OFGdfZgIKxHwHc9EiY69S94Qtk4UC+6fQ+dnqN8gZZXA==", "type": "package", "path": "distributedlock/2.6.0", "files": [".nupkg.metadata", ".signature.p7s", "distributedlock.2.6.0.nupkg.sha512", "distributedlock.nuspec", "lib/net462/DistributedLock.dll", "lib/net462/DistributedLock.xml", "lib/net472/DistributedLock.dll", "lib/net472/DistributedLock.xml", "lib/netstandard2.0/DistributedLock.dll", "lib/netstandard2.0/DistributedLock.xml", "lib/netstandard2.1/DistributedLock.dll", "lib/netstandard2.1/DistributedLock.xml", "package.readme.md"]}, "DistributedLock.Azure/1.0.2": {"sha512": "0JsTkmN64lqZLPu3TdqeTFbo6zzzbxj6Uni5JMRVjf2pMf+VsbOtfnIzP7PX5lCqOHiYqQ9Jno7WX+Ud2G1VQQ==", "type": "package", "path": "distributedlock.azure/1.0.2", "files": [".nupkg.metadata", ".signature.p7s", "distributedlock.azure.1.0.2.nupkg.sha512", "distributedlock.azure.nuspec", "lib/net462/DistributedLock.Azure.dll", "lib/net462/DistributedLock.Azure.xml", "lib/netstandard2.0/DistributedLock.Azure.dll", "lib/netstandard2.0/DistributedLock.Azure.xml", "lib/netstandard2.1/DistributedLock.Azure.dll", "lib/netstandard2.1/DistributedLock.Azure.xml", "package.readme.md"]}, "DistributedLock.Core/1.0.8": {"sha512": "LAOsY8WxX8JU/n3lfXFz+f2pfnv0+4bHkCrOO3bwa28u9HrS3DlxSG6jf+u76SqesKs+KehZi0CndkfaUXBKvg==", "type": "package", "path": "distributedlock.core/1.0.8", "files": [".nupkg.metadata", ".signature.p7s", "distributedlock.core.1.0.8.nupkg.sha512", "distributedlock.core.nuspec", "lib/net462/DistributedLock.Core.dll", "lib/net462/DistributedLock.Core.xml", "lib/net8.0/DistributedLock.Core.dll", "lib/net8.0/DistributedLock.Core.xml", "lib/netstandard2.0/DistributedLock.Core.dll", "lib/netstandard2.0/DistributedLock.Core.xml", "lib/netstandard2.1/DistributedLock.Core.dll", "lib/netstandard2.1/DistributedLock.Core.xml", "package.readme.md"]}, "DistributedLock.FileSystem/1.0.3": {"sha512": "Q88LTgHXzcQq9ATa6SZSqAE+VFyIapNGFs+XG7nHx+SpzUf1ACnZ8iyz6fAT/ojH+OLNnWwF3kCYh83s+vnCbA==", "type": "package", "path": "distributedlock.filesystem/1.0.3", "files": [".nupkg.metadata", ".signature.p7s", "distributedlock.filesystem.1.0.3.nupkg.sha512", "distributedlock.filesystem.nuspec", "lib/net462/DistributedLock.FileSystem.dll", "lib/net462/DistributedLock.FileSystem.xml", "lib/netstandard2.0/DistributedLock.FileSystem.dll", "lib/netstandard2.0/DistributedLock.FileSystem.xml", "lib/netstandard2.1/DistributedLock.FileSystem.dll", "lib/netstandard2.1/DistributedLock.FileSystem.xml", "package.readme.md"]}, "DistributedLock.MySql/1.0.2": {"sha512": "1FIaBNM1/EmFeKc9qFNGf+8D/g3ZyBXoGYp0j+DQqKdfJJ500m45zYR4PWFsy1RYUL3E8VNnSvGpxUhB6OKrww==", "type": "package", "path": "distributedlock.mysql/1.0.2", "files": [".nupkg.metadata", ".signature.p7s", "distributedlock.mysql.1.0.2.nupkg.sha512", "distributedlock.mysql.nuspec", "lib/net462/DistributedLock.MySql.dll", "lib/net462/DistributedLock.MySql.xml", "lib/netstandard2.0/DistributedLock.MySql.dll", "lib/netstandard2.0/DistributedLock.MySql.xml", "lib/netstandard2.1/DistributedLock.MySql.dll", "lib/netstandard2.1/DistributedLock.MySql.xml", "package.readme.md"]}, "DistributedLock.Oracle/1.0.4": {"sha512": "AwuURnp5eQFsPlSMBRThrZN5vbMGfilYqmwxcbT/ndDr4DZrAa8aYoyzhjjDmwNrf67hXiI3t5KyG64oczIS3g==", "type": "package", "path": "distributedlock.oracle/1.0.4", "files": [".nupkg.metadata", ".signature.p7s", "distributedlock.oracle.1.0.4.nupkg.sha512", "distributedlock.oracle.nuspec", "lib/net472/DistributedLock.Oracle.dll", "lib/net472/DistributedLock.Oracle.xml", "lib/netstandard2.1/DistributedLock.Oracle.dll", "lib/netstandard2.1/DistributedLock.Oracle.xml", "package.readme.md"]}, "DistributedLock.Postgres/1.3.0": {"sha512": "CyjXmbhFgG30qPg4DwGnsmZO63y5DBRo+PI2xW0NwtFrsYsMVt/T1RcEUlb36JMKhDkf+euhnkanv/nU+W35qA==", "type": "package", "path": "distributedlock.postgres/1.3.0", "files": [".nupkg.metadata", ".signature.p7s", "distributedlock.postgres.1.3.0.nupkg.sha512", "distributedlock.postgres.nuspec", "lib/net462/DistributedLock.Postgres.dll", "lib/net462/DistributedLock.Postgres.xml", "lib/net8.0/DistributedLock.Postgres.dll", "lib/net8.0/DistributedLock.Postgres.xml", "lib/netstandard2.0/DistributedLock.Postgres.dll", "lib/netstandard2.0/DistributedLock.Postgres.xml", "lib/netstandard2.1/DistributedLock.Postgres.dll", "lib/netstandard2.1/DistributedLock.Postgres.xml", "package.readme.md"]}, "DistributedLock.Redis/1.0.3": {"sha512": "2Rzw2IYwG8/58hwhHC6oTlbrZ7JfjEmgZof9/mPH9Vn1mDQUhWbO1VFRFInu87RYJW/6iUg3/VadDhMN7ykyWQ==", "type": "package", "path": "distributedlock.redis/1.0.3", "files": [".nupkg.metadata", ".signature.p7s", "distributedlock.redis.1.0.3.nupkg.sha512", "distributedlock.redis.nuspec", "lib/net462/DistributedLock.Redis.dll", "lib/net462/DistributedLock.Redis.xml", "lib/netstandard2.0/DistributedLock.Redis.dll", "lib/netstandard2.0/DistributedLock.Redis.xml", "lib/netstandard2.1/DistributedLock.Redis.dll", "lib/netstandard2.1/DistributedLock.Redis.xml", "package.readme.md"]}, "DistributedLock.SqlServer/1.0.6": {"sha512": "fT8XiBDDd3etCWRr77dgV+IbvNc+nDuYJ06f0vxWqA+BzX6gC7FU0Cn11a1GoufQW8IpE56nFbvDtNrfyewHeQ==", "type": "package", "path": "distributedlock.sqlserver/1.0.6", "files": [".nupkg.metadata", ".signature.p7s", "distributedlock.sqlserver.1.0.6.nupkg.sha512", "distributedlock.sqlserver.nuspec", "lib/net462/DistributedLock.SqlServer.dll", "lib/net462/DistributedLock.SqlServer.xml", "lib/netstandard2.0/DistributedLock.SqlServer.dll", "lib/netstandard2.0/DistributedLock.SqlServer.xml", "lib/netstandard2.1/DistributedLock.SqlServer.dll", "lib/netstandard2.1/DistributedLock.SqlServer.xml", "package.readme.md"]}, "DistributedLock.WaitHandles/1.0.1": {"sha512": "9Wabh4Nqm/9OPvWeD3fgpc6RgEhtpEczy6gl3AITi6+MTFr/G6axrPJlDPWo9Jr+mI1f1FB/9K2O5FQN+vo5ZA==", "type": "package", "path": "distributedlock.waithandles/1.0.1", "files": [".nupkg.metadata", ".signature.p7s", "distributedlock.waithandles.1.0.1.nupkg.sha512", "distributedlock.waithandles.nuspec", "lib/net461/DistributedLock.WaitHandles.dll", "lib/net461/DistributedLock.WaitHandles.xml", "lib/netstandard2.0/DistributedLock.WaitHandles.dll", "lib/netstandard2.0/DistributedLock.WaitHandles.xml", "lib/netstandard2.1/DistributedLock.WaitHandles.dll", "lib/netstandard2.1/DistributedLock.WaitHandles.xml"]}, "DistributedLock.ZooKeeper/1.0.0": {"sha512": "aGu3NxkUAaTYPEP2PLXrxH7UfKcv1alp7xMBELzg089V+SSKljRXP90NgphrOX5IL6yTT3e0N0v9gFEz1xWOvA==", "type": "package", "path": "distributedlock.zookeeper/1.0.0", "files": [".nupkg.metadata", ".signature.p7s", "distributedlock.zookeeper.1.0.0.nupkg.sha512", "distributedlock.zookeeper.nuspec", "lib/net461/DistributedLock.ZooKeeper.dll", "lib/net461/DistributedLock.ZooKeeper.xml", "lib/netstandard2.0/DistributedLock.ZooKeeper.dll", "lib/netstandard2.0/DistributedLock.ZooKeeper.xml", "lib/netstandard2.1/DistributedLock.ZooKeeper.dll", "lib/netstandard2.1/DistributedLock.ZooKeeper.xml"]}, "DocumentFormat.OpenXml/3.3.0": {"sha512": "JogRPJNiE6kKvbuCqVRX691pPWeGMqdQgjrUwRYkdpfkMmtElfqAgcRR73geYj7OtBeEpstldZXXzJw27LUI9w==", "type": "package", "path": "documentformat.openxml/3.3.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "documentformat.openxml.3.3.0.nupkg.sha512", "documentformat.openxml.nuspec", "icon.png", "lib/net35/DocumentFormat.OpenXml.dll", "lib/net35/DocumentFormat.OpenXml.xml", "lib/net40/DocumentFormat.OpenXml.dll", "lib/net40/DocumentFormat.OpenXml.xml", "lib/net46/DocumentFormat.OpenXml.dll", "lib/net46/DocumentFormat.OpenXml.xml", "lib/net8.0/DocumentFormat.OpenXml.dll", "lib/net8.0/DocumentFormat.OpenXml.xml", "lib/netstandard2.0/DocumentFormat.OpenXml.dll", "lib/netstandard2.0/DocumentFormat.OpenXml.xml"]}, "DocumentFormat.OpenXml.Framework/3.3.0": {"sha512": "R5CLzEoeyr7XDB7g3NTxRobcU19agaxVAhGZm+fZUShJGiU4bw8oUgnA2BNFepigJckfFMayOBMAbV3kDXNInA==", "type": "package", "path": "documentformat.openxml.framework/3.3.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "documentformat.openxml.framework.3.3.0.nupkg.sha512", "documentformat.openxml.framework.nuspec", "icon.png", "lib/net35/DocumentFormat.OpenXml.Framework.dll", "lib/net35/DocumentFormat.OpenXml.Framework.xml", "lib/net40/DocumentFormat.OpenXml.Framework.dll", "lib/net40/DocumentFormat.OpenXml.Framework.xml", "lib/net46/DocumentFormat.OpenXml.Framework.dll", "lib/net46/DocumentFormat.OpenXml.Framework.xml", "lib/net6.0/DocumentFormat.OpenXml.Framework.dll", "lib/net6.0/DocumentFormat.OpenXml.Framework.xml", "lib/net8.0/DocumentFormat.OpenXml.Framework.dll", "lib/net8.0/DocumentFormat.OpenXml.Framework.xml", "lib/netstandard2.0/DocumentFormat.OpenXml.Framework.dll", "lib/netstandard2.0/DocumentFormat.OpenXml.Framework.xml"]}, "Flee/2.0.0": {"sha512": "7PC3aw0JkVtD9XO17VMM0tObqaEbnCRWdJmoDMyR0Me0hSY0sCYVGEFR9MxiEuEc2ReC8U0fvSjsH6PCV6O4ZA==", "type": "package", "path": "flee/2.0.0", "files": [".nupkg.metadata", ".signature.p7s", "content/Resources/DocComments.xml", "contentFiles/any/net5.0/Resources/DocComments.xml", "contentFiles/any/net6.0/Resources/DocComments.xml", "contentFiles/any/netstandard2.0/Resources/DocComments.xml", "contentFiles/any/netstandard2.1/Resources/DocComments.xml", "flee.2.0.0.nupkg.sha512", "flee.nuspec", "lib/net5.0/Flee.dll", "lib/net6.0/Flee.dll", "lib/netstandard2.0/Flee.dll", "lib/netstandard2.1/Flee.dll"]}, "Microsoft.AspNetCore.Antiforgery/2.3.0": {"sha512": "C4o0buZnlzyhCpBny7//4Z9sKcxpVdFo0TRfE14q9Lac6+xkgwunPON+47FWzyqbUXu52SaABEoAiHC5lMam0A==", "type": "package", "path": "microsoft.aspnetcore.antiforgery/2.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Antiforgery.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Antiforgery.xml", "microsoft.aspnetcore.antiforgery.2.3.0.nupkg.sha512", "microsoft.aspnetcore.antiforgery.nuspec"]}, "Microsoft.AspNetCore.Authentication/2.3.0": {"sha512": "Tq6bxTOe65Ikh9dWVTEOqpvNqBGIQueO0J+zl2rQba0yP0YV66iYDkSz9MqTdRZftvJ2I5kMeRUm9Z2mjEAbUQ==", "type": "package", "path": "microsoft.aspnetcore.authentication/2.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Authentication.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Authentication.xml", "microsoft.aspnetcore.authentication.2.3.0.nupkg.sha512", "microsoft.aspnetcore.authentication.nuspec"]}, "Microsoft.AspNetCore.Authentication.Abstractions/2.3.0": {"sha512": "ve6uvLwKNRkfnO/QeN9M8eUJ49lCnWv/6/9p6iTEuiI6Rtsz+myaBAjdMzLuTViQY032xbTF5AdZF5BJzJJyXQ==", "type": "package", "path": "microsoft.aspnetcore.authentication.abstractions/2.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Authentication.Abstractions.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Authentication.Abstractions.xml", "microsoft.aspnetcore.authentication.abstractions.2.3.0.nupkg.sha512", "microsoft.aspnetcore.authentication.abstractions.nuspec"]}, "Microsoft.AspNetCore.Authentication.Cookies/2.3.0": {"sha512": "w3JPWHreXJ/Uv9CLkQtGCLwTbxZKY+94QPVi1RxcMuBTyRp+C9SdynznHEjnHWnw6QFNEHnBuHmWW3OYrvbpEQ==", "type": "package", "path": "microsoft.aspnetcore.authentication.cookies/2.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Authentication.Cookies.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Authentication.Cookies.xml", "microsoft.aspnetcore.authentication.cookies.2.3.0.nupkg.sha512", "microsoft.aspnetcore.authentication.cookies.nuspec"]}, "Microsoft.AspNetCore.Authentication.Core/2.3.0": {"sha512": "gnLnKGawBjqBnU9fEuel3VcYAARkjyONAliaGDfMc8o8HBtfh+HrOPEoR8Xx4b2RnMb7uxdBDOvEAC7sul79ig==", "type": "package", "path": "microsoft.aspnetcore.authentication.core/2.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Authentication.Core.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Authentication.Core.xml", "microsoft.aspnetcore.authentication.core.2.3.0.nupkg.sha512", "microsoft.aspnetcore.authentication.core.nuspec"]}, "Microsoft.AspNetCore.Authorization/2.3.0": {"sha512": "2/aBgLqBXva/+w8pzRNY8ET43Gi+dr1gv/7ySfbsh23lTK6IAgID5MGUEa1hreNIF+0XpW4tX7QwVe70+YvaPg==", "type": "package", "path": "microsoft.aspnetcore.authorization/2.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Authorization.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Authorization.xml", "microsoft.aspnetcore.authorization.2.3.0.nupkg.sha512", "microsoft.aspnetcore.authorization.nuspec"]}, "Microsoft.AspNetCore.Authorization.Policy/2.3.0": {"sha512": "vn31uQ1dA1MIV2WNNDOOOm88V5KgR9esfi0LyQ6eVaGq2h0Yw+R29f5A6qUNJt+RccS3qkYayylAy9tP1wV+7Q==", "type": "package", "path": "microsoft.aspnetcore.authorization.policy/2.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Authorization.Policy.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Authorization.Policy.xml", "microsoft.aspnetcore.authorization.policy.2.3.0.nupkg.sha512", "microsoft.aspnetcore.authorization.policy.nuspec"]}, "Microsoft.AspNetCore.Connections.Abstractions/2.3.0": {"sha512": "ULFSa+/L+WiAHVlIFHyg0OmHChU9Hx+K+xnt0hbIU5XmT1EGy0pNDx23QAzDtAy9jxQrTG6MX0MdvMeU4D4c7w==", "type": "package", "path": "microsoft.aspnetcore.connections.abstractions/2.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Connections.Abstractions.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Connections.Abstractions.xml", "microsoft.aspnetcore.connections.abstractions.2.3.0.nupkg.sha512", "microsoft.aspnetcore.connections.abstractions.nuspec"]}, "Microsoft.AspNetCore.Cors/2.3.0": {"sha512": "AVVAOi4oGar9+7pFYGWw5NeD8qITe+lswIawJJ64zuLpU5YYdW90ALM8XDsYjEulNLziM3qgqjoLaWpr9Ic4vQ==", "type": "package", "path": "microsoft.aspnetcore.cors/2.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Cors.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Cors.xml", "microsoft.aspnetcore.cors.2.3.0.nupkg.sha512", "microsoft.aspnetcore.cors.nuspec"]}, "Microsoft.AspNetCore.Cryptography.Internal/9.0.4": {"sha512": "E4pHyEb2Ul5a6bIwraGtw9TN39a/C2asyVPEJoyItc0reV4Y26FsPcEdcXyKjBbP4kSz9iU1Cz4Yhx/aOFPpqA==", "type": "package", "path": "microsoft.aspnetcore.cryptography.internal/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/net462/Microsoft.AspNetCore.Cryptography.Internal.dll", "lib/net462/Microsoft.AspNetCore.Cryptography.Internal.xml", "lib/net9.0/Microsoft.AspNetCore.Cryptography.Internal.dll", "lib/net9.0/Microsoft.AspNetCore.Cryptography.Internal.xml", "lib/netstandard2.0/Microsoft.AspNetCore.Cryptography.Internal.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Cryptography.Internal.xml", "microsoft.aspnetcore.cryptography.internal.9.0.4.nupkg.sha512", "microsoft.aspnetcore.cryptography.internal.nuspec"]}, "Microsoft.AspNetCore.Cryptography.KeyDerivation/9.0.4": {"sha512": "5v9Kj2arRrCftLKW80Hfj31HkNnjcKyw57lQhF84drvGxJlCR63J0zMM1sMM+Hc+KCQjuoDmHtjwN0uOT+X3ag==", "type": "package", "path": "microsoft.aspnetcore.cryptography.keyderivation/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/net462/Microsoft.AspNetCore.Cryptography.KeyDerivation.dll", "lib/net462/Microsoft.AspNetCore.Cryptography.KeyDerivation.xml", "lib/net9.0/Microsoft.AspNetCore.Cryptography.KeyDerivation.dll", "lib/net9.0/Microsoft.AspNetCore.Cryptography.KeyDerivation.xml", "lib/netstandard2.0/Microsoft.AspNetCore.Cryptography.KeyDerivation.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Cryptography.KeyDerivation.xml", "microsoft.aspnetcore.cryptography.keyderivation.9.0.4.nupkg.sha512", "microsoft.aspnetcore.cryptography.keyderivation.nuspec"]}, "Microsoft.AspNetCore.DataProtection/2.3.0": {"sha512": "C+FhGaA8ekrfes0Ujhtkhk74Bpkt6Zt+NrMaGrCWBqW1LFzqw/pXDbMbpcAyI9hbYgZfC6+t01As4LGXbdxG4A==", "type": "package", "path": "microsoft.aspnetcore.dataprotection/2.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.DataProtection.dll", "lib/netstandard2.0/Microsoft.AspNetCore.DataProtection.xml", "microsoft.aspnetcore.dataprotection.2.3.0.nupkg.sha512", "microsoft.aspnetcore.dataprotection.nuspec"]}, "Microsoft.AspNetCore.DataProtection.Abstractions/2.3.0": {"sha512": "71GdtUkVDagLsBt+YatfzUItnbT2vIjHxWySNE2MkgIDhqT3g4sNNxOj/0PlPTpc1+mG3ZwfUoZ61jIt1wPw7g==", "type": "package", "path": "microsoft.aspnetcore.dataprotection.abstractions/2.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.DataProtection.Abstractions.dll", "lib/netstandard2.0/Microsoft.AspNetCore.DataProtection.Abstractions.xml", "microsoft.aspnetcore.dataprotection.abstractions.2.3.0.nupkg.sha512", "microsoft.aspnetcore.dataprotection.abstractions.nuspec"]}, "Microsoft.AspNetCore.Diagnostics.Abstractions/2.3.0": {"sha512": "zlraKnYaX7UPhmVMlrQ1ecpUep2LxKjJQsTox5gEFv5tCfsLNmtY0B/5cKFkK9ee4U0EOg2/Hvpmnb4NKS7yrA==", "type": "package", "path": "microsoft.aspnetcore.diagnostics.abstractions/2.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Diagnostics.Abstractions.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Diagnostics.Abstractions.xml", "microsoft.aspnetcore.diagnostics.abstractions.2.3.0.nupkg.sha512", "microsoft.aspnetcore.diagnostics.abstractions.nuspec"]}, "Microsoft.AspNetCore.Hosting.Abstractions/2.3.0": {"sha512": "4ivq53W2k6Nj4eez9wc81ytfGj6HR1NaZJCpOrvghJo9zHuQF57PLhPoQH5ItyCpHXnrN/y7yJDUm+TGYzrx0w==", "type": "package", "path": "microsoft.aspnetcore.hosting.abstractions/2.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Hosting.Abstractions.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Hosting.Abstractions.xml", "microsoft.aspnetcore.hosting.abstractions.2.3.0.nupkg.sha512", "microsoft.aspnetcore.hosting.abstractions.nuspec"]}, "Microsoft.AspNetCore.Hosting.Server.Abstractions/2.3.0": {"sha512": "F5iHx7odAbFKBV1DNPDkFFcVmD5Tk7rk+tYm3LMQxHEFFdjlg5QcYb5XhHAefl5YaaPeG6ad+/ck8kSG3/D6kw==", "type": "package", "path": "microsoft.aspnetcore.hosting.server.abstractions/2.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Hosting.Server.Abstractions.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Hosting.Server.Abstractions.xml", "microsoft.aspnetcore.hosting.server.abstractions.2.3.0.nupkg.sha512", "microsoft.aspnetcore.hosting.server.abstractions.nuspec"]}, "Microsoft.AspNetCore.Html.Abstractions/2.3.0": {"sha512": "RgoGZ0jTVDx2GT2YA93y2D9OckL3KyV2O1AUwNuKsJIwIHOlSctXEokXpphGyzdxgt+WtjKKKEZTXKZoOOvzyw==", "type": "package", "path": "microsoft.aspnetcore.html.abstractions/2.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Html.Abstractions.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Html.Abstractions.xml", "microsoft.aspnetcore.html.abstractions.2.3.0.nupkg.sha512", "microsoft.aspnetcore.html.abstractions.nuspec"]}, "Microsoft.AspNetCore.Http/2.3.0": {"sha512": "I9azEG2tZ4DDHAFgv+N38e6Yhttvf+QjE2j2UYyCACE7Swm5/0uoihCMWZ87oOZYeqiEFSxbsfpT71OYHe2tpw==", "type": "package", "path": "microsoft.aspnetcore.http/2.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Http.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Http.xml", "microsoft.aspnetcore.http.2.3.0.nupkg.sha512", "microsoft.aspnetcore.http.nuspec"]}, "Microsoft.AspNetCore.Http.Abstractions/2.3.0": {"sha512": "********************************+3b53ybuGGNTXEj1/DStQJ4NWjFL6QTRQpL9zt7nDyKxZdJOlcnq+Q==", "type": "package", "path": "microsoft.aspnetcore.http.abstractions/2.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Http.Abstractions.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Http.Abstractions.xml", "microsoft.aspnetcore.http.abstractions.2.3.0.nupkg.sha512", "microsoft.aspnetcore.http.abstractions.nuspec"]}, "Microsoft.AspNetCore.Http.Connections/1.2.0": {"sha512": "VYMCOLvdT0y3O9lk4jUuIs8+re7u5+i+ka6ZZ6fIzSJ94c/JeMnAOOg39EB2i4crPXvLoiSdzKWlNPJgTbCZ2g==", "type": "package", "path": "microsoft.aspnetcore.http.connections/1.2.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Http.Connections.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Http.Connections.xml", "microsoft.aspnetcore.http.connections.1.2.0.nupkg.sha512", "microsoft.aspnetcore.http.connections.nuspec"]}, "Microsoft.AspNetCore.Http.Connections.Common/1.2.0": {"sha512": "yUA7eg6kv7Wbz5TCW4PqS5/kYE5VxUIEDvoxjw4p1RwS2LGm84F9fBtM0mD6wrRfiv1NUyJ7WBjn3PWd/ccO+w==", "type": "package", "path": "microsoft.aspnetcore.http.connections.common/1.2.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Http.Connections.Common.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Http.Connections.Common.xml", "microsoft.aspnetcore.http.connections.common.1.2.0.nupkg.sha512", "microsoft.aspnetcore.http.connections.common.nuspec"]}, "Microsoft.AspNetCore.Http.Extensions/2.3.0": {"sha512": "EY2u/wFF5jsYwGXXswfQWrSsFPmiXsniAlUWo3rv/MGYf99ZFsENDnZcQP6W3c/+xQmQXq0NauzQ7jyy+o1LDQ==", "type": "package", "path": "microsoft.aspnetcore.http.extensions/2.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Http.Extensions.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Http.Extensions.xml", "microsoft.aspnetcore.http.extensions.2.3.0.nupkg.sha512", "microsoft.aspnetcore.http.extensions.nuspec"]}, "Microsoft.AspNetCore.Http.Features/2.3.0": {"sha512": "f10WUgcsKqrkmnz6gt8HeZ7kyKjYN30PO7cSic1lPtH7paPtnQqXPOveul/SIPI43PhRD4trttg4ywnrEmmJpA==", "type": "package", "path": "microsoft.aspnetcore.http.features/2.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Http.Features.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Http.Features.xml", "microsoft.aspnetcore.http.features.2.3.0.nupkg.sha512", "microsoft.aspnetcore.http.features.nuspec"]}, "Microsoft.AspNetCore.Identity/2.3.1": {"sha512": "JcQ4pNXg+IISfcR95jeO2ZRt38N67MrUEj28HBmwfqD96BUyw4S54tQhrBmCOyPlf2vgNvSz/tsGAG7EgC0yRg==", "type": "package", "path": "microsoft.aspnetcore.identity/2.3.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Identity.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Identity.xml", "microsoft.aspnetcore.identity.2.3.1.nupkg.sha512", "microsoft.aspnetcore.identity.nuspec"]}, "Microsoft.AspNetCore.Identity.EntityFrameworkCore/9.0.4": {"sha512": "IC3X6Db6H0cXdE2zGtyk/jmSwXhHbJZaiNpg7TNFV/Biu/NgO6l/GuwgE0D1U6U9pca00WsqxESkNov+WA77CA==", "type": "package", "path": "microsoft.aspnetcore.identity.entityframeworkcore/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/net9.0/Microsoft.AspNetCore.Identity.EntityFrameworkCore.dll", "lib/net9.0/Microsoft.AspNetCore.Identity.EntityFrameworkCore.xml", "microsoft.aspnetcore.identity.entityframeworkcore.9.0.4.nupkg.sha512", "microsoft.aspnetcore.identity.entityframeworkcore.nuspec"]}, "Microsoft.AspNetCore.JsonPatch/2.3.0": {"sha512": "xrcdmMlZxwZM0n65T8gq8/erN0aW8HyBXJYhBk3cyfboVQsE/S9h9R59wILMgA4wal3glih+6l06XGs8AnELWA==", "type": "package", "path": "microsoft.aspnetcore.jsonpatch/2.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.JsonPatch.dll", "lib/netstandard2.0/Microsoft.AspNetCore.JsonPatch.xml", "microsoft.aspnetcore.jsonpatch.2.3.0.nupkg.sha512", "microsoft.aspnetcore.jsonpatch.nuspec"]}, "Microsoft.AspNetCore.Localization/2.3.0": {"sha512": "91LqEBTZnV8xkn56f2y3q6q4Lo+TgzxFkz72+zRzWR4tVwtGisqa/Rjjkvc5rJAnz6tAbuqiddKl03pAIUVrtQ==", "type": "package", "path": "microsoft.aspnetcore.localization/2.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Localization.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Localization.xml", "microsoft.aspnetcore.localization.2.3.0.nupkg.sha512", "microsoft.aspnetcore.localization.nuspec"]}, "Microsoft.AspNetCore.Mvc/2.3.0": {"sha512": "b+FL1XcPq79wf5pwXj+GSrhpn7JPTi0oSSgsh93zu5WSnEO7M7Y5HLEhlC7I1WgIp/HmWRfvca1/M99QGOF+Vg==", "type": "package", "path": "microsoft.aspnetcore.mvc/2.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Mvc.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Mvc.xml", "microsoft.aspnetcore.mvc.2.3.0.nupkg.sha512", "microsoft.aspnetcore.mvc.nuspec"]}, "Microsoft.AspNetCore.Mvc.Abstractions/2.3.0": {"sha512": "xrF8Fh10hEAS6Qp967IgPqNw2iz3/3Q8FQo+Jowbytaj1JJN86p0z851hSH4XP/sFnI5gFu7mGdkplirAJMWPw==", "type": "package", "path": "microsoft.aspnetcore.mvc.abstractions/2.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Abstractions.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Abstractions.xml", "microsoft.aspnetcore.mvc.abstractions.2.3.0.nupkg.sha512", "microsoft.aspnetcore.mvc.abstractions.nuspec"]}, "Microsoft.AspNetCore.Mvc.ApiExplorer/2.3.0": {"sha512": "aEY2mftyiiamLq3ReyRcqpdJEtbKPeW0tmtxb265D4NiO+5S7ll8EG+oUfADScoMMXazg7e/eOYK2eDafrzCQA==", "type": "package", "path": "microsoft.aspnetcore.mvc.apiexplorer/2.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Mvc.ApiExplorer.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Mvc.ApiExplorer.xml", "microsoft.aspnetcore.mvc.apiexplorer.2.3.0.nupkg.sha512", "microsoft.aspnetcore.mvc.apiexplorer.nuspec"]}, "Microsoft.AspNetCore.Mvc.Core/2.3.0": {"sha512": "WRXKrQ4wpzbo6Oo3+n6RegL83B/wXGo4/+Uo8yFFejMNCuNIyk/NL3Qe8MMRC3Mr9NqlOWwJr3qiWv/nKwg7dw==", "type": "package", "path": "microsoft.aspnetcore.mvc.core/2.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Core.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Core.xml", "microsoft.aspnetcore.mvc.core.2.3.0.nupkg.sha512", "microsoft.aspnetcore.mvc.core.nuspec"]}, "Microsoft.AspNetCore.Mvc.Cors/2.3.0": {"sha512": "yqSG4/V7LC10L4mLnK72ugNuaahh76MV+B/6qQqJN/ZVNAzFvI2RiA6yb+okQXUZ71gBF5K4f2c/kmBUnOnI2Q==", "type": "package", "path": "microsoft.aspnetcore.mvc.cors/2.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Cors.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Cors.xml", "microsoft.aspnetcore.mvc.cors.2.3.0.nupkg.sha512", "microsoft.aspnetcore.mvc.cors.nuspec"]}, "Microsoft.AspNetCore.Mvc.DataAnnotations/2.3.0": {"sha512": "PRVEIf7QIGKxgkBc5WERwXcK8vtQeLF33obig8zRMtk4UqQijomZx+taLjpHNXVKAMm4zII1z+XLsypOt4D8gQ==", "type": "package", "path": "microsoft.aspnetcore.mvc.dataannotations/2.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Mvc.DataAnnotations.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Mvc.DataAnnotations.xml", "microsoft.aspnetcore.mvc.dataannotations.2.3.0.nupkg.sha512", "microsoft.aspnetcore.mvc.dataannotations.nuspec"]}, "Microsoft.AspNetCore.Mvc.Formatters.Json/2.3.0": {"sha512": "bh/nygMBuGoMNitOsstyoqWQj2L4SICecHhVdOVCsMXmkr0KQgzpTE5RwB34ZbxRTERG0p+eG5xnGGdgeoZ/eA==", "type": "package", "path": "microsoft.aspnetcore.mvc.formatters.json/2.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Formatters.Json.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Formatters.Json.xml", "microsoft.aspnetcore.mvc.formatters.json.2.3.0.nupkg.sha512", "microsoft.aspnetcore.mvc.formatters.json.nuspec"]}, "Microsoft.AspNetCore.Mvc.Localization/2.3.0": {"sha512": "tn2/RtmOWar821IBaMuMrg/b11sPhFS0DlAa+R+0HIvREq17WkMhpkaY97BNzEhaPJqRljz6clVckRFq9hkNhQ==", "type": "package", "path": "microsoft.aspnetcore.mvc.localization/2.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Localization.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Localization.xml", "microsoft.aspnetcore.mvc.localization.2.3.0.nupkg.sha512", "microsoft.aspnetcore.mvc.localization.nuspec"]}, "Microsoft.AspNetCore.Mvc.Razor/2.3.0": {"sha512": "zA6hEvs2H+FeHfvTgL+xkM9+xnL9WT3hVjravMmH2U9OmPFTi2kHjILXxTp1Sd5puTOfPWnpdjHvN7HGCpAl1A==", "type": "package", "path": "microsoft.aspnetcore.mvc.razor/2.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Razor.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Razor.xml", "microsoft.aspnetcore.mvc.razor.2.3.0.nupkg.sha512", "microsoft.aspnetcore.mvc.razor.nuspec"]}, "Microsoft.AspNetCore.Mvc.Razor.Extensions/6.0.0": {"sha512": "M0h+ChPgydX2xY17agiphnAVa/Qh05RAP8eeuqGGhQKT10claRBlLNO6d2/oSV8zy0RLHzwLnNZm5xuC/gckGA==", "type": "package", "path": "microsoft.aspnetcore.mvc.razor.extensions/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Razor.Extensions.dll", "microsoft.aspnetcore.mvc.razor.extensions.6.0.0.nupkg.sha512", "microsoft.aspnetcore.mvc.razor.extensions.nuspec"]}, "Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation/9.0.4": {"sha512": "ppzthXSIAV5Z3tqayYUPqKZFbM+kHxiNflFm5O3392cXRn7b+S513EF+tLgU/kpaCF1OwprSe5b2iQOxLr7qvA==", "type": "package", "path": "microsoft.aspnetcore.mvc.razor.runtimecompilation/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "build/net9.0/Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation.targets", "buildTransitive/net9.0/Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation.targets", "lib/net9.0/Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation.dll", "lib/net9.0/Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation.xml", "microsoft.aspnetcore.mvc.razor.runtimecompilation.9.0.4.nupkg.sha512", "microsoft.aspnetcore.mvc.razor.runtimecompilation.nuspec"]}, "Microsoft.AspNetCore.Mvc.RazorPages/2.3.0": {"sha512": "RWSzjhpuXEs0IA/TNd+oUh9BVTOeDXTouJVhwfEVpC1x3ZwRIk+Ki2wKZPbu7EpOG/jITgFrHqWWWcwUbSCtzg==", "type": "package", "path": "microsoft.aspnetcore.mvc.razorpages/2.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Mvc.RazorPages.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Mvc.RazorPages.xml", "microsoft.aspnetcore.mvc.razorpages.2.3.0.nupkg.sha512", "microsoft.aspnetcore.mvc.razorpages.nuspec"]}, "Microsoft.AspNetCore.Mvc.TagHelpers/2.3.0": {"sha512": "7lG10p3eiO03NHii0wXUTVMf+BCvNfDZ6idtI4HVAt1pgYxdJHA2e4zMJHGZYUUMTneCWVzZousOtZGA8HpPZA==", "type": "package", "path": "microsoft.aspnetcore.mvc.taghelpers/2.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Mvc.TagHelpers.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Mvc.TagHelpers.xml", "microsoft.aspnetcore.mvc.taghelpers.2.3.0.nupkg.sha512", "microsoft.aspnetcore.mvc.taghelpers.nuspec"]}, "Microsoft.AspNetCore.Mvc.ViewFeatures/2.3.0": {"sha512": "H50xJ4voSid2rASnL1c0Ijp/TD5WlESczTOAv75SXNKjDg5ZRDWMIY7gnuE/W2jliy81vq+/koe/1Xlr55sGGQ==", "type": "package", "path": "microsoft.aspnetcore.mvc.viewfeatures/2.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Mvc.ViewFeatures.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Mvc.ViewFeatures.xml", "microsoft.aspnetcore.mvc.viewfeatures.2.3.0.nupkg.sha512", "microsoft.aspnetcore.mvc.viewfeatures.nuspec"]}, "Microsoft.AspNetCore.Razor/2.3.0": {"sha512": "aF7Zy7Qt6YxCDP5Y2sQdLch1JgrqjB1vooWDbJNjxUZ48AKd0KV9I4Fj/40sr8dW2BxrxiZuWHxgyXmeg2HQ5g==", "type": "package", "path": "microsoft.aspnetcore.razor/2.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Razor.dll", "microsoft.aspnetcore.razor.2.3.0.nupkg.sha512", "microsoft.aspnetcore.razor.nuspec"]}, "Microsoft.AspNetCore.Razor.Design/2.3.0": {"sha512": "f5wyCM2WKfa6IPSNimPbwcwugCs7bmINjxrwL/wU8yi8OHXg3kp4cCOP1vTXRp48n0y9N1syvZ8q0cjpUuWY1g==", "type": "package", "path": "microsoft.aspnetcore.razor.design/2.3.0", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "build/netstandard2.0/Microsoft.AspNetCore.Razor.Design.CodeGeneration.targets", "build/netstandard2.0/Microsoft.AspNetCore.Razor.Design.props", "buildMultiTargeting/Microsoft.AspNetCore.Razor.Design.props", "microsoft.aspnetcore.razor.design.2.3.0.nupkg.sha512", "microsoft.aspnetcore.razor.design.nuspec", "tasks/net46/Microsoft.AspNetCore.Razor.Tasks.dll", "tasks/netstandard2.0/Microsoft.AspNetCore.Razor.Tasks.dll", "tools/Microsoft.AspNetCore.Razor.Language.dll", "tools/Microsoft.CodeAnalysis.CSharp.dll", "tools/Microsoft.CodeAnalysis.Razor.dll", "tools/Microsoft.CodeAnalysis.dll", "tools/Newtonsoft.Json.dll", "tools/runtimes/unix/lib/netstandard1.3/System.Text.Encoding.CodePages.dll", "tools/runtimes/win/lib/netstandard1.3/System.Text.Encoding.CodePages.dll", "tools/rzc.deps.json", "tools/rzc.dll", "tools/rzc.runtimeconfig.json"]}, "Microsoft.AspNetCore.Razor.Language/6.0.0": {"sha512": "yCtBr1GSGzJrrp1NJUb4ltwFYMKHw/tJLnIDvg9g/FnkGIEzmE19tbCQqXARIJv5kdtBgsoVIdGLL+zmjxvM/A==", "type": "package", "path": "microsoft.aspnetcore.razor.language/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/netstandard2.0/Microsoft.AspNetCore.Razor.Language.dll", "microsoft.aspnetcore.razor.language.6.0.0.nupkg.sha512", "microsoft.aspnetcore.razor.language.nuspec"]}, "Microsoft.AspNetCore.Razor.Runtime/2.3.0": {"sha512": "LdNmHXt0oEeM3/CZNvmHq/STX8YOOII5DNmqLVBh3U7yV171C+gyuev8ouAu96D7rER31mrUiPNCMsvoEuY7Tw==", "type": "package", "path": "microsoft.aspnetcore.razor.runtime/2.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Razor.Runtime.dll", "microsoft.aspnetcore.razor.runtime.2.3.0.nupkg.sha512", "microsoft.aspnetcore.razor.runtime.nuspec"]}, "Microsoft.AspNetCore.ResponseCaching.Abstractions/2.3.0": {"sha512": "VTqhxnUiawKS22fss5fr/NMJ4MVQHFUgqyWOIaJ9pUY+jmYGCAa+VYfB5DLJYmsD4AhdKnlO6I9lML5tUbDNNA==", "type": "package", "path": "microsoft.aspnetcore.responsecaching.abstractions/2.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.ResponseCaching.Abstractions.dll", "lib/netstandard2.0/Microsoft.AspNetCore.ResponseCaching.Abstractions.xml", "microsoft.aspnetcore.responsecaching.abstractions.2.3.0.nupkg.sha512", "microsoft.aspnetcore.responsecaching.abstractions.nuspec"]}, "Microsoft.AspNetCore.Routing/2.3.0": {"sha512": "no5/VC0CAQuT4PK4rp2K5fqwuSfzr2mdB6m1XNfWVhHnwzpRQzKAu9flChiT/JTLKwVI0Vq2MSmSW2OFMDCNXg==", "type": "package", "path": "microsoft.aspnetcore.routing/2.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Routing.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Routing.xml", "microsoft.aspnetcore.routing.2.3.0.nupkg.sha512", "microsoft.aspnetcore.routing.nuspec"]}, "Microsoft.AspNetCore.Routing.Abstractions/2.3.0": {"sha512": "ZkFpUrSmp6TocxZLBEX3IBv5dPMbQuMs6L/BPl0WRfn32UVOtNYJQ0bLdh3cL9LMV0rmTW/5R0w8CBYxr0AOUw==", "type": "package", "path": "microsoft.aspnetcore.routing.abstractions/2.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Routing.Abstractions.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Routing.Abstractions.xml", "microsoft.aspnetcore.routing.abstractions.2.3.0.nupkg.sha512", "microsoft.aspnetcore.routing.abstractions.nuspec"]}, "Microsoft.AspNetCore.SignalR/1.2.0": {"sha512": "XoCcsOTdtBiXyOzUtpbCl0IaqMOYjnr+6dbDxvUCFn7NR6bu7CwrlQ3oQzkltTwDZH0b6VEUN9wZPOYvPHi+Lg==", "type": "package", "path": "microsoft.aspnetcore.signalr/1.2.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.SignalR.dll", "lib/netstandard2.0/Microsoft.AspNetCore.SignalR.xml", "microsoft.aspnetcore.signalr.1.2.0.nupkg.sha512", "microsoft.aspnetcore.signalr.nuspec"]}, "Microsoft.AspNetCore.SignalR.Common/1.2.0": {"sha512": "FZeXIaoWqe145ZPdfiptwkw/sP1BX1UD0706GNBwwoaFiKsNbLEl/Trhj2+idlp3qbX1BEwkQesKNxkopVY5Xg==", "type": "package", "path": "microsoft.aspnetcore.signalr.common/1.2.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.SignalR.Common.dll", "lib/netstandard2.0/Microsoft.AspNetCore.SignalR.Common.xml", "microsoft.aspnetcore.signalr.common.1.2.0.nupkg.sha512", "microsoft.aspnetcore.signalr.common.nuspec"]}, "Microsoft.AspNetCore.SignalR.Core/1.2.0": {"sha512": "eZTuMkSDw1uwjhLhJbMxgW2Cuyxfn0Kfqm8OBmqvuzE9Qc/VVzh8dGrAp2F9Pk7XKTDHmlhc5RTLcPPAZ5PSZw==", "type": "package", "path": "microsoft.aspnetcore.signalr.core/1.2.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.SignalR.Core.dll", "lib/netstandard2.0/Microsoft.AspNetCore.SignalR.Core.xml", "microsoft.aspnetcore.signalr.core.1.2.0.nupkg.sha512", "microsoft.aspnetcore.signalr.core.nuspec"]}, "Microsoft.AspNetCore.SignalR.Protocols.Json/1.2.0": {"sha512": "hNvZ7kQxp5Udqd/IFWViU35bUJvi4xnNzjkF28HRvrdrS7JNsIASTvMqArP6HLQUc3j6nlUOeShNhVmgI1wzHg==", "type": "package", "path": "microsoft.aspnetcore.signalr.protocols.json/1.2.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.SignalR.Protocols.Json.dll", "lib/netstandard2.0/Microsoft.AspNetCore.SignalR.Protocols.Json.xml", "microsoft.aspnetcore.signalr.protocols.json.1.2.0.nupkg.sha512", "microsoft.aspnetcore.signalr.protocols.json.nuspec"]}, "Microsoft.AspNetCore.WebSockets/2.3.0": {"sha512": "+T4zpnVPkIjvvkyhTH3WBJlTfqmTBRozvnMudAUDvcb4e+NrWf52q8BXh52rkCrBgX6Cudf6F/UhZwTowyBtKg==", "type": "package", "path": "microsoft.aspnetcore.websockets/2.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.WebSockets.dll", "lib/netstandard2.0/Microsoft.AspNetCore.WebSockets.xml", "microsoft.aspnetcore.websockets.2.3.0.nupkg.sha512", "microsoft.aspnetcore.websockets.nuspec"]}, "Microsoft.AspNetCore.WebUtilities/2.3.0": {"sha512": "trbXdWzoAEUVd0PE2yTopkz4kjZaAIA7xUWekd5uBw+7xE8Do/YOVTeb9d9koPTlbtZT539aESJjSLSqD8eYrQ==", "type": "package", "path": "microsoft.aspnetcore.webutilities/2.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.WebUtilities.dll", "lib/netstandard2.0/Microsoft.AspNetCore.WebUtilities.xml", "microsoft.aspnetcore.webutilities.2.3.0.nupkg.sha512", "microsoft.aspnetcore.webutilities.nuspec"]}, "Microsoft.Bcl.AsyncInterfaces/1.1.1": {"sha512": "yuvf07qFWFqtK3P/MRkEKLhn5r2UbSpVueRziSqj0yJQIKFwG1pq9mOayK3zE5qZCTs0CbrwL9M6R8VwqyGy2w==", "type": "package", "path": "microsoft.bcl.asyncinterfaces/1.1.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Bcl.AsyncInterfaces.dll", "lib/net461/Microsoft.Bcl.AsyncInterfaces.xml", "lib/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.dll", "lib/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.xml", "lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll", "lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.xml", "microsoft.bcl.asyncinterfaces.1.1.1.nupkg.sha512", "microsoft.bcl.asyncinterfaces.nuspec", "ref/net461/Microsoft.Bcl.AsyncInterfaces.dll", "ref/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.dll", "ref/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.CodeAnalysis.Analyzers/3.3.2": {"sha512": "7xt6zTlIEizUgEsYAIgm37EbdkiMmr6fP6J9pDoKEpiGM4pi32BCPGr/IczmSJI9Zzp0a6HOzpr9OvpMP+2veA==", "type": "package", "path": "microsoft.codeanalysis.analyzers/3.3.2", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "EULA.rtf", "ThirdPartyNotices.rtf", "analyzers/dotnet/cs/Microsoft.CodeAnalysis.Analyzers.dll", "analyzers/dotnet/cs/Microsoft.CodeAnalysis.CSharp.Analyzers.dll", "analyzers/dotnet/cs/cs/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/de/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/es/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/fr/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/it/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/ja/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/ko/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/pl/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/pt-BR/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/ru/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/tr/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/zh-<PERSON>/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/zh-Hant/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/Microsoft.CodeAnalysis.Analyzers.dll", "analyzers/dotnet/vb/Microsoft.CodeAnalysis.VisualBasic.Analyzers.dll", "analyzers/dotnet/vb/cs/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/de/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/es/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/fr/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/it/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/ja/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/ko/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/pl/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/pt-BR/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/ru/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/tr/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/zh-<PERSON>/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/zh-Hant/Microsoft.CodeAnalysis.Analyzers.resources.dll", "build/Microsoft.CodeAnalysis.Analyzers.props", "build/Microsoft.CodeAnalysis.Analyzers.targets", "build/config/AnalysisLevel_2_9_8_AllDisabledByDefault.editorconfig", "build/config/AnalysisLevel_2_9_8_AllEnabledByDefault.editorconfig", "build/config/AnalysisLevel_2_9_8_Default.editorconfig", "build/config/AnalysisLevel_3_3_AllDisabledByDefault.editorconfig", "build/config/AnalysisLevel_3_3_AllEnabledByDefault.editorconfig", "build/config/AnalysisLevel_3_3_Default.editorconfig", "build/config/AnalysisLevel_3_AllDisabledByDefault.editorconfig", "build/config/AnalysisLevel_3_AllEnabledByDefault.editorconfig", "build/config/AnalysisLevel_3_Default.editorconfig", "documentation/Analyzer Configuration.md", "documentation/Microsoft.CodeAnalysis.Analyzers.md", "documentation/Microsoft.CodeAnalysis.Analyzers.sarif", "editorconfig/AllRulesDefault/.editorconfig", "editorconfig/AllRulesDisabled/.editorconfig", "editorconfig/AllRulesEnabled/.editorconfig", "editorconfig/CorrectnessRulesDefault/.editorconfig", "editorconfig/CorrectnessRulesEnabled/.editorconfig", "editorconfig/DataflowRulesDefault/.editorconfig", "editorconfig/DataflowRulesEnabled/.editorconfig", "editorconfig/LibraryRulesDefault/.editorconfig", "editorconfig/LibraryRulesEnabled/.editorconfig", "editorconfig/MicrosoftCodeAnalysisCompatibilityRulesDefault/.editorconfig", "editorconfig/MicrosoftCodeAnalysisCompatibilityRulesEnabled/.editorconfig", "editorconfig/MicrosoftCodeAnalysisCorrectnessRulesDefault/.editorconfig", "editorconfig/MicrosoftCodeAnalysisCorrectnessRulesEnabled/.editorconfig", "editorconfig/MicrosoftCodeAnalysisDesignRulesDefault/.editorconfig", "editorconfig/MicrosoftCodeAnalysisDesignRulesEnabled/.editorconfig", "editorconfig/MicrosoftCodeAnalysisDocumentationRulesDefault/.editorconfig", "editorconfig/MicrosoftCodeAnalysisDocumentationRulesEnabled/.editorconfig", "editorconfig/MicrosoftCodeAnalysisLocalizationRulesDefault/.editorconfig", "editorconfig/MicrosoftCodeAnalysisLocalizationRulesEnabled/.editorconfig", "editorconfig/MicrosoftCodeAnalysisPerformanceRulesDefault/.editorconfig", "editorconfig/MicrosoftCodeAnalysisPerformanceRulesEnabled/.editorconfig", "editorconfig/MicrosoftCodeAnalysisReleaseTrackingRulesDefault/.editorconfig", "editorconfig/MicrosoftCodeAnalysisReleaseTrackingRulesEnabled/.editorconfig", "editorconfig/PortedFromFxCopRulesDefault/.editorconfig", "editorconfig/PortedFromFxCopRulesEnabled/.editorconfig", "microsoft.codeanalysis.analyzers.3.3.2.nupkg.sha512", "microsoft.codeanalysis.analyzers.nuspec", "rulesets/AllRulesDefault.ruleset", "rulesets/AllRulesDisabled.ruleset", "rulesets/AllRulesEnabled.ruleset", "rulesets/CorrectnessRulesDefault.ruleset", "rulesets/CorrectnessRulesEnabled.ruleset", "rulesets/DataflowRulesDefault.ruleset", "rulesets/DataflowRulesEnabled.ruleset", "rulesets/LibraryRulesDefault.ruleset", "rulesets/LibraryRulesEnabled.ruleset", "rulesets/MicrosoftCodeAnalysisCompatibilityRulesDefault.ruleset", "rulesets/MicrosoftCodeAnalysisCompatibilityRulesEnabled.ruleset", "rulesets/MicrosoftCodeAnalysisCorrectnessRulesDefault.ruleset", "rulesets/MicrosoftCodeAnalysisCorrectnessRulesEnabled.ruleset", "rulesets/MicrosoftCodeAnalysisDesignRulesDefault.ruleset", "rulesets/MicrosoftCodeAnalysisDesignRulesEnabled.ruleset", "rulesets/MicrosoftCodeAnalysisDocumentationRulesDefault.ruleset", "rulesets/MicrosoftCodeAnalysisDocumentationRulesEnabled.ruleset", "rulesets/MicrosoftCodeAnalysisLocalizationRulesDefault.ruleset", "rulesets/MicrosoftCodeAnalysisLocalizationRulesEnabled.ruleset", "rulesets/MicrosoftCodeAnalysisPerformanceRulesDefault.ruleset", "rulesets/MicrosoftCodeAnalysisPerformanceRulesEnabled.ruleset", "rulesets/MicrosoftCodeAnalysisReleaseTrackingRulesDefault.ruleset", "rulesets/MicrosoftCodeAnalysisReleaseTrackingRulesEnabled.ruleset", "rulesets/PortedFromFxCopRulesDefault.ruleset", "rulesets/PortedFromFxCopRulesEnabled.ruleset", "tools/install.ps1", "tools/uninstall.ps1"]}, "Microsoft.CodeAnalysis.Common/4.0.0": {"sha512": "d02ybMhUJl1r/dI6SkJPHrTiTzXBYCZeJdOLMckV+jyoMU/GGkjqFX/sRbv1K0QmlpwwKuLTiYVQvfYC+8ox2g==", "type": "package", "path": "microsoft.codeanalysis.common/4.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "ThirdPartyNotices.rtf", "lib/netcoreapp3.1/Microsoft.CodeAnalysis.dll", "lib/netcoreapp3.1/Microsoft.CodeAnalysis.pdb", "lib/netcoreapp3.1/Microsoft.CodeAnalysis.xml", "lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.resources.dll", "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.resources.dll", "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.resources.dll", "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.resources.dll", "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.resources.dll", "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.resources.dll", "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.resources.dll", "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.resources.dll", "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.resources.dll", "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.resources.dll", "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.resources.dll", "lib/netcoreapp3.1/zh-Hans/Microsoft.CodeAnalysis.resources.dll", "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/Microsoft.CodeAnalysis.dll", "lib/netstandard2.0/Microsoft.CodeAnalysis.pdb", "lib/netstandard2.0/Microsoft.CodeAnalysis.xml", "lib/netstandard2.0/cs/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/de/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/es/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/fr/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/it/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/ja/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/ko/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/pl/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/pt-BR/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/ru/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/tr/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/zh-<PERSON>/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/zh-Hant/Microsoft.CodeAnalysis.resources.dll", "microsoft.codeanalysis.common.4.0.0.nupkg.sha512", "microsoft.codeanalysis.common.nuspec"]}, "Microsoft.CodeAnalysis.CSharp/4.0.0": {"sha512": "2UVTGtyQGgTCazvnT6t82f+7AV2L+kqJdyb61rT9GQed4yK+tVh5IkaKcsm70VqyZQhBbDqsfZFNHnY65xhrRw==", "type": "package", "path": "microsoft.codeanalysis.csharp/4.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "ThirdPartyNotices.rtf", "lib/netcoreapp3.1/Microsoft.CodeAnalysis.CSharp.dll", "lib/netcoreapp3.1/Microsoft.CodeAnalysis.CSharp.pdb", "lib/netcoreapp3.1/Microsoft.CodeAnalysis.CSharp.xml", "lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netcoreapp3.1/zh-<PERSON>/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/Microsoft.CodeAnalysis.CSharp.dll", "lib/netstandard2.0/Microsoft.CodeAnalysis.CSharp.pdb", "lib/netstandard2.0/Microsoft.CodeAnalysis.CSharp.xml", "lib/netstandard2.0/cs/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/de/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/es/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/fr/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/it/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/ja/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/ko/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/pl/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/pt-BR/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/ru/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/tr/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/zh-<PERSON>/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/zh-Hant/Microsoft.CodeAnalysis.CSharp.resources.dll", "microsoft.codeanalysis.csharp.4.0.0.nupkg.sha512", "microsoft.codeanalysis.csharp.nuspec"]}, "Microsoft.CodeAnalysis.Razor/6.0.0": {"sha512": "uqdzuQXxD7XrJCbIbbwpI/LOv0PBJ9VIR0gdvANTHOfK5pjTaCir+XcwvYvBZ5BIzd0KGzyiamzlEWw1cK1q0w==", "type": "package", "path": "microsoft.codeanalysis.razor/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/netstandard2.0/Microsoft.CodeAnalysis.Razor.dll", "microsoft.codeanalysis.razor.6.0.0.nupkg.sha512", "microsoft.codeanalysis.razor.nuspec"]}, "Microsoft.CSharp/4.5.0": {"sha512": "kaj6Wb4qoMuH3HySFJhxwQfe8R/sJsNJnANrvv8WdFPMoNbKY5htfNscv+LHCu5ipz+49m2e+WQXpLXr9XYemQ==", "type": "package", "path": "microsoft.csharp/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/Microsoft.CSharp.dll", "lib/netcoreapp2.0/_._", "lib/netstandard1.3/Microsoft.CSharp.dll", "lib/netstandard2.0/Microsoft.CSharp.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/uap10.0.16299/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "microsoft.csharp.4.5.0.nupkg.sha512", "microsoft.csharp.nuspec", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/Microsoft.CSharp.dll", "ref/netcore50/Microsoft.CSharp.xml", "ref/netcore50/de/Microsoft.CSharp.xml", "ref/netcore50/es/Microsoft.CSharp.xml", "ref/netcore50/fr/Microsoft.CSharp.xml", "ref/netcore50/it/Microsoft.CSharp.xml", "ref/netcore50/ja/Microsoft.CSharp.xml", "ref/netcore50/ko/Microsoft.CSharp.xml", "ref/netcore50/ru/Microsoft.CSharp.xml", "ref/netcore50/zh-hans/Microsoft.CSharp.xml", "ref/netcore50/zh-hant/Microsoft.CSharp.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.0/Microsoft.CSharp.dll", "ref/netstandard1.0/Microsoft.CSharp.xml", "ref/netstandard1.0/de/Microsoft.CSharp.xml", "ref/netstandard1.0/es/Microsoft.CSharp.xml", "ref/netstandard1.0/fr/Microsoft.CSharp.xml", "ref/netstandard1.0/it/Microsoft.CSharp.xml", "ref/netstandard1.0/ja/Microsoft.CSharp.xml", "ref/netstandard1.0/ko/Microsoft.CSharp.xml", "ref/netstandard1.0/ru/Microsoft.CSharp.xml", "ref/netstandard1.0/zh-hans/Microsoft.CSharp.xml", "ref/netstandard1.0/zh-hant/Microsoft.CSharp.xml", "ref/netstandard2.0/Microsoft.CSharp.dll", "ref/netstandard2.0/Microsoft.CSharp.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/uap10.0.16299/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Data.SqlClient/5.2.2": {"sha512": "mtoeRMh7F/OA536c/Cnh8L4H0uLSKB5kSmoi54oN7Fp0hNJDy22IqyMhaMH4PkDCqI7xL//Fvg9ldtuPHG0h5g==", "type": "package", "path": "microsoft.data.sqlclient/5.2.2", "files": [".nupkg.metadata", ".signature.p7s", "dotnet.png", "lib/net462/Microsoft.Data.SqlClient.dll", "lib/net462/Microsoft.Data.SqlClient.xml", "lib/net462/de/Microsoft.Data.SqlClient.resources.dll", "lib/net462/es/Microsoft.Data.SqlClient.resources.dll", "lib/net462/fr/Microsoft.Data.SqlClient.resources.dll", "lib/net462/it/Microsoft.Data.SqlClient.resources.dll", "lib/net462/ja/Microsoft.Data.SqlClient.resources.dll", "lib/net462/ko/Microsoft.Data.SqlClient.resources.dll", "lib/net462/pt-BR/Microsoft.Data.SqlClient.resources.dll", "lib/net462/ru/Microsoft.Data.SqlClient.resources.dll", "lib/net462/zh-<PERSON>/Microsoft.Data.SqlClient.resources.dll", "lib/net462/zh-Hant/Microsoft.Data.SqlClient.resources.dll", "lib/net6.0/Microsoft.Data.SqlClient.dll", "lib/net6.0/Microsoft.Data.SqlClient.xml", "lib/net6.0/de/Microsoft.Data.SqlClient.resources.dll", "lib/net6.0/es/Microsoft.Data.SqlClient.resources.dll", "lib/net6.0/fr/Microsoft.Data.SqlClient.resources.dll", "lib/net6.0/it/Microsoft.Data.SqlClient.resources.dll", "lib/net6.0/ja/Microsoft.Data.SqlClient.resources.dll", "lib/net6.0/ko/Microsoft.Data.SqlClient.resources.dll", "lib/net6.0/pt-BR/Microsoft.Data.SqlClient.resources.dll", "lib/net6.0/ru/Microsoft.Data.SqlClient.resources.dll", "lib/net6.0/zh-Hans/Microsoft.Data.SqlClient.resources.dll", "lib/net6.0/zh-Hant/Microsoft.Data.SqlClient.resources.dll", "lib/net8.0/Microsoft.Data.SqlClient.dll", "lib/net8.0/Microsoft.Data.SqlClient.xml", "lib/net8.0/de/Microsoft.Data.SqlClient.resources.dll", "lib/net8.0/es/Microsoft.Data.SqlClient.resources.dll", "lib/net8.0/fr/Microsoft.Data.SqlClient.resources.dll", "lib/net8.0/it/Microsoft.Data.SqlClient.resources.dll", "lib/net8.0/ja/Microsoft.Data.SqlClient.resources.dll", "lib/net8.0/ko/Microsoft.Data.SqlClient.resources.dll", "lib/net8.0/pt-BR/Microsoft.Data.SqlClient.resources.dll", "lib/net8.0/ru/Microsoft.Data.SqlClient.resources.dll", "lib/net8.0/zh-Hans/Microsoft.Data.SqlClient.resources.dll", "lib/net8.0/zh-Hant/Microsoft.Data.SqlClient.resources.dll", "lib/netstandard2.0/Microsoft.Data.SqlClient.dll", "lib/netstandard2.0/Microsoft.Data.SqlClient.xml", "lib/netstandard2.0/de/Microsoft.Data.SqlClient.resources.dll", "lib/netstandard2.0/es/Microsoft.Data.SqlClient.resources.dll", "lib/netstandard2.0/fr/Microsoft.Data.SqlClient.resources.dll", "lib/netstandard2.0/it/Microsoft.Data.SqlClient.resources.dll", "lib/netstandard2.0/ja/Microsoft.Data.SqlClient.resources.dll", "lib/netstandard2.0/ko/Microsoft.Data.SqlClient.resources.dll", "lib/netstandard2.0/pt-BR/Microsoft.Data.SqlClient.resources.dll", "lib/netstandard2.0/ru/Microsoft.Data.SqlClient.resources.dll", "lib/netstandard2.0/zh-Hans/Microsoft.Data.SqlClient.resources.dll", "lib/netstandard2.0/zh-Hant/Microsoft.Data.SqlClient.resources.dll", "lib/netstandard2.1/Microsoft.Data.SqlClient.dll", "lib/netstandard2.1/Microsoft.Data.SqlClient.xml", "lib/netstandard2.1/de/Microsoft.Data.SqlClient.resources.dll", "lib/netstandard2.1/es/Microsoft.Data.SqlClient.resources.dll", "lib/netstandard2.1/fr/Microsoft.Data.SqlClient.resources.dll", "lib/netstandard2.1/it/Microsoft.Data.SqlClient.resources.dll", "lib/netstandard2.1/ja/Microsoft.Data.SqlClient.resources.dll", "lib/netstandard2.1/ko/Microsoft.Data.SqlClient.resources.dll", "lib/netstandard2.1/pt-BR/Microsoft.Data.SqlClient.resources.dll", "lib/netstandard2.1/ru/Microsoft.Data.SqlClient.resources.dll", "lib/netstandard2.1/zh-<PERSON>/Microsoft.Data.SqlClient.resources.dll", "lib/netstandard2.1/zh-Hant/Microsoft.Data.SqlClient.resources.dll", "microsoft.data.sqlclient.5.2.2.nupkg.sha512", "microsoft.data.sqlclient.nuspec", "ref/net462/Microsoft.Data.SqlClient.dll", "ref/net462/Microsoft.Data.SqlClient.xml", "ref/net6.0/Microsoft.Data.SqlClient.dll", "ref/net6.0/Microsoft.Data.SqlClient.xml", "ref/net8.0/Microsoft.Data.SqlClient.dll", "ref/net8.0/Microsoft.Data.SqlClient.xml", "ref/netstandard2.0/Microsoft.Data.SqlClient.dll", "ref/netstandard2.0/Microsoft.Data.SqlClient.xml", "ref/netstandard2.1/Microsoft.Data.SqlClient.dll", "ref/netstandard2.1/Microsoft.Data.SqlClient.xml", "runtimes/unix/lib/net6.0/Microsoft.Data.SqlClient.dll", "runtimes/unix/lib/net8.0/Microsoft.Data.SqlClient.dll", "runtimes/unix/lib/netstandard2.0/Microsoft.Data.SqlClient.dll", "runtimes/unix/lib/netstandard2.1/Microsoft.Data.SqlClient.dll", "runtimes/win/lib/net462/Microsoft.Data.SqlClient.dll", "runtimes/win/lib/net6.0/Microsoft.Data.SqlClient.dll", "runtimes/win/lib/net8.0/Microsoft.Data.SqlClient.dll", "runtimes/win/lib/netstandard2.0/Microsoft.Data.SqlClient.dll", "runtimes/win/lib/netstandard2.1/Microsoft.Data.SqlClient.dll"]}, "Microsoft.Data.SqlClient.SNI.runtime/5.2.0": {"sha512": "po1jhvFd+8pbfvJR/puh+fkHi0GRanAdvayh/0e47yaM6CXWZ6opUjCMFuYlAnD2LcbyvQE7fPJKvogmaUcN+w==", "type": "package", "path": "microsoft.data.sqlclient.sni.runtime/5.2.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "dotnet.png", "microsoft.data.sqlclient.sni.runtime.5.2.0.nupkg.sha512", "microsoft.data.sqlclient.sni.runtime.nuspec", "runtimes/win-arm/native/Microsoft.Data.SqlClient.SNI.dll", "runtimes/win-arm64/native/Microsoft.Data.SqlClient.SNI.dll", "runtimes/win-x64/native/Microsoft.Data.SqlClient.SNI.dll", "runtimes/win-x86/native/Microsoft.Data.SqlClient.SNI.dll"]}, "Microsoft.EntityFrameworkCore/9.0.5": {"sha512": "TeCtb/vc+jxvgkVAqeJlZKOoG5w/w8AigWQQyOmeJsJ7+0SkONX8bqEV/wB+ojnT0sXuJrrfXQOEC3ws6asEng==", "type": "package", "path": "microsoft.entityframeworkcore/9.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "buildTransitive/net8.0/Microsoft.EntityFrameworkCore.props", "lib/net8.0/Microsoft.EntityFrameworkCore.dll", "lib/net8.0/Microsoft.EntityFrameworkCore.xml", "microsoft.entityframeworkcore.9.0.5.nupkg.sha512", "microsoft.entityframeworkcore.nuspec"]}, "Microsoft.EntityFrameworkCore.Abstractions/9.0.5": {"sha512": "81fGyIibhGc4rq4ZxmVZE/1CFSvGMQOZqdRyCBLKz/Hb8eE973dmSfcdXpXhQ/5f+nbax4VGkWhwPGxWUNWaCQ==", "type": "package", "path": "microsoft.entityframeworkcore.abstractions/9.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.dll", "lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.xml", "microsoft.entityframeworkcore.abstractions.9.0.5.nupkg.sha512", "microsoft.entityframeworkcore.abstractions.nuspec"]}, "Microsoft.EntityFrameworkCore.Analyzers/9.0.5": {"sha512": "kWRrD69qCXo7lahPZPt7C127UfK0I024laFZEDMfT3JbALB1EWneFvq1utWM0cNKPFuYis1E1oaYTuRGI/9inQ==", "type": "package", "path": "microsoft.entityframeworkcore.analyzers/9.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "analyzers/dotnet/cs/Microsoft.EntityFrameworkCore.Analyzers.dll", "docs/PACKAGE.md", "microsoft.entityframeworkcore.analyzers.9.0.5.nupkg.sha512", "microsoft.entityframeworkcore.analyzers.nuspec"]}, "Microsoft.EntityFrameworkCore.Relational/9.0.5": {"sha512": "6eErbrZFd9yNnncemtDdmHZ3KC792OQCIYITuMsjK2oh4CLzlYo8mzNsozgUzQ+utHnne11/3eV8zMWbYF5Puw==", "type": "package", "path": "microsoft.entityframeworkcore.relational/9.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "lib/net8.0/Microsoft.EntityFrameworkCore.Relational.dll", "lib/net8.0/Microsoft.EntityFrameworkCore.Relational.xml", "microsoft.entityframeworkcore.relational.9.0.5.nupkg.sha512", "microsoft.entityframeworkcore.relational.nuspec"]}, "Microsoft.EntityFrameworkCore.SqlServer/9.0.5": {"sha512": "Y4194uyqwMivN2ioKd7GYBFVeeG2kZFFC1ZCmOTvXy3G6Wd05ZVyUyR/3mB+SHCequMPt/DI4f58WMmVaOS6eg==", "type": "package", "path": "microsoft.entityframeworkcore.sqlserver/9.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "lib/net8.0/Microsoft.EntityFrameworkCore.SqlServer.dll", "lib/net8.0/Microsoft.EntityFrameworkCore.SqlServer.xml", "microsoft.entityframeworkcore.sqlserver.9.0.5.nupkg.sha512", "microsoft.entityframeworkcore.sqlserver.nuspec"]}, "Microsoft.Extensions.Caching.Abstractions/9.0.5": {"sha512": "RV6wOTvH5BeVRs6cvxFuaV1ut05Dklpvq19XRO1JxAayfLWYIEP7K94aamY0iSUhoehWk1X5H6gMcbZkHuBjew==", "type": "package", "path": "microsoft.extensions.caching.abstractions/9.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Caching.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Caching.Abstractions.targets", "lib/net462/Microsoft.Extensions.Caching.Abstractions.dll", "lib/net462/Microsoft.Extensions.Caching.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Caching.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Caching.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.Caching.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.Caching.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Caching.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Caching.Abstractions.xml", "microsoft.extensions.caching.abstractions.9.0.5.nupkg.sha512", "microsoft.extensions.caching.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Caching.Memory/9.0.5": {"sha512": "qDmoAzIUBup5KZG1Abv51ifbHMCWFnaXbt05l+Sd92mLOpF9OwHOuoxu3XhzXaPGfq0Ns3pv1df5l8zuKjFgGw==", "type": "package", "path": "microsoft.extensions.caching.memory/9.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Caching.Memory.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Caching.Memory.targets", "lib/net462/Microsoft.Extensions.Caching.Memory.dll", "lib/net462/Microsoft.Extensions.Caching.Memory.xml", "lib/net8.0/Microsoft.Extensions.Caching.Memory.dll", "lib/net8.0/Microsoft.Extensions.Caching.Memory.xml", "lib/net9.0/Microsoft.Extensions.Caching.Memory.dll", "lib/net9.0/Microsoft.Extensions.Caching.Memory.xml", "lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.dll", "lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.xml", "microsoft.extensions.caching.memory.9.0.5.nupkg.sha512", "microsoft.extensions.caching.memory.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration/9.0.4": {"sha512": "KIVBrMbItnCJDd1RF4KEaE8jZwDJcDUJW5zXpbwQ05HNYTK1GveHxHK0B3SjgDJuR48GRACXAO+BLhL8h34S7g==", "type": "package", "path": "microsoft.extensions.configuration/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.targets", "lib/net462/Microsoft.Extensions.Configuration.dll", "lib/net462/Microsoft.Extensions.Configuration.xml", "lib/net8.0/Microsoft.Extensions.Configuration.dll", "lib/net8.0/Microsoft.Extensions.Configuration.xml", "lib/net9.0/Microsoft.Extensions.Configuration.dll", "lib/net9.0/Microsoft.Extensions.Configuration.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.xml", "microsoft.extensions.configuration.9.0.4.nupkg.sha512", "microsoft.extensions.configuration.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.Abstractions/9.0.5": {"sha512": "ew0G6gIznnyAkbIa67wXspkDFcVektjN3xaDAfBDIPbWph+rbuGaaohFxUSGw28ht7wdcWtTtElKnzfkcDDbOQ==", "type": "package", "path": "microsoft.extensions.configuration.abstractions/9.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.Abstractions.targets", "lib/net462/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net462/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.xml", "microsoft.extensions.configuration.abstractions.9.0.5.nupkg.sha512", "microsoft.extensions.configuration.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.Binder/9.0.4": {"sha512": "cdrjcl9RIcwt3ECbnpP0Gt1+pkjdW90mq5yFYy8D9qRj2NqFFcv3yDp141iEamsd9E218sGxK8WHaIOcrqgDJg==", "type": "package", "path": "microsoft.extensions.configuration.binder/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/cs/Microsoft.Extensions.Configuration.Binder.SourceGeneration.dll", "analyzers/dotnet/cs/cs/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/de/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/es/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/fr/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/it/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/ja/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/ko/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/pl/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/pt-BR/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/ru/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/tr/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/zh-<PERSON>/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/zh-Hant/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "buildTransitive/netstandard2.0/Microsoft.Extensions.Configuration.Binder.targets", "lib/net462/Microsoft.Extensions.Configuration.Binder.dll", "lib/net462/Microsoft.Extensions.Configuration.Binder.xml", "lib/net8.0/Microsoft.Extensions.Configuration.Binder.dll", "lib/net8.0/Microsoft.Extensions.Configuration.Binder.xml", "lib/net9.0/Microsoft.Extensions.Configuration.Binder.dll", "lib/net9.0/Microsoft.Extensions.Configuration.Binder.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.xml", "microsoft.extensions.configuration.binder.9.0.4.nupkg.sha512", "microsoft.extensions.configuration.binder.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyInjection/9.0.5": {"sha512": "N1Mn0T/tUBPoLL+Fzsp+VCEtneUhhxc1//Dx3BeuQ8AX+XrMlYCfnp2zgpEXnTCB7053CLdiqVWPZ7mEX6MPjg==", "type": "package", "path": "microsoft.extensions.dependencyinjection/9.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyInjection.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.targets", "lib/net462/Microsoft.Extensions.DependencyInjection.dll", "lib/net462/Microsoft.Extensions.DependencyInjection.xml", "lib/net8.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net8.0/Microsoft.Extensions.DependencyInjection.xml", "lib/net9.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net9.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.xml", "microsoft.extensions.dependencyinjection.9.0.5.nupkg.sha512", "microsoft.extensions.dependencyinjection.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.5": {"sha512": "cjnRtsEAzU73aN6W7vkWy8Phj5t3Xm78HSqgrbh/O4Q9SK/yN73wZVa21QQY6amSLQRQ/M8N+koGnY6PuvKQsw==", "type": "package", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "microsoft.extensions.dependencyinjection.abstractions.9.0.5.nupkg.sha512", "microsoft.extensions.dependencyinjection.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyModel/9.0.4": {"sha512": "ACtnvl3H3M/f8Z42980JxsNu7V9PPbzys4vBs83ZewnsgKd7JeYK18OMPo0g+MxAHrpgMrjmlinXDiaSRPcVnA==", "type": "package", "path": "microsoft.extensions.dependencymodel/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyModel.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyModel.targets", "lib/net462/Microsoft.Extensions.DependencyModel.dll", "lib/net462/Microsoft.Extensions.DependencyModel.xml", "lib/net8.0/Microsoft.Extensions.DependencyModel.dll", "lib/net8.0/Microsoft.Extensions.DependencyModel.xml", "lib/net9.0/Microsoft.Extensions.DependencyModel.dll", "lib/net9.0/Microsoft.Extensions.DependencyModel.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyModel.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyModel.xml", "microsoft.extensions.dependencymodel.9.0.4.nupkg.sha512", "microsoft.extensions.dependencymodel.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Diagnostics.Abstractions/8.0.1": {"sha512": "elH2vmwNmsXuKmUeMQ4YW9ldXiF+gSGDgg1vORksob5POnpaI6caj1Hu8zaYbEuibhqCoWg0YRWDazBY3zjBfg==", "type": "package", "path": "microsoft.extensions.diagnostics.abstractions/8.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Diagnostics.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Diagnostics.Abstractions.targets", "lib/net462/Microsoft.Extensions.Diagnostics.Abstractions.dll", "lib/net462/Microsoft.Extensions.Diagnostics.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.Diagnostics.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.Diagnostics.Abstractions.xml", "lib/net7.0/Microsoft.Extensions.Diagnostics.Abstractions.dll", "lib/net7.0/Microsoft.Extensions.Diagnostics.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Diagnostics.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Diagnostics.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Diagnostics.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Diagnostics.Abstractions.xml", "microsoft.extensions.diagnostics.abstractions.8.0.1.nupkg.sha512", "microsoft.extensions.diagnostics.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.FileProviders.Abstractions/8.0.0": {"sha512": "ZbaMlhJlpisjuWbvXr4LdAst/1XxH3vZ6A0BsgTphZ2L4PGuxRLz7Jr/S7mkAAnOn78Vu0fKhEgNF5JO3zfjqQ==", "type": "package", "path": "microsoft.extensions.fileproviders.abstractions/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.FileProviders.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.FileProviders.Abstractions.targets", "lib/net462/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/net462/Microsoft.Extensions.FileProviders.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.FileProviders.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.xml", "microsoft.extensions.fileproviders.abstractions.8.0.0.nupkg.sha512", "microsoft.extensions.fileproviders.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.FileProviders.Composite/8.0.0": {"sha512": "ynMjdZ5B3Fd3A9GxJaNhIcTrjLY1bXDQltyVIMVOxbT0ssTOCpFYWc977bVBAocB62fYWu/RN6/1HLnX/HjVuQ==", "type": "package", "path": "microsoft.extensions.fileproviders.composite/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.FileProviders.Composite.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.FileProviders.Composite.targets", "lib/net462/Microsoft.Extensions.FileProviders.Composite.dll", "lib/net462/Microsoft.Extensions.FileProviders.Composite.xml", "lib/net6.0/Microsoft.Extensions.FileProviders.Composite.dll", "lib/net6.0/Microsoft.Extensions.FileProviders.Composite.xml", "lib/net7.0/Microsoft.Extensions.FileProviders.Composite.dll", "lib/net7.0/Microsoft.Extensions.FileProviders.Composite.xml", "lib/net8.0/Microsoft.Extensions.FileProviders.Composite.dll", "lib/net8.0/Microsoft.Extensions.FileProviders.Composite.xml", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Composite.dll", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Composite.xml", "microsoft.extensions.fileproviders.composite.8.0.0.nupkg.sha512", "microsoft.extensions.fileproviders.composite.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.FileSystemGlobbing/8.0.0": {"sha512": "OK+670i7esqlQrPjdIKRbsyMCe9g5kSLpRRQGSr4Q58AOYEe/hCnfLZprh7viNisSUUQZmMrbbuDaIrP+V1ebQ==", "type": "package", "path": "microsoft.extensions.filesystemglobbing/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.FileSystemGlobbing.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.FileSystemGlobbing.targets", "lib/net462/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/net462/Microsoft.Extensions.FileSystemGlobbing.xml", "lib/net6.0/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/net6.0/Microsoft.Extensions.FileSystemGlobbing.xml", "lib/net7.0/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/net7.0/Microsoft.Extensions.FileSystemGlobbing.xml", "lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.xml", "lib/netstandard2.0/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/netstandard2.0/Microsoft.Extensions.FileSystemGlobbing.xml", "microsoft.extensions.filesystemglobbing.8.0.0.nupkg.sha512", "microsoft.extensions.filesystemglobbing.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Hosting.Abstractions/8.0.1": {"sha512": "nHwq9aPBdBPYXPti6wYEEfgXddfBrYC+CQLn+qISiwQq5tpfaqDZSKOJNxoe9rfQxGf1c+2wC/qWFe1QYJPYqw==", "type": "package", "path": "microsoft.extensions.hosting.abstractions/8.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Hosting.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Hosting.Abstractions.targets", "lib/net462/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/net462/Microsoft.Extensions.Hosting.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.Hosting.Abstractions.xml", "lib/net7.0/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/net7.0/Microsoft.Extensions.Hosting.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Hosting.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Hosting.Abstractions.xml", "lib/netstandard2.1/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/netstandard2.1/Microsoft.Extensions.Hosting.Abstractions.xml", "microsoft.extensions.hosting.abstractions.8.0.1.nupkg.sha512", "microsoft.extensions.hosting.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Identity.Core/9.0.4": {"sha512": "KKfCsoIHFGZmmCEjZBPuvDW0pCjboMru/Z3vbEyC/OIwUVeKrdPugFyjc81i7rNSjcPcDxVvGl/Ks8HLelKocg==", "type": "package", "path": "microsoft.extensions.identity.core/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/net462/Microsoft.Extensions.Identity.Core.dll", "lib/net462/Microsoft.Extensions.Identity.Core.xml", "lib/net9.0/Microsoft.Extensions.Identity.Core.dll", "lib/net9.0/Microsoft.Extensions.Identity.Core.xml", "lib/netstandard2.0/Microsoft.Extensions.Identity.Core.dll", "lib/netstandard2.0/Microsoft.Extensions.Identity.Core.xml", "microsoft.extensions.identity.core.9.0.4.nupkg.sha512", "microsoft.extensions.identity.core.nuspec"]}, "Microsoft.Extensions.Identity.Stores/9.0.4": {"sha512": "0F6lSngwyXzrv+qtX46nhHYBOlPxEzj0qyCCef1kvlyEYhbj8kBL13FuDk4nEPkzk1yVjZgsnXBG19+TrNdakQ==", "type": "package", "path": "microsoft.extensions.identity.stores/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/net462/Microsoft.Extensions.Identity.Stores.dll", "lib/net462/Microsoft.Extensions.Identity.Stores.xml", "lib/net9.0/Microsoft.Extensions.Identity.Stores.dll", "lib/net9.0/Microsoft.Extensions.Identity.Stores.xml", "lib/netstandard2.0/Microsoft.Extensions.Identity.Stores.dll", "lib/netstandard2.0/Microsoft.Extensions.Identity.Stores.xml", "microsoft.extensions.identity.stores.9.0.4.nupkg.sha512", "microsoft.extensions.identity.stores.nuspec"]}, "Microsoft.Extensions.Localization/8.0.11": {"sha512": "yTb325+mCuoUBBMEztLv48kK/I/99TfVPh4V5kW8Qw+45mqAJs9bndOYxvjJvJxKVuwgzOYiWC/a34fr4pJ8IA==", "type": "package", "path": "microsoft.extensions.localization/8.0.11", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/net462/Microsoft.Extensions.Localization.dll", "lib/net462/Microsoft.Extensions.Localization.xml", "lib/net8.0/Microsoft.Extensions.Localization.dll", "lib/net8.0/Microsoft.Extensions.Localization.xml", "lib/netstandard2.0/Microsoft.Extensions.Localization.dll", "lib/netstandard2.0/Microsoft.Extensions.Localization.xml", "microsoft.extensions.localization.8.0.11.nupkg.sha512", "microsoft.extensions.localization.nuspec"]}, "Microsoft.Extensions.Localization.Abstractions/8.0.11": {"sha512": "qyfVEOJ8PsYrwC6kTQFSTm4+FrpOAz3OfPXvRPfs7j34V+yHMuVUgIiP5yjrYHcpNbXTtCzoQc/ZdcbpdeX/Xg==", "type": "package", "path": "microsoft.extensions.localization.abstractions/8.0.11", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/net462/Microsoft.Extensions.Localization.Abstractions.dll", "lib/net462/Microsoft.Extensions.Localization.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Localization.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Localization.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Localization.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Localization.Abstractions.xml", "microsoft.extensions.localization.abstractions.8.0.11.nupkg.sha512", "microsoft.extensions.localization.abstractions.nuspec"]}, "Microsoft.Extensions.Logging/9.0.5": {"sha512": "rQU61lrgvpE/UgcAd4E56HPxUIkX/VUQCxWmwDTLLVeuwRDYTL0q/FLGfAW17cGTKyCh7ywYAEnY3sTEvURsfg==", "type": "package", "path": "microsoft.extensions.logging/9.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Logging.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.targets", "lib/net462/Microsoft.Extensions.Logging.dll", "lib/net462/Microsoft.Extensions.Logging.xml", "lib/net8.0/Microsoft.Extensions.Logging.dll", "lib/net8.0/Microsoft.Extensions.Logging.xml", "lib/net9.0/Microsoft.Extensions.Logging.dll", "lib/net9.0/Microsoft.Extensions.Logging.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.xml", "lib/netstandard2.1/Microsoft.Extensions.Logging.dll", "lib/netstandard2.1/Microsoft.Extensions.Logging.xml", "microsoft.extensions.logging.9.0.5.nupkg.sha512", "microsoft.extensions.logging.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.Abstractions/9.0.5": {"sha512": "pP1PADCrIxMYJXxFmTVbAgEU7GVpjK5i0/tyfU9DiE0oXQy3JWQaOVgCkrCiePLgS8b5sghM3Fau3EeHiVWbCg==", "type": "package", "path": "microsoft.extensions.logging.abstractions/9.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn3.11/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn3.11/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn4.0/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn4.4/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "buildTransitive/net461/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/net462/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/net8.0/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.targets", "lib/net462/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net462/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.xml", "microsoft.extensions.logging.abstractions.9.0.5.nupkg.sha512", "microsoft.extensions.logging.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.ObjectPool/8.0.11": {"sha512": "6ApKcHNJigXBfZa6XlDQ8feJpq7SG1ogZXg6M4FiNzgd6irs3LUAzo0Pfn4F2ZI9liGnH1XIBR/OtSbZmJAV5w==", "type": "package", "path": "microsoft.extensions.objectpool/8.0.11", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/net462/Microsoft.Extensions.ObjectPool.dll", "lib/net462/Microsoft.Extensions.ObjectPool.xml", "lib/net8.0/Microsoft.Extensions.ObjectPool.dll", "lib/net8.0/Microsoft.Extensions.ObjectPool.xml", "lib/netstandard2.0/Microsoft.Extensions.ObjectPool.dll", "lib/netstandard2.0/Microsoft.Extensions.ObjectPool.xml", "microsoft.extensions.objectpool.8.0.11.nupkg.sha512", "microsoft.extensions.objectpool.nuspec"]}, "Microsoft.Extensions.Options/9.0.5": {"sha512": "vPdJQU8YLOUSSK8NL0RmwcXJr2E0w8xH559PGQl4JYsglgilZr9LZnqV2zdgk+XR05+kuvhBEZKoDVd46o7NqA==", "type": "package", "path": "microsoft.extensions.options/9.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn4.4/cs/Microsoft.Extensions.Options.SourceGeneration.dll", "analyzers/dotnet/roslyn4.4/cs/cs/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-<PERSON>/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "buildTransitive/net461/Microsoft.Extensions.Options.targets", "buildTransitive/net462/Microsoft.Extensions.Options.targets", "buildTransitive/net8.0/Microsoft.Extensions.Options.targets", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Options.targets", "buildTransitive/netstandard2.0/Microsoft.Extensions.Options.targets", "lib/net462/Microsoft.Extensions.Options.dll", "lib/net462/Microsoft.Extensions.Options.xml", "lib/net8.0/Microsoft.Extensions.Options.dll", "lib/net8.0/Microsoft.Extensions.Options.xml", "lib/net9.0/Microsoft.Extensions.Options.dll", "lib/net9.0/Microsoft.Extensions.Options.xml", "lib/netstandard2.0/Microsoft.Extensions.Options.dll", "lib/netstandard2.0/Microsoft.Extensions.Options.xml", "lib/netstandard2.1/Microsoft.Extensions.Options.dll", "lib/netstandard2.1/Microsoft.Extensions.Options.xml", "microsoft.extensions.options.9.0.5.nupkg.sha512", "microsoft.extensions.options.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Primitives/9.0.5": {"sha512": "b4OAv1qE1C9aM+ShWJu3rlo/WjDwa/I30aIPXqDWSKXTtKl1Wwh6BZn+glH5HndGVVn3C6ZAPQj5nv7/7HJNBQ==", "type": "package", "path": "microsoft.extensions.primitives/9.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Primitives.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Primitives.targets", "lib/net462/Microsoft.Extensions.Primitives.dll", "lib/net462/Microsoft.Extensions.Primitives.xml", "lib/net8.0/Microsoft.Extensions.Primitives.dll", "lib/net8.0/Microsoft.Extensions.Primitives.xml", "lib/net9.0/Microsoft.Extensions.Primitives.dll", "lib/net9.0/Microsoft.Extensions.Primitives.xml", "lib/netstandard2.0/Microsoft.Extensions.Primitives.dll", "lib/netstandard2.0/Microsoft.Extensions.Primitives.xml", "microsoft.extensions.primitives.9.0.5.nupkg.sha512", "microsoft.extensions.primitives.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.WebEncoders/8.0.11": {"sha512": "EwF+KaQzTa/MoIm8gciABL6xeeiGKowqyam+lPYWukTppwch1P3QeL8CpgtLs8kIWuEowpAAUrVfP1kyZsZgqg==", "type": "package", "path": "microsoft.extensions.webencoders/8.0.11", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/net462/Microsoft.Extensions.WebEncoders.dll", "lib/net462/Microsoft.Extensions.WebEncoders.xml", "lib/net8.0/Microsoft.Extensions.WebEncoders.dll", "lib/net8.0/Microsoft.Extensions.WebEncoders.xml", "lib/netstandard2.0/Microsoft.Extensions.WebEncoders.dll", "lib/netstandard2.0/Microsoft.Extensions.WebEncoders.xml", "microsoft.extensions.webencoders.8.0.11.nupkg.sha512", "microsoft.extensions.webencoders.nuspec"]}, "Microsoft.Identity.Client/4.61.3": {"sha512": "naJo/Qm35Caaoxp5utcw+R8eU8ZtLz2ALh8S+gkekOYQ1oazfCQMWVT4NJ/FnHzdIJlm8dMz0oMpMGCabx5odA==", "type": "package", "path": "microsoft.identity.client/4.61.3", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net462/Microsoft.Identity.Client.dll", "lib/net462/Microsoft.Identity.Client.xml", "lib/net6.0-android31.0/Microsoft.Identity.Client.dll", "lib/net6.0-android31.0/Microsoft.Identity.Client.xml", "lib/net6.0-ios15.4/Microsoft.Identity.Client.dll", "lib/net6.0-ios15.4/Microsoft.Identity.Client.xml", "lib/net6.0/Microsoft.Identity.Client.dll", "lib/net6.0/Microsoft.Identity.Client.xml", "lib/netstandard2.0/Microsoft.Identity.Client.dll", "lib/netstandard2.0/Microsoft.Identity.Client.xml", "microsoft.identity.client.4.61.3.nupkg.sha512", "microsoft.identity.client.nuspec"]}, "Microsoft.Identity.Client.Extensions.Msal/4.61.3": {"sha512": "PWnJcznrSGr25MN8ajlc2XIDW4zCFu0U6FkpaNLEWLgd1NgFCp5uDY3mqLDgM8zCN8hqj8yo5wHYfLB2HjcdGw==", "type": "package", "path": "microsoft.identity.client.extensions.msal/4.61.3", "files": [".nupkg.metadata", ".signature.p7s", "lib/net6.0/Microsoft.Identity.Client.Extensions.Msal.dll", "lib/net6.0/Microsoft.Identity.Client.Extensions.Msal.xml", "lib/netstandard2.0/Microsoft.Identity.Client.Extensions.Msal.dll", "lib/netstandard2.0/Microsoft.Identity.Client.Extensions.Msal.xml", "microsoft.identity.client.extensions.msal.4.61.3.nupkg.sha512", "microsoft.identity.client.extensions.msal.nuspec"]}, "Microsoft.IdentityModel.Abstractions/8.9.0": {"sha512": "b/87S+lb86U7Ns7xgTKnqql6XGNr8hBE+k0rj5sRWwXeJe6uA+3mSjvpZ9GoQo3cB9zlwzcbGBU8KM44qX0t1g==", "type": "package", "path": "microsoft.identitymodel.abstractions/8.9.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net462/Microsoft.IdentityModel.Abstractions.dll", "lib/net462/Microsoft.IdentityModel.Abstractions.xml", "lib/net472/Microsoft.IdentityModel.Abstractions.dll", "lib/net472/Microsoft.IdentityModel.Abstractions.xml", "lib/net6.0/Microsoft.IdentityModel.Abstractions.dll", "lib/net6.0/Microsoft.IdentityModel.Abstractions.xml", "lib/net8.0/Microsoft.IdentityModel.Abstractions.dll", "lib/net8.0/Microsoft.IdentityModel.Abstractions.xml", "lib/net9.0/Microsoft.IdentityModel.Abstractions.dll", "lib/net9.0/Microsoft.IdentityModel.Abstractions.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Abstractions.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Abstractions.xml", "microsoft.identitymodel.abstractions.8.9.0.nupkg.sha512", "microsoft.identitymodel.abstractions.nuspec"]}, "Microsoft.IdentityModel.JsonWebTokens/6.35.0": {"sha512": "9wxai3hKgZUb4/NjdRKfQd0QJvtXKDlvmGMYACbEC8DFaicMFCFhQFZq9ZET1kJLwZahf2lfY5Gtcpsx8zYzbg==", "type": "package", "path": "microsoft.identitymodel.jsonwebtokens/6.35.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net45/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/net461/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net461/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/net462/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net462/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/net472/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net472/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/net6.0/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net6.0/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/netstandard2.0/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/netstandard2.0/Microsoft.IdentityModel.JsonWebTokens.xml", "microsoft.identitymodel.jsonwebtokens.6.35.0.nupkg.sha512", "microsoft.identitymodel.jsonwebtokens.nuspec"]}, "Microsoft.IdentityModel.Logging/8.9.0": {"sha512": "rswvH4ZANbFsJYEn+PGEOj7nkkBRjnsb7LcYGAS16VUJpSeKULLeYSy/7SK6jLO1WTT12xqdeL4mj3dYT7GdoQ==", "type": "package", "path": "microsoft.identitymodel.logging/8.9.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net462/Microsoft.IdentityModel.Logging.dll", "lib/net462/Microsoft.IdentityModel.Logging.xml", "lib/net472/Microsoft.IdentityModel.Logging.dll", "lib/net472/Microsoft.IdentityModel.Logging.xml", "lib/net6.0/Microsoft.IdentityModel.Logging.dll", "lib/net6.0/Microsoft.IdentityModel.Logging.xml", "lib/net8.0/Microsoft.IdentityModel.Logging.dll", "lib/net8.0/Microsoft.IdentityModel.Logging.xml", "lib/net9.0/Microsoft.IdentityModel.Logging.dll", "lib/net9.0/Microsoft.IdentityModel.Logging.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Logging.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Logging.xml", "microsoft.identitymodel.logging.8.9.0.nupkg.sha512", "microsoft.identitymodel.logging.nuspec"]}, "Microsoft.IdentityModel.Protocols/8.9.0": {"sha512": "MXnHFlFzWiSuD4Sj0HHoY2fhQ+gzglMVnRT9zugIEua09ToyHrAyynj/7F3aU4Q3vAEwiA0780Vk3FkxhzGaUQ==", "type": "package", "path": "microsoft.identitymodel.protocols/8.9.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net462/Microsoft.IdentityModel.Protocols.dll", "lib/net462/Microsoft.IdentityModel.Protocols.xml", "lib/net472/Microsoft.IdentityModel.Protocols.dll", "lib/net472/Microsoft.IdentityModel.Protocols.xml", "lib/net6.0/Microsoft.IdentityModel.Protocols.dll", "lib/net6.0/Microsoft.IdentityModel.Protocols.xml", "lib/net8.0/Microsoft.IdentityModel.Protocols.dll", "lib/net8.0/Microsoft.IdentityModel.Protocols.xml", "lib/net9.0/Microsoft.IdentityModel.Protocols.dll", "lib/net9.0/Microsoft.IdentityModel.Protocols.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Protocols.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Protocols.xml", "microsoft.identitymodel.protocols.8.9.0.nupkg.sha512", "microsoft.identitymodel.protocols.nuspec"]}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/6.35.0": {"sha512": "LMtVqnECCCdSmyFoCOxIE5tXQqkOLrvGrL7OxHg41DIm1bpWtaCdGyVcTAfOQpJXvzND9zUKIN/lhngPkYR8vg==", "type": "package", "path": "microsoft.identitymodel.protocols.openidconnect/6.35.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/net45/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "lib/net461/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/net461/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "lib/net462/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/net462/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "lib/net472/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/net472/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "lib/net6.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/net6.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "microsoft.identitymodel.protocols.openidconnect.6.35.0.nupkg.sha512", "microsoft.identitymodel.protocols.openidconnect.nuspec"]}, "Microsoft.IdentityModel.Tokens/8.9.0": {"sha512": "qK6kW5qZvDj7E5RLWQ9gzJxQe5GUz7+7bXrLQQydSDF9hTf5Ip2qHuAQW3Fg9GND6jkjTr7IXAZFmBHadNQi4Q==", "type": "package", "path": "microsoft.identitymodel.tokens/8.9.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net462/Microsoft.IdentityModel.Tokens.dll", "lib/net462/Microsoft.IdentityModel.Tokens.xml", "lib/net472/Microsoft.IdentityModel.Tokens.dll", "lib/net472/Microsoft.IdentityModel.Tokens.xml", "lib/net6.0/Microsoft.IdentityModel.Tokens.dll", "lib/net6.0/Microsoft.IdentityModel.Tokens.xml", "lib/net8.0/Microsoft.IdentityModel.Tokens.dll", "lib/net8.0/Microsoft.IdentityModel.Tokens.xml", "lib/net9.0/Microsoft.IdentityModel.Tokens.dll", "lib/net9.0/Microsoft.IdentityModel.Tokens.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Tokens.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Tokens.xml", "microsoft.identitymodel.tokens.8.9.0.nupkg.sha512", "microsoft.identitymodel.tokens.nuspec"]}, "Microsoft.Net.Http.Headers/2.3.0": {"sha512": "/M0wVg6tJUOHutWD3BMOUVZAioJVXe0tCpFiovzv0T9T12TBf4MnaHP0efO8TCr1a6O9RZgQeZ9Gdark8L9XdA==", "type": "package", "path": "microsoft.net.http.headers/2.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.Net.Http.Headers.dll", "lib/netstandard2.0/Microsoft.Net.Http.Headers.xml", "microsoft.net.http.headers.2.3.0.nupkg.sha512", "microsoft.net.http.headers.nuspec"]}, "Microsoft.NETCore.Platforms/2.1.2": {"sha512": "mOJy3M0UN+LUG21dLGMxaWZEP6xYpQEpLuvuEQBaownaX4YuhH6NmNUlN9si+vNkAS6dwJ//N1O4DmLf2CikVg==", "type": "package", "path": "microsoft.netcore.platforms/2.1.2", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/netstandard1.0/_._", "microsoft.netcore.platforms.2.1.2.nupkg.sha512", "microsoft.netcore.platforms.nuspec", "runtime.json", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.NETCore.Targets/1.1.0": {"sha512": "aOZA3BWfz9RXjpzt0sRJJMjAscAUm3Hoa4UWAfceV9UTYxgwZ1lZt5nO2myFf+/jetYQo4uTP7zS8sJY67BBxg==", "type": "package", "path": "microsoft.netcore.targets/1.1.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netstandard1.0/_._", "microsoft.netcore.targets.1.1.0.nupkg.sha512", "microsoft.netcore.targets.nuspec", "runtime.json"]}, "Microsoft.SqlServer.Server/1.0.0": {"sha512": "N4KeF3cpcm1PUHym1RmakkzfkEv3GRMyofVv40uXsQhCQeglr2OHNcUk2WOG51AKpGO8ynGpo9M/kFXSzghwug==", "type": "package", "path": "microsoft.sqlserver.server/1.0.0", "files": [".nupkg.metadata", ".signature.p7s", "dotnet.png", "lib/net46/Microsoft.SqlServer.Server.dll", "lib/net46/Microsoft.SqlServer.Server.pdb", "lib/net46/Microsoft.SqlServer.Server.xml", "lib/netstandard2.0/Microsoft.SqlServer.Server.dll", "lib/netstandard2.0/Microsoft.SqlServer.Server.pdb", "lib/netstandard2.0/Microsoft.SqlServer.Server.xml", "microsoft.sqlserver.server.1.0.0.nupkg.sha512", "microsoft.sqlserver.server.nuspec"]}, "Microsoft.Win32.Registry/4.5.0": {"sha512": "+FWlwd//+Tt56316p00hVePBCouXyEzT86Jb3+AuRotTND0IYn0OO3obs1gnQEs/txEnt+rF2JBGLItTG+Be6A==", "type": "package", "path": "microsoft.win32.registry/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/Microsoft.Win32.Registry.dll", "lib/net461/Microsoft.Win32.Registry.dll", "lib/netstandard1.3/Microsoft.Win32.Registry.dll", "lib/netstandard2.0/Microsoft.Win32.Registry.dll", "microsoft.win32.registry.4.5.0.nupkg.sha512", "microsoft.win32.registry.nuspec", "ref/net46/Microsoft.Win32.Registry.dll", "ref/net461/Microsoft.Win32.Registry.dll", "ref/net461/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/Microsoft.Win32.Registry.dll", "ref/netstandard1.3/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/de/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/es/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/fr/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/it/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/ja/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/ko/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/ru/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/zh-hans/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/zh-hant/Microsoft.Win32.Registry.xml", "ref/netstandard2.0/Microsoft.Win32.Registry.dll", "ref/netstandard2.0/Microsoft.Win32.Registry.xml", "runtimes/unix/lib/netstandard2.0/Microsoft.Win32.Registry.dll", "runtimes/win/lib/net46/Microsoft.Win32.Registry.dll", "runtimes/win/lib/net461/Microsoft.Win32.Registry.dll", "runtimes/win/lib/netstandard1.3/Microsoft.Win32.Registry.dll", "runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.dll", "useSharedDesignerContext.txt", "version.txt"]}, "MySqlConnector/2.3.5": {"sha512": "AmEfUPkFl+Ev6jJ8Dhns3CYHBfD12RHzGYWuLt6DfG6/af6YvOMyPz74ZPPjBYQGRJkumD2Z48Kqm8s5DJuhLA==", "type": "package", "path": "mysqlconnector/2.3.5", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net462/MySqlConnector.dll", "lib/net462/MySqlConnector.xml", "lib/net471/MySqlConnector.dll", "lib/net471/MySqlConnector.xml", "lib/net48/MySqlConnector.dll", "lib/net48/MySqlConnector.xml", "lib/net6.0/MySqlConnector.dll", "lib/net6.0/MySqlConnector.xml", "lib/net7.0/MySqlConnector.dll", "lib/net7.0/MySqlConnector.xml", "lib/net8.0/MySqlConnector.dll", "lib/net8.0/MySqlConnector.xml", "lib/netstandard2.0/MySqlConnector.dll", "lib/netstandard2.0/MySqlConnector.xml", "lib/netstandard2.1/MySqlConnector.dll", "lib/netstandard2.1/MySqlConnector.xml", "logo.png", "mysqlconnector.2.3.5.nupkg.sha512", "mysqlconnector.nuspec"]}, "Newtonsoft.Json/13.0.3": {"sha512": "HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "type": "package", "path": "newtonsoft.json/13.0.3", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.md", "README.md", "lib/net20/Newtonsoft.Json.dll", "lib/net20/Newtonsoft.Json.xml", "lib/net35/Newtonsoft.Json.dll", "lib/net35/Newtonsoft.Json.xml", "lib/net40/Newtonsoft.Json.dll", "lib/net40/Newtonsoft.Json.xml", "lib/net45/Newtonsoft.Json.dll", "lib/net45/Newtonsoft.Json.xml", "lib/net6.0/Newtonsoft.Json.dll", "lib/net6.0/Newtonsoft.Json.xml", "lib/netstandard1.0/Newtonsoft.Json.dll", "lib/netstandard1.0/Newtonsoft.Json.xml", "lib/netstandard1.3/Newtonsoft.Json.dll", "lib/netstandard1.3/Newtonsoft.Json.xml", "lib/netstandard2.0/Newtonsoft.Json.dll", "lib/netstandard2.0/Newtonsoft.Json.xml", "newtonsoft.json.13.0.3.nupkg.sha512", "newtonsoft.json.nuspec", "packageIcon.png"]}, "Newtonsoft.Json.Bson/1.0.2": {"sha512": "QYFyxhaABwmq3p/21VrZNYvCg3DaEoN/wUuw5nmfAf0X3HLjgupwhkEWdgfb9nvGAUIv3osmZoD3kKl4jxEmYQ==", "type": "package", "path": "newtonsoft.json.bson/1.0.2", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.md", "lib/net45/Newtonsoft.Json.Bson.dll", "lib/net45/Newtonsoft.Json.Bson.pdb", "lib/net45/Newtonsoft.Json.Bson.xml", "lib/netstandard1.3/Newtonsoft.Json.Bson.dll", "lib/netstandard1.3/Newtonsoft.Json.Bson.pdb", "lib/netstandard1.3/Newtonsoft.Json.Bson.xml", "lib/netstandard2.0/Newtonsoft.Json.Bson.dll", "lib/netstandard2.0/Newtonsoft.Json.Bson.pdb", "lib/netstandard2.0/Newtonsoft.Json.Bson.xml", "newtonsoft.json.bson.1.0.2.nupkg.sha512", "newtonsoft.json.bson.nuspec"]}, "Npgsql/9.0.3": {"sha512": "tPvY61CxOAWxNsKLEBg+oR646X4Bc8UmyQ/tJszL/7mEmIXQnnBhVJZrZEEUv0Bstu0mEsHZD5At3EO8zQRAYw==", "type": "package", "path": "npgsql/9.0.3", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net6.0/Npgsql.dll", "lib/net6.0/Npgsql.xml", "lib/net8.0/Npgsql.dll", "lib/net8.0/Npgsql.xml", "npgsql.9.0.3.nupkg.sha512", "npgsql.nuspec", "postgresql.png"]}, "Npgsql.EntityFrameworkCore.PostgreSQL/9.0.4": {"sha512": "mw5vcY2IEc7L+IeGrxpp/J5OSnCcjkjAgJYCm/eD52wpZze8zsSifdqV7zXslSMmfJG2iIUGZyo3KuDtEFKwMQ==", "type": "package", "path": "npgsql.entityframeworkcore.postgresql/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net8.0/Npgsql.EntityFrameworkCore.PostgreSQL.dll", "lib/net8.0/Npgsql.EntityFrameworkCore.PostgreSQL.xml", "npgsql.entityframeworkcore.postgresql.9.0.4.nupkg.sha512", "npgsql.entityframeworkcore.postgresql.nuspec", "postgresql.png"]}, "Oracle.ManagedDataAccess.Core/23.6.1": {"sha512": "Oc8AX7xme05xrp4/aCxKBH4+bpWgMCFafXI7LbLO/7OBMJLZRXhMtejDgIb8aYvIVyV7vSdAy3LkCYcJorxn1A==", "type": "package", "path": "oracle.manageddataaccess.core/23.6.1", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "PerfCounters/register_odpc_perfmon_counters.ps1", "PerfCounters/unregister_odpc_perfmon_counters.ps1", "README.md", "info.txt", "lib/net8.0/Oracle.ManagedDataAccess.dll", "lib/netstandard2.1/Oracle.ManagedDataAccess.dll", "oracle.manageddataaccess.core.23.6.1.nupkg.sha512", "oracle.manageddataaccess.core.nuspec", "oracle.png"]}, "Pipelines.Sockets.Unofficial/2.2.8": {"sha512": "zG2FApP5zxSx6OcdJQLbZDk2AVlN2BNQD6MorwIfV6gVj0RRxWPEp2LXAxqDGZqeNV1Zp0BNPcNaey/GXmTdvQ==", "type": "package", "path": "pipelines.sockets.unofficial/2.2.8", "files": [".nupkg.metadata", ".signature.p7s", "lib/net461/Pipelines.Sockets.Unofficial.dll", "lib/net461/Pipelines.Sockets.Unofficial.xml", "lib/net472/Pipelines.Sockets.Unofficial.dll", "lib/net472/Pipelines.Sockets.Unofficial.xml", "lib/net5.0/Pipelines.Sockets.Unofficial.dll", "lib/net5.0/Pipelines.Sockets.Unofficial.xml", "lib/netcoreapp3.1/Pipelines.Sockets.Unofficial.dll", "lib/netcoreapp3.1/Pipelines.Sockets.Unofficial.xml", "lib/netstandard2.0/Pipelines.Sockets.Unofficial.dll", "lib/netstandard2.0/Pipelines.Sockets.Unofficial.xml", "lib/netstandard2.1/Pipelines.Sockets.Unofficial.dll", "lib/netstandard2.1/Pipelines.Sockets.Unofficial.xml", "pipelines.sockets.unofficial.2.2.8.nupkg.sha512", "pipelines.sockets.unofficial.nuspec"]}, "RestSharp/112.1.0": {"sha512": "bOUGNZqhhv/QeCXHTKEUil846yBjwWXpzPKjO67kFvaAOoF361z7jLY5BMnuZ6WD2WQRA750sNMcK7MSPCQ5Mg==", "type": "package", "path": "restsharp/112.1.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net471/RestSharp.dll", "lib/net471/RestSharp.xml", "lib/net48/RestSharp.dll", "lib/net48/RestSharp.xml", "lib/net6.0/RestSharp.dll", "lib/net6.0/RestSharp.xml", "lib/net7.0/RestSharp.dll", "lib/net7.0/RestSharp.xml", "lib/net8.0/RestSharp.dll", "lib/net8.0/RestSharp.xml", "lib/netstandard2.0/RestSharp.dll", "lib/netstandard2.0/RestSharp.xml", "restsharp.112.1.0.nupkg.sha512", "restsharp.nuspec", "restsharp.png"]}, "StackExchange.Redis/2.7.27": {"sha512": "Uqc2OQHglqj9/FfGQ6RkKFkZfHySfZlfmbCl+hc+u2I/IqunfelQ7QJi7ZhvAJxUtu80pildVX6NPLdDaUffOw==", "type": "package", "path": "stackexchange.redis/2.7.27", "files": [".nupkg.metadata", ".signature.p7s", "lib/net461/StackExchange.Redis.dll", "lib/net461/StackExchange.Redis.xml", "lib/net472/StackExchange.Redis.dll", "lib/net472/StackExchange.Redis.xml", "lib/net6.0/StackExchange.Redis.dll", "lib/net6.0/StackExchange.Redis.xml", "lib/netcoreapp3.1/StackExchange.Redis.dll", "lib/netcoreapp3.1/StackExchange.Redis.xml", "lib/netstandard2.0/StackExchange.Redis.dll", "lib/netstandard2.0/StackExchange.Redis.xml", "stackexchange.redis.2.7.27.nupkg.sha512", "stackexchange.redis.nuspec"]}, "System.Buffers/4.6.0": {"sha512": "lN6tZi7Q46zFzAbRYXTIvfXcyvQQgxnY7Xm6C6xQ9784dEL1amjM6S6Iw4ZpsvesAKnRVsM4scrDQaDqSClkjA==", "type": "package", "path": "system.buffers/4.6.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "buildTransitive/net461/System.Buffers.targets", "buildTransitive/net462/_._", "lib/net462/System.Buffers.dll", "lib/net462/System.Buffers.xml", "lib/netcoreapp2.1/_._", "lib/netstandard2.0/System.Buffers.dll", "lib/netstandard2.0/System.Buffers.xml", "system.buffers.4.6.0.nupkg.sha512", "system.buffers.nuspec"]}, "System.ClientModel/1.0.0": {"sha512": "I3CVkvxeqFYjIVEP59DnjbeoGNfo/+SZrCLpRz2v/g0gpCHaEMPtWSY0s9k/7jR1rAsLNg2z2u1JRB76tPjnIw==", "type": "package", "path": "system.clientmodel/1.0.0", "files": [".nupkg.metadata", ".signature.p7s", "CHANGELOG.md", "DotNetPackageIcon.png", "README.md", "lib/net6.0/System.ClientModel.dll", "lib/net6.0/System.ClientModel.xml", "lib/netstandard2.0/System.ClientModel.dll", "lib/netstandard2.0/System.ClientModel.xml", "system.clientmodel.1.0.0.nupkg.sha512", "system.clientmodel.nuspec"]}, "System.Collections.Immutable/5.0.0": {"sha512": "FXkLXiK0sVVewcso0imKQoOxjoPAj42R8HtjjbSjVPAzwDfzoyoznWxgA3c38LDbN9SJux1xXoXYAhz98j7r2g==", "type": "package", "path": "system.collections.immutable/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Collections.Immutable.dll", "lib/net461/System.Collections.Immutable.xml", "lib/netstandard1.0/System.Collections.Immutable.dll", "lib/netstandard1.0/System.Collections.Immutable.xml", "lib/netstandard1.3/System.Collections.Immutable.dll", "lib/netstandard1.3/System.Collections.Immutable.xml", "lib/netstandard2.0/System.Collections.Immutable.dll", "lib/netstandard2.0/System.Collections.Immutable.xml", "lib/portable-net45+win8+wp8+wpa81/System.Collections.Immutable.dll", "lib/portable-net45+win8+wp8+wpa81/System.Collections.Immutable.xml", "system.collections.immutable.5.0.0.nupkg.sha512", "system.collections.immutable.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.ComponentModel/4.3.0": {"sha512": "VyGn1jGRZVfxnh8EdvDCi71v3bMXrsu8aYJOwoV7SNDLVhiEqwP86pPMyRGsDsxhXAm2b3o9OIqeETfN5qfezw==", "type": "package", "path": "system.componentmodel/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.ComponentModel.dll", "lib/netstandard1.3/System.ComponentModel.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.ComponentModel.dll", "ref/netcore50/System.ComponentModel.xml", "ref/netcore50/de/System.ComponentModel.xml", "ref/netcore50/es/System.ComponentModel.xml", "ref/netcore50/fr/System.ComponentModel.xml", "ref/netcore50/it/System.ComponentModel.xml", "ref/netcore50/ja/System.ComponentModel.xml", "ref/netcore50/ko/System.ComponentModel.xml", "ref/netcore50/ru/System.ComponentModel.xml", "ref/netcore50/zh-hans/System.ComponentModel.xml", "ref/netcore50/zh-hant/System.ComponentModel.xml", "ref/netstandard1.0/System.ComponentModel.dll", "ref/netstandard1.0/System.ComponentModel.xml", "ref/netstandard1.0/de/System.ComponentModel.xml", "ref/netstandard1.0/es/System.ComponentModel.xml", "ref/netstandard1.0/fr/System.ComponentModel.xml", "ref/netstandard1.0/it/System.ComponentModel.xml", "ref/netstandard1.0/ja/System.ComponentModel.xml", "ref/netstandard1.0/ko/System.ComponentModel.xml", "ref/netstandard1.0/ru/System.ComponentModel.xml", "ref/netstandard1.0/zh-hans/System.ComponentModel.xml", "ref/netstandard1.0/zh-hant/System.ComponentModel.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.componentmodel.4.3.0.nupkg.sha512", "system.componentmodel.nuspec"]}, "System.ComponentModel.Annotations/5.0.0": {"sha512": "dMkqfy2el8A8/I76n2Hi1oBFEbG1SfxD2l5nhwXV3XjlnOmwxJlQbYpJH4W51odnU9sARCSAgv7S3CyAFMkpYg==", "type": "package", "path": "system.componentmodel.annotations/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net461/System.ComponentModel.Annotations.dll", "lib/netcore50/System.ComponentModel.Annotations.dll", "lib/netstandard1.4/System.ComponentModel.Annotations.dll", "lib/netstandard2.0/System.ComponentModel.Annotations.dll", "lib/netstandard2.1/System.ComponentModel.Annotations.dll", "lib/netstandard2.1/System.ComponentModel.Annotations.xml", "lib/portable-net45+win8/_._", "lib/win8/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net461/System.ComponentModel.Annotations.dll", "ref/net461/System.ComponentModel.Annotations.xml", "ref/netcore50/System.ComponentModel.Annotations.dll", "ref/netcore50/System.ComponentModel.Annotations.xml", "ref/netcore50/de/System.ComponentModel.Annotations.xml", "ref/netcore50/es/System.ComponentModel.Annotations.xml", "ref/netcore50/fr/System.ComponentModel.Annotations.xml", "ref/netcore50/it/System.ComponentModel.Annotations.xml", "ref/netcore50/ja/System.ComponentModel.Annotations.xml", "ref/netcore50/ko/System.ComponentModel.Annotations.xml", "ref/netcore50/ru/System.ComponentModel.Annotations.xml", "ref/netcore50/zh-hans/System.ComponentModel.Annotations.xml", "ref/netcore50/zh-hant/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/System.ComponentModel.Annotations.dll", "ref/netstandard1.1/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/de/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/es/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/fr/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/it/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/ja/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/ko/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/ru/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/zh-hans/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/zh-hant/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/System.ComponentModel.Annotations.dll", "ref/netstandard1.3/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/de/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/es/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/fr/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/it/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/ja/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/ko/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/ru/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/zh-hans/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/zh-hant/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/System.ComponentModel.Annotations.dll", "ref/netstandard1.4/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/de/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/es/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/fr/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/it/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/ja/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/ko/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/ru/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/zh-hans/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/zh-hant/System.ComponentModel.Annotations.xml", "ref/netstandard2.0/System.ComponentModel.Annotations.dll", "ref/netstandard2.0/System.ComponentModel.Annotations.xml", "ref/netstandard2.1/System.ComponentModel.Annotations.dll", "ref/netstandard2.1/System.ComponentModel.Annotations.xml", "ref/portable-net45+win8/_._", "ref/win8/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.componentmodel.annotations.5.0.0.nupkg.sha512", "system.componentmodel.annotations.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Configuration.ConfigurationManager/9.0.4": {"sha512": "dvjqKp+2LpGid6phzrdrS/2mmEPxFl3jE1+L7614q4ZChKbLJCpHXg6sBILlCCED1t//EE+un/UdAetzIMpqnw==", "type": "package", "path": "system.configuration.configurationmanager/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Configuration.ConfigurationManager.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.Configuration.ConfigurationManager.targets", "lib/net462/System.Configuration.ConfigurationManager.dll", "lib/net462/System.Configuration.ConfigurationManager.xml", "lib/net8.0/System.Configuration.ConfigurationManager.dll", "lib/net8.0/System.Configuration.ConfigurationManager.xml", "lib/net9.0/System.Configuration.ConfigurationManager.dll", "lib/net9.0/System.Configuration.ConfigurationManager.xml", "lib/netstandard2.0/System.Configuration.ConfigurationManager.dll", "lib/netstandard2.0/System.Configuration.ConfigurationManager.xml", "system.configuration.configurationmanager.9.0.4.nupkg.sha512", "system.configuration.configurationmanager.nuspec", "useSharedDesignerContext.txt"]}, "System.Diagnostics.DiagnosticSource/8.0.1": {"sha512": "vaoWjvkG1aenR2XdjaVivlCV9fADfgyhW5bZtXT23qaEea0lWiUljdQuze4E31vKM7ZWJaSUsbYIKE3rnzfZUg==", "type": "package", "path": "system.diagnostics.diagnosticsource/8.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Diagnostics.DiagnosticSource.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Diagnostics.DiagnosticSource.targets", "lib/net462/System.Diagnostics.DiagnosticSource.dll", "lib/net462/System.Diagnostics.DiagnosticSource.xml", "lib/net6.0/System.Diagnostics.DiagnosticSource.dll", "lib/net6.0/System.Diagnostics.DiagnosticSource.xml", "lib/net7.0/System.Diagnostics.DiagnosticSource.dll", "lib/net7.0/System.Diagnostics.DiagnosticSource.xml", "lib/net8.0/System.Diagnostics.DiagnosticSource.dll", "lib/net8.0/System.Diagnostics.DiagnosticSource.xml", "lib/netstandard2.0/System.Diagnostics.DiagnosticSource.dll", "lib/netstandard2.0/System.Diagnostics.DiagnosticSource.xml", "system.diagnostics.diagnosticsource.8.0.1.nupkg.sha512", "system.diagnostics.diagnosticsource.nuspec", "useSharedDesignerContext.txt"]}, "System.Diagnostics.EventLog/9.0.4": {"sha512": "getRQEXD8idlpb1KW56XuxImMy0FKp2WJPDf3Qr0kI/QKxxJSftqfDFVo0DZ3HCJRLU73qHSruv5q2l5O47jQQ==", "type": "package", "path": "system.diagnostics.eventlog/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Diagnostics.EventLog.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.Diagnostics.EventLog.targets", "lib/net462/System.Diagnostics.EventLog.dll", "lib/net462/System.Diagnostics.EventLog.xml", "lib/net8.0/System.Diagnostics.EventLog.dll", "lib/net8.0/System.Diagnostics.EventLog.xml", "lib/net9.0/System.Diagnostics.EventLog.dll", "lib/net9.0/System.Diagnostics.EventLog.xml", "lib/netstandard2.0/System.Diagnostics.EventLog.dll", "lib/netstandard2.0/System.Diagnostics.EventLog.xml", "runtimes/win/lib/net8.0/System.Diagnostics.EventLog.Messages.dll", "runtimes/win/lib/net8.0/System.Diagnostics.EventLog.dll", "runtimes/win/lib/net8.0/System.Diagnostics.EventLog.xml", "runtimes/win/lib/net9.0/System.Diagnostics.EventLog.Messages.dll", "runtimes/win/lib/net9.0/System.Diagnostics.EventLog.dll", "runtimes/win/lib/net9.0/System.Diagnostics.EventLog.xml", "system.diagnostics.eventlog.9.0.4.nupkg.sha512", "system.diagnostics.eventlog.nuspec", "useSharedDesignerContext.txt"]}, "System.Diagnostics.PerformanceCounter/8.0.0": {"sha512": "lX6DXxtJqVGWw7N/QmVoiCyVQ+Q/Xp+jVXPr3gLK1jJExSn1qmAjJQeb8gnOYeeBTG3E3PmG1nu92eYj/TEjpg==", "type": "package", "path": "system.diagnostics.performancecounter/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Diagnostics.PerformanceCounter.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Diagnostics.PerformanceCounter.targets", "lib/net462/System.Diagnostics.PerformanceCounter.dll", "lib/net462/System.Diagnostics.PerformanceCounter.xml", "lib/net6.0/System.Diagnostics.PerformanceCounter.dll", "lib/net6.0/System.Diagnostics.PerformanceCounter.xml", "lib/net7.0/System.Diagnostics.PerformanceCounter.dll", "lib/net7.0/System.Diagnostics.PerformanceCounter.xml", "lib/net8.0/System.Diagnostics.PerformanceCounter.dll", "lib/net8.0/System.Diagnostics.PerformanceCounter.xml", "lib/netstandard2.0/System.Diagnostics.PerformanceCounter.dll", "lib/netstandard2.0/System.Diagnostics.PerformanceCounter.xml", "runtimes/win/lib/net6.0/System.Diagnostics.PerformanceCounter.dll", "runtimes/win/lib/net6.0/System.Diagnostics.PerformanceCounter.xml", "runtimes/win/lib/net7.0/System.Diagnostics.PerformanceCounter.dll", "runtimes/win/lib/net7.0/System.Diagnostics.PerformanceCounter.xml", "runtimes/win/lib/net8.0/System.Diagnostics.PerformanceCounter.dll", "runtimes/win/lib/net8.0/System.Diagnostics.PerformanceCounter.xml", "system.diagnostics.performancecounter.8.0.0.nupkg.sha512", "system.diagnostics.performancecounter.nuspec", "useSharedDesignerContext.txt"]}, "System.DirectoryServices/9.0.4": {"sha512": "qYUtcmdjVXnr8i7I8CrTovmFWLiaKexmsONERt3+SUiAwjAiPmjFe5n56Jh2J44LP1mCIAQOEmpDNQ0e0DWBwQ==", "type": "package", "path": "system.directoryservices/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.DirectoryServices.targets", "lib/net462/_._", "lib/net8.0/System.DirectoryServices.dll", "lib/net8.0/System.DirectoryServices.xml", "lib/net9.0/System.DirectoryServices.dll", "lib/net9.0/System.DirectoryServices.xml", "lib/netstandard2.0/System.DirectoryServices.dll", "lib/netstandard2.0/System.DirectoryServices.xml", "runtimes/win/lib/net8.0/System.DirectoryServices.dll", "runtimes/win/lib/net8.0/System.DirectoryServices.xml", "runtimes/win/lib/net9.0/System.DirectoryServices.dll", "runtimes/win/lib/net9.0/System.DirectoryServices.xml", "system.directoryservices.9.0.4.nupkg.sha512", "system.directoryservices.nuspec", "useSharedDesignerContext.txt"]}, "System.DirectoryServices.AccountManagement/9.0.4": {"sha512": "rQH+PhZS91rupPW5NpM2DmgI0QClrdWqGMOGphvBTvb1gnqG/1EbYnNzP8rSIzDJgWEEILXyLHaw2BeBPH2NkQ==", "type": "package", "path": "system.directoryservices.accountmanagement/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.DirectoryServices.AccountManagement.targets", "lib/net462/_._", "lib/net8.0/System.DirectoryServices.AccountManagement.dll", "lib/net8.0/System.DirectoryServices.AccountManagement.xml", "lib/net9.0/System.DirectoryServices.AccountManagement.dll", "lib/net9.0/System.DirectoryServices.AccountManagement.xml", "lib/netstandard2.0/System.DirectoryServices.AccountManagement.dll", "lib/netstandard2.0/System.DirectoryServices.AccountManagement.xml", "runtimes/win/lib/net8.0/System.DirectoryServices.AccountManagement.dll", "runtimes/win/lib/net8.0/System.DirectoryServices.AccountManagement.xml", "runtimes/win/lib/net9.0/System.DirectoryServices.AccountManagement.dll", "runtimes/win/lib/net9.0/System.DirectoryServices.AccountManagement.xml", "system.directoryservices.accountmanagement.9.0.4.nupkg.sha512", "system.directoryservices.accountmanagement.nuspec", "useSharedDesignerContext.txt"]}, "System.DirectoryServices.Protocols/9.0.4": {"sha512": "fGdiJme2/nN4xKV6sP67bN4HBz+EdoTYECFH5YVIiIRm/AJwCB+Y/4NW/7xtOFR2h6STlTY+e2/VztiaSI+ZaA==", "type": "package", "path": "system.directoryservices.protocols/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.DirectoryServices.Protocols.targets", "lib/net462/_._", "lib/net8.0/System.DirectoryServices.Protocols.dll", "lib/net8.0/System.DirectoryServices.Protocols.xml", "lib/net9.0/System.DirectoryServices.Protocols.dll", "lib/net9.0/System.DirectoryServices.Protocols.xml", "lib/netstandard2.0/System.DirectoryServices.Protocols.dll", "lib/netstandard2.0/System.DirectoryServices.Protocols.xml", "runtimes/linux/lib/net8.0/System.DirectoryServices.Protocols.dll", "runtimes/linux/lib/net8.0/System.DirectoryServices.Protocols.xml", "runtimes/linux/lib/net9.0/System.DirectoryServices.Protocols.dll", "runtimes/linux/lib/net9.0/System.DirectoryServices.Protocols.xml", "runtimes/osx/lib/net8.0/System.DirectoryServices.Protocols.dll", "runtimes/osx/lib/net8.0/System.DirectoryServices.Protocols.xml", "runtimes/osx/lib/net9.0/System.DirectoryServices.Protocols.dll", "runtimes/osx/lib/net9.0/System.DirectoryServices.Protocols.xml", "runtimes/win/lib/net8.0/System.DirectoryServices.Protocols.dll", "runtimes/win/lib/net8.0/System.DirectoryServices.Protocols.xml", "runtimes/win/lib/net9.0/System.DirectoryServices.Protocols.dll", "runtimes/win/lib/net9.0/System.DirectoryServices.Protocols.xml", "system.directoryservices.protocols.9.0.4.nupkg.sha512", "system.directoryservices.protocols.nuspec", "useSharedDesignerContext.txt"]}, "System.Formats.Asn1/9.0.5": {"sha512": "GpMHKhuwUgnp1jKiZQ1slyAQnLp4HG2MgzCJ4u4oZEfi6aBzE3HOx01JFStaiC8dtJqsv0WlrGAWVixv8TEN1w==", "type": "package", "path": "system.formats.asn1/9.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Formats.Asn1.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.Formats.Asn1.targets", "lib/net462/System.Formats.Asn1.dll", "lib/net462/System.Formats.Asn1.xml", "lib/net8.0/System.Formats.Asn1.dll", "lib/net8.0/System.Formats.Asn1.xml", "lib/net9.0/System.Formats.Asn1.dll", "lib/net9.0/System.Formats.Asn1.xml", "lib/netstandard2.0/System.Formats.Asn1.dll", "lib/netstandard2.0/System.Formats.Asn1.xml", "system.formats.asn1.9.0.5.nupkg.sha512", "system.formats.asn1.nuspec", "useSharedDesignerContext.txt"]}, "System.IdentityModel.Tokens.Jwt/6.35.0": {"sha512": "yxGIQd3BFK7F6S62/7RdZk3C/mfwyVxvh6ngd1VYMBmbJ1YZZA9+Ku6suylVtso0FjI0wbElpJ0d27CdsyLpBQ==", "type": "package", "path": "system.identitymodel.tokens.jwt/6.35.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/System.IdentityModel.Tokens.Jwt.dll", "lib/net45/System.IdentityModel.Tokens.Jwt.xml", "lib/net461/System.IdentityModel.Tokens.Jwt.dll", "lib/net461/System.IdentityModel.Tokens.Jwt.xml", "lib/net462/System.IdentityModel.Tokens.Jwt.dll", "lib/net462/System.IdentityModel.Tokens.Jwt.xml", "lib/net472/System.IdentityModel.Tokens.Jwt.dll", "lib/net472/System.IdentityModel.Tokens.Jwt.xml", "lib/net6.0/System.IdentityModel.Tokens.Jwt.dll", "lib/net6.0/System.IdentityModel.Tokens.Jwt.xml", "lib/netstandard2.0/System.IdentityModel.Tokens.Jwt.dll", "lib/netstandard2.0/System.IdentityModel.Tokens.Jwt.xml", "system.identitymodel.tokens.jwt.6.35.0.nupkg.sha512", "system.identitymodel.tokens.jwt.nuspec"]}, "System.IO/4.3.0": {"sha512": "3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "type": "package", "path": "system.io/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.IO.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.IO.dll", "ref/netcore50/System.IO.dll", "ref/netcore50/System.IO.xml", "ref/netcore50/de/System.IO.xml", "ref/netcore50/es/System.IO.xml", "ref/netcore50/fr/System.IO.xml", "ref/netcore50/it/System.IO.xml", "ref/netcore50/ja/System.IO.xml", "ref/netcore50/ko/System.IO.xml", "ref/netcore50/ru/System.IO.xml", "ref/netcore50/zh-hans/System.IO.xml", "ref/netcore50/zh-hant/System.IO.xml", "ref/netstandard1.0/System.IO.dll", "ref/netstandard1.0/System.IO.xml", "ref/netstandard1.0/de/System.IO.xml", "ref/netstandard1.0/es/System.IO.xml", "ref/netstandard1.0/fr/System.IO.xml", "ref/netstandard1.0/it/System.IO.xml", "ref/netstandard1.0/ja/System.IO.xml", "ref/netstandard1.0/ko/System.IO.xml", "ref/netstandard1.0/ru/System.IO.xml", "ref/netstandard1.0/zh-hans/System.IO.xml", "ref/netstandard1.0/zh-hant/System.IO.xml", "ref/netstandard1.3/System.IO.dll", "ref/netstandard1.3/System.IO.xml", "ref/netstandard1.3/de/System.IO.xml", "ref/netstandard1.3/es/System.IO.xml", "ref/netstandard1.3/fr/System.IO.xml", "ref/netstandard1.3/it/System.IO.xml", "ref/netstandard1.3/ja/System.IO.xml", "ref/netstandard1.3/ko/System.IO.xml", "ref/netstandard1.3/ru/System.IO.xml", "ref/netstandard1.3/zh-hans/System.IO.xml", "ref/netstandard1.3/zh-hant/System.IO.xml", "ref/netstandard1.5/System.IO.dll", "ref/netstandard1.5/System.IO.xml", "ref/netstandard1.5/de/System.IO.xml", "ref/netstandard1.5/es/System.IO.xml", "ref/netstandard1.5/fr/System.IO.xml", "ref/netstandard1.5/it/System.IO.xml", "ref/netstandard1.5/ja/System.IO.xml", "ref/netstandard1.5/ko/System.IO.xml", "ref/netstandard1.5/ru/System.IO.xml", "ref/netstandard1.5/zh-hans/System.IO.xml", "ref/netstandard1.5/zh-hant/System.IO.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.io.4.3.0.nupkg.sha512", "system.io.nuspec"]}, "System.IO.Hashing/6.0.0": {"sha512": "Rfm2jYCaUeGysFEZjDe7j1R4x6Z6BzumS/vUT5a1AA/AWJuGX71PoGB0RmpyX3VmrGqVnAwtfMn39OHR8Y/5+g==", "type": "package", "path": "system.io.hashing/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.IO.Hashing.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.IO.Hashing.dll", "lib/net461/System.IO.Hashing.xml", "lib/net6.0/System.IO.Hashing.dll", "lib/net6.0/System.IO.Hashing.xml", "lib/netstandard2.0/System.IO.Hashing.dll", "lib/netstandard2.0/System.IO.Hashing.xml", "system.io.hashing.6.0.0.nupkg.sha512", "system.io.hashing.nuspec", "useSharedDesignerContext.txt"]}, "System.IO.Packaging/8.0.1": {"sha512": "KYkIOAvPexQOLDxPO2g0BVoWInnQhPpkFzRqvNrNrMhVT6kqhVr0zEb6KCHlptLFukxnZrjuMVAnxK7pOGUYrw==", "type": "package", "path": "system.io.packaging/8.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.IO.Packaging.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.IO.Packaging.targets", "lib/net462/System.IO.Packaging.dll", "lib/net462/System.IO.Packaging.xml", "lib/net6.0/System.IO.Packaging.dll", "lib/net6.0/System.IO.Packaging.xml", "lib/net7.0/System.IO.Packaging.dll", "lib/net7.0/System.IO.Packaging.xml", "lib/net8.0/System.IO.Packaging.dll", "lib/net8.0/System.IO.Packaging.xml", "lib/netstandard2.0/System.IO.Packaging.dll", "lib/netstandard2.0/System.IO.Packaging.xml", "system.io.packaging.8.0.1.nupkg.sha512", "system.io.packaging.nuspec", "useSharedDesignerContext.txt"]}, "System.IO.Pipelines/8.0.0": {"sha512": "FHNOatmUq0sqJOkTx+UF/9YK1f180cnW5FVqnQMvYUN0elp6wFzbtPSiqbo1/ru8ICp43JM1i7kKkk6GsNGHlA==", "type": "package", "path": "system.io.pipelines/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.IO.Pipelines.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.IO.Pipelines.targets", "lib/net462/System.IO.Pipelines.dll", "lib/net462/System.IO.Pipelines.xml", "lib/net6.0/System.IO.Pipelines.dll", "lib/net6.0/System.IO.Pipelines.xml", "lib/net7.0/System.IO.Pipelines.dll", "lib/net7.0/System.IO.Pipelines.xml", "lib/net8.0/System.IO.Pipelines.dll", "lib/net8.0/System.IO.Pipelines.xml", "lib/netstandard2.0/System.IO.Pipelines.dll", "lib/netstandard2.0/System.IO.Pipelines.xml", "system.io.pipelines.8.0.0.nupkg.sha512", "system.io.pipelines.nuspec", "useSharedDesignerContext.txt"]}, "System.Memory/4.5.4": {"sha512": "1MbJTHS1lZ4bS4FmsJjnuGJOu88ZzTT2rLvrhW7Ygic+pC0NWA+3hgAen0HRdsocuQXCkUTdFn9yHJJhsijDXw==", "type": "package", "path": "system.memory/4.5.4", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Memory.dll", "lib/net461/System.Memory.xml", "lib/netcoreapp2.1/_._", "lib/netstandard1.1/System.Memory.dll", "lib/netstandard1.1/System.Memory.xml", "lib/netstandard2.0/System.Memory.dll", "lib/netstandard2.0/System.Memory.xml", "ref/netcoreapp2.1/_._", "system.memory.4.5.4.nupkg.sha512", "system.memory.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Memory.Data/1.0.2": {"sha512": "JGkzeqgBsiZwKJZ1IxPNsDFZDhUvuEdX8L8BDC8N3KOj+6zMcNU28CNN59TpZE/VJYy9cP+5M+sbxtWJx3/xtw==", "type": "package", "path": "system.memory.data/1.0.2", "files": [".nupkg.metadata", ".signature.p7s", "CHANGELOG.md", "DotNetPackageIcon.png", "README.md", "lib/net461/System.Memory.Data.dll", "lib/net461/System.Memory.Data.xml", "lib/netstandard2.0/System.Memory.Data.dll", "lib/netstandard2.0/System.Memory.Data.xml", "system.memory.data.1.0.2.nupkg.sha512", "system.memory.data.nuspec"]}, "System.Net.WebSockets.WebSocketProtocol/5.1.0": {"sha512": "cVTT/Zw4JuUeX8H0tdWii0OMHsA5MY2PaFYOq/Hstw0jk479jZ+f8baCicWFNzJlCPWAe0uoNCELoB5eNmaMqA==", "type": "package", "path": "system.net.websockets.websocketprotocol/5.1.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "buildTransitive/net461/System.Net.WebSockets.WebSocketProtocol.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Net.WebSockets.WebSocketProtocol.targets", "lib/net462/System.Net.WebSockets.WebSocketProtocol.dll", "lib/net462/System.Net.WebSockets.WebSocketProtocol.xml", "lib/net6.0/System.Net.WebSockets.WebSocketProtocol.dll", "lib/net6.0/System.Net.WebSockets.WebSocketProtocol.xml", "lib/netstandard2.0/System.Net.WebSockets.WebSocketProtocol.dll", "lib/netstandard2.0/System.Net.WebSockets.WebSocketProtocol.xml", "system.net.websockets.websocketprotocol.5.1.0.nupkg.sha512", "system.net.websockets.websocketprotocol.nuspec"]}, "System.Numerics.Vectors/4.5.0": {"sha512": "QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "type": "package", "path": "system.numerics.vectors/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Numerics.Vectors.dll", "lib/net46/System.Numerics.Vectors.xml", "lib/netcoreapp2.0/_._", "lib/netstandard1.0/System.Numerics.Vectors.dll", "lib/netstandard1.0/System.Numerics.Vectors.xml", "lib/netstandard2.0/System.Numerics.Vectors.dll", "lib/netstandard2.0/System.Numerics.Vectors.xml", "lib/portable-net45+win8+wp8+wpa81/System.Numerics.Vectors.dll", "lib/portable-net45+win8+wp8+wpa81/System.Numerics.Vectors.xml", "lib/uap10.0.16299/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/System.Numerics.Vectors.dll", "ref/net45/System.Numerics.Vectors.xml", "ref/net46/System.Numerics.Vectors.dll", "ref/net46/System.Numerics.Vectors.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.0/System.Numerics.Vectors.dll", "ref/netstandard1.0/System.Numerics.Vectors.xml", "ref/netstandard2.0/System.Numerics.Vectors.dll", "ref/netstandard2.0/System.Numerics.Vectors.xml", "ref/uap10.0.16299/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.numerics.vectors.4.5.0.nupkg.sha512", "system.numerics.vectors.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Reflection/4.3.0": {"sha512": "KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "type": "package", "path": "system.reflection/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.Reflection.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.Reflection.dll", "ref/netcore50/System.Reflection.dll", "ref/netcore50/System.Reflection.xml", "ref/netcore50/de/System.Reflection.xml", "ref/netcore50/es/System.Reflection.xml", "ref/netcore50/fr/System.Reflection.xml", "ref/netcore50/it/System.Reflection.xml", "ref/netcore50/ja/System.Reflection.xml", "ref/netcore50/ko/System.Reflection.xml", "ref/netcore50/ru/System.Reflection.xml", "ref/netcore50/zh-hans/System.Reflection.xml", "ref/netcore50/zh-hant/System.Reflection.xml", "ref/netstandard1.0/System.Reflection.dll", "ref/netstandard1.0/System.Reflection.xml", "ref/netstandard1.0/de/System.Reflection.xml", "ref/netstandard1.0/es/System.Reflection.xml", "ref/netstandard1.0/fr/System.Reflection.xml", "ref/netstandard1.0/it/System.Reflection.xml", "ref/netstandard1.0/ja/System.Reflection.xml", "ref/netstandard1.0/ko/System.Reflection.xml", "ref/netstandard1.0/ru/System.Reflection.xml", "ref/netstandard1.0/zh-hans/System.Reflection.xml", "ref/netstandard1.0/zh-hant/System.Reflection.xml", "ref/netstandard1.3/System.Reflection.dll", "ref/netstandard1.3/System.Reflection.xml", "ref/netstandard1.3/de/System.Reflection.xml", "ref/netstandard1.3/es/System.Reflection.xml", "ref/netstandard1.3/fr/System.Reflection.xml", "ref/netstandard1.3/it/System.Reflection.xml", "ref/netstandard1.3/ja/System.Reflection.xml", "ref/netstandard1.3/ko/System.Reflection.xml", "ref/netstandard1.3/ru/System.Reflection.xml", "ref/netstandard1.3/zh-hans/System.Reflection.xml", "ref/netstandard1.3/zh-hant/System.Reflection.xml", "ref/netstandard1.5/System.Reflection.dll", "ref/netstandard1.5/System.Reflection.xml", "ref/netstandard1.5/de/System.Reflection.xml", "ref/netstandard1.5/es/System.Reflection.xml", "ref/netstandard1.5/fr/System.Reflection.xml", "ref/netstandard1.5/it/System.Reflection.xml", "ref/netstandard1.5/ja/System.Reflection.xml", "ref/netstandard1.5/ko/System.Reflection.xml", "ref/netstandard1.5/ru/System.Reflection.xml", "ref/netstandard1.5/zh-hans/System.Reflection.xml", "ref/netstandard1.5/zh-hant/System.Reflection.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.reflection.4.3.0.nupkg.sha512", "system.reflection.nuspec"]}, "System.Reflection.Emit/4.7.0": {"sha512": "VR4kk8XLKebQ4MZuKuIni/7oh+QGFmZW3qORd1GvBq/8026OpW501SzT/oypwiQl4TvT8ErnReh/NzY9u+C6wQ==", "type": "package", "path": "system.reflection.emit/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Reflection.Emit.dll", "lib/netcoreapp2.0/_._", "lib/netstandard1.1/System.Reflection.Emit.dll", "lib/netstandard1.1/System.Reflection.Emit.xml", "lib/netstandard1.3/System.Reflection.Emit.dll", "lib/netstandard2.0/System.Reflection.Emit.dll", "lib/netstandard2.0/System.Reflection.Emit.xml", "lib/netstandard2.1/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcoreapp2.0/_._", "ref/netstandard1.1/System.Reflection.Emit.dll", "ref/netstandard1.1/System.Reflection.Emit.xml", "ref/netstandard1.1/de/System.Reflection.Emit.xml", "ref/netstandard1.1/es/System.Reflection.Emit.xml", "ref/netstandard1.1/fr/System.Reflection.Emit.xml", "ref/netstandard1.1/it/System.Reflection.Emit.xml", "ref/netstandard1.1/ja/System.Reflection.Emit.xml", "ref/netstandard1.1/ko/System.Reflection.Emit.xml", "ref/netstandard1.1/ru/System.Reflection.Emit.xml", "ref/netstandard1.1/zh-hans/System.Reflection.Emit.xml", "ref/netstandard1.1/zh-hant/System.Reflection.Emit.xml", "ref/netstandard2.0/System.Reflection.Emit.dll", "ref/netstandard2.0/System.Reflection.Emit.xml", "ref/netstandard2.1/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.Reflection.Emit.dll", "runtimes/aot/lib/netcore50/System.Reflection.Emit.xml", "system.reflection.emit.4.7.0.nupkg.sha512", "system.reflection.emit.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Reflection.Emit.ILGeneration/4.7.0": {"sha512": "AucBYo3DSI0IDxdUjKksBcQJXPHyoPyrCXYURW1WDsLI4M65Ar/goSHjdnHOAY9MiYDNKqDlIgaYm+zL2hA1KA==", "type": "package", "path": "system.reflection.emit.ilgeneration/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Reflection.Emit.ILGeneration.dll", "lib/netcoreapp2.0/_._", "lib/netstandard1.0/System.Reflection.Emit.ILGeneration.dll", "lib/netstandard1.0/System.Reflection.Emit.ILGeneration.xml", "lib/netstandard1.3/System.Reflection.Emit.ILGeneration.dll", "lib/netstandard2.0/System.Reflection.Emit.ILGeneration.dll", "lib/netstandard2.0/System.Reflection.Emit.ILGeneration.xml", "lib/netstandard2.1/_._", "lib/portable-net45+wp8/_._", "lib/wp80/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcoreapp2.0/_._", "ref/netstandard1.0/System.Reflection.Emit.ILGeneration.dll", "ref/netstandard1.0/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/de/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/es/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/fr/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/it/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/ja/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/ko/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/ru/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/zh-hans/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/zh-hant/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard2.0/System.Reflection.Emit.ILGeneration.dll", "ref/netstandard2.0/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard2.1/_._", "ref/portable-net45+wp8/_._", "ref/wp80/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.Reflection.Emit.ILGeneration.dll", "runtimes/aot/lib/netcore50/System.Reflection.Emit.ILGeneration.xml", "system.reflection.emit.ilgeneration.4.7.0.nupkg.sha512", "system.reflection.emit.ilgeneration.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Reflection.Emit.Lightweight/4.7.0": {"sha512": "a4OLB4IITxAXJeV74MDx49Oq2+PsF6Sml54XAFv+2RyWwtDBcabzoxiiJRhdhx+gaohLh4hEGCLQyBozXoQPqA==", "type": "package", "path": "system.reflection.emit.lightweight/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Reflection.Emit.Lightweight.dll", "lib/netcoreapp2.0/_._", "lib/netstandard1.0/System.Reflection.Emit.Lightweight.dll", "lib/netstandard1.0/System.Reflection.Emit.Lightweight.xml", "lib/netstandard1.3/System.Reflection.Emit.Lightweight.dll", "lib/netstandard2.0/System.Reflection.Emit.Lightweight.dll", "lib/netstandard2.0/System.Reflection.Emit.Lightweight.xml", "lib/netstandard2.1/_._", "lib/portable-net45+wp8/_._", "lib/wp80/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcoreapp2.0/_._", "ref/netstandard1.0/System.Reflection.Emit.Lightweight.dll", "ref/netstandard1.0/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/de/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/es/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/fr/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/it/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/ja/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/ko/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/ru/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/zh-hans/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/zh-hant/System.Reflection.Emit.Lightweight.xml", "ref/netstandard2.0/System.Reflection.Emit.Lightweight.dll", "ref/netstandard2.0/System.Reflection.Emit.Lightweight.xml", "ref/netstandard2.1/_._", "ref/portable-net45+wp8/_._", "ref/wp80/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.Reflection.Emit.Lightweight.dll", "runtimes/aot/lib/netcore50/System.Reflection.Emit.Lightweight.xml", "system.reflection.emit.lightweight.4.7.0.nupkg.sha512", "system.reflection.emit.lightweight.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Reflection.Metadata/5.0.0": {"sha512": "5NecZgXktdGg34rh1OenY1rFNDCI8xSjFr+Z4OU4cU06AQHUdRnIIEeWENu3Wl4YowbzkymAIMvi3WyK9U53pQ==", "type": "package", "path": "system.reflection.metadata/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Reflection.Metadata.dll", "lib/net461/System.Reflection.Metadata.xml", "lib/netstandard1.1/System.Reflection.Metadata.dll", "lib/netstandard1.1/System.Reflection.Metadata.xml", "lib/netstandard2.0/System.Reflection.Metadata.dll", "lib/netstandard2.0/System.Reflection.Metadata.xml", "lib/portable-net45+win8/System.Reflection.Metadata.dll", "lib/portable-net45+win8/System.Reflection.Metadata.xml", "system.reflection.metadata.5.0.0.nupkg.sha512", "system.reflection.metadata.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Reflection.Primitives/4.3.0": {"sha512": "5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "type": "package", "path": "system.reflection.primitives/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Reflection.Primitives.dll", "ref/netcore50/System.Reflection.Primitives.xml", "ref/netcore50/de/System.Reflection.Primitives.xml", "ref/netcore50/es/System.Reflection.Primitives.xml", "ref/netcore50/fr/System.Reflection.Primitives.xml", "ref/netcore50/it/System.Reflection.Primitives.xml", "ref/netcore50/ja/System.Reflection.Primitives.xml", "ref/netcore50/ko/System.Reflection.Primitives.xml", "ref/netcore50/ru/System.Reflection.Primitives.xml", "ref/netcore50/zh-hans/System.Reflection.Primitives.xml", "ref/netcore50/zh-hant/System.Reflection.Primitives.xml", "ref/netstandard1.0/System.Reflection.Primitives.dll", "ref/netstandard1.0/System.Reflection.Primitives.xml", "ref/netstandard1.0/de/System.Reflection.Primitives.xml", "ref/netstandard1.0/es/System.Reflection.Primitives.xml", "ref/netstandard1.0/fr/System.Reflection.Primitives.xml", "ref/netstandard1.0/it/System.Reflection.Primitives.xml", "ref/netstandard1.0/ja/System.Reflection.Primitives.xml", "ref/netstandard1.0/ko/System.Reflection.Primitives.xml", "ref/netstandard1.0/ru/System.Reflection.Primitives.xml", "ref/netstandard1.0/zh-hans/System.Reflection.Primitives.xml", "ref/netstandard1.0/zh-hant/System.Reflection.Primitives.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.reflection.primitives.4.3.0.nupkg.sha512", "system.reflection.primitives.nuspec"]}, "System.Runtime/4.3.0": {"sha512": "JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "type": "package", "path": "system.runtime/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.Runtime.dll", "lib/portable-net45+win8+wp80+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.Runtime.dll", "ref/netcore50/System.Runtime.dll", "ref/netcore50/System.Runtime.xml", "ref/netcore50/de/System.Runtime.xml", "ref/netcore50/es/System.Runtime.xml", "ref/netcore50/fr/System.Runtime.xml", "ref/netcore50/it/System.Runtime.xml", "ref/netcore50/ja/System.Runtime.xml", "ref/netcore50/ko/System.Runtime.xml", "ref/netcore50/ru/System.Runtime.xml", "ref/netcore50/zh-hans/System.Runtime.xml", "ref/netcore50/zh-hant/System.Runtime.xml", "ref/netstandard1.0/System.Runtime.dll", "ref/netstandard1.0/System.Runtime.xml", "ref/netstandard1.0/de/System.Runtime.xml", "ref/netstandard1.0/es/System.Runtime.xml", "ref/netstandard1.0/fr/System.Runtime.xml", "ref/netstandard1.0/it/System.Runtime.xml", "ref/netstandard1.0/ja/System.Runtime.xml", "ref/netstandard1.0/ko/System.Runtime.xml", "ref/netstandard1.0/ru/System.Runtime.xml", "ref/netstandard1.0/zh-hans/System.Runtime.xml", "ref/netstandard1.0/zh-hant/System.Runtime.xml", "ref/netstandard1.2/System.Runtime.dll", "ref/netstandard1.2/System.Runtime.xml", "ref/netstandard1.2/de/System.Runtime.xml", "ref/netstandard1.2/es/System.Runtime.xml", "ref/netstandard1.2/fr/System.Runtime.xml", "ref/netstandard1.2/it/System.Runtime.xml", "ref/netstandard1.2/ja/System.Runtime.xml", "ref/netstandard1.2/ko/System.Runtime.xml", "ref/netstandard1.2/ru/System.Runtime.xml", "ref/netstandard1.2/zh-hans/System.Runtime.xml", "ref/netstandard1.2/zh-hant/System.Runtime.xml", "ref/netstandard1.3/System.Runtime.dll", "ref/netstandard1.3/System.Runtime.xml", "ref/netstandard1.3/de/System.Runtime.xml", "ref/netstandard1.3/es/System.Runtime.xml", "ref/netstandard1.3/fr/System.Runtime.xml", "ref/netstandard1.3/it/System.Runtime.xml", "ref/netstandard1.3/ja/System.Runtime.xml", "ref/netstandard1.3/ko/System.Runtime.xml", "ref/netstandard1.3/ru/System.Runtime.xml", "ref/netstandard1.3/zh-hans/System.Runtime.xml", "ref/netstandard1.3/zh-hant/System.Runtime.xml", "ref/netstandard1.5/System.Runtime.dll", "ref/netstandard1.5/System.Runtime.xml", "ref/netstandard1.5/de/System.Runtime.xml", "ref/netstandard1.5/es/System.Runtime.xml", "ref/netstandard1.5/fr/System.Runtime.xml", "ref/netstandard1.5/it/System.Runtime.xml", "ref/netstandard1.5/ja/System.Runtime.xml", "ref/netstandard1.5/ko/System.Runtime.xml", "ref/netstandard1.5/ru/System.Runtime.xml", "ref/netstandard1.5/zh-hans/System.Runtime.xml", "ref/netstandard1.5/zh-hant/System.Runtime.xml", "ref/portable-net45+win8+wp80+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.runtime.4.3.0.nupkg.sha512", "system.runtime.nuspec"]}, "System.Runtime.Caching/8.0.0": {"sha512": "4TmlmvGp4kzZomm7J2HJn6IIx0UUrQyhBDyb5O1XiunZlQImXW+B8b7W/sTPcXhSf9rp5NR5aDtQllwbB5elOQ==", "type": "package", "path": "system.runtime.caching/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Runtime.Caching.targets", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net462/_._", "lib/net6.0/System.Runtime.Caching.dll", "lib/net6.0/System.Runtime.Caching.xml", "lib/net7.0/System.Runtime.Caching.dll", "lib/net7.0/System.Runtime.Caching.xml", "lib/net8.0/System.Runtime.Caching.dll", "lib/net8.0/System.Runtime.Caching.xml", "lib/netstandard2.0/System.Runtime.Caching.dll", "lib/netstandard2.0/System.Runtime.Caching.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "runtimes/win/lib/net6.0/System.Runtime.Caching.dll", "runtimes/win/lib/net6.0/System.Runtime.Caching.xml", "runtimes/win/lib/net7.0/System.Runtime.Caching.dll", "runtimes/win/lib/net7.0/System.Runtime.Caching.xml", "runtimes/win/lib/net8.0/System.Runtime.Caching.dll", "runtimes/win/lib/net8.0/System.Runtime.Caching.xml", "system.runtime.caching.8.0.0.nupkg.sha512", "system.runtime.caching.nuspec", "useSharedDesignerContext.txt"]}, "System.Runtime.CompilerServices.Unsafe/5.0.0": {"sha512": "ZD9TMpsmYJLrxbbmdvhwt9YEgG5WntEnZ/d1eH8JBX9LBp+Ju8BSBhUGbZMNVHHomWo2KVImJhTDl2hIgw/6MA==", "type": "package", "path": "system.runtime.compilerservices.unsafe/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net45/System.Runtime.CompilerServices.Unsafe.dll", "lib/net45/System.Runtime.CompilerServices.Unsafe.xml", "lib/netcoreapp2.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netcoreapp2.0/System.Runtime.CompilerServices.Unsafe.xml", "lib/netstandard1.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netstandard1.0/System.Runtime.CompilerServices.Unsafe.xml", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.xml", "ref/net461/System.Runtime.CompilerServices.Unsafe.dll", "ref/net461/System.Runtime.CompilerServices.Unsafe.xml", "ref/netstandard1.0/System.Runtime.CompilerServices.Unsafe.dll", "ref/netstandard1.0/System.Runtime.CompilerServices.Unsafe.xml", "ref/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll", "ref/netstandard2.0/System.Runtime.CompilerServices.Unsafe.xml", "ref/netstandard2.1/System.Runtime.CompilerServices.Unsafe.dll", "ref/netstandard2.1/System.Runtime.CompilerServices.Unsafe.xml", "system.runtime.compilerservices.unsafe.5.0.0.nupkg.sha512", "system.runtime.compilerservices.unsafe.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Security.AccessControl/4.5.0": {"sha512": "vW8Eoq0TMyz5vAG/6ce483x/CP83fgm4SJe5P8Tb1tZaobcvPrbMEL7rhH1DRdrYbbb6F0vq3OlzmK0Pkwks5A==", "type": "package", "path": "system.security.accesscontrol/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/System.Security.AccessControl.dll", "lib/net461/System.Security.AccessControl.dll", "lib/netstandard1.3/System.Security.AccessControl.dll", "lib/netstandard2.0/System.Security.AccessControl.dll", "lib/uap10.0.16299/_._", "ref/net46/System.Security.AccessControl.dll", "ref/net461/System.Security.AccessControl.dll", "ref/net461/System.Security.AccessControl.xml", "ref/netstandard1.3/System.Security.AccessControl.dll", "ref/netstandard1.3/System.Security.AccessControl.xml", "ref/netstandard1.3/de/System.Security.AccessControl.xml", "ref/netstandard1.3/es/System.Security.AccessControl.xml", "ref/netstandard1.3/fr/System.Security.AccessControl.xml", "ref/netstandard1.3/it/System.Security.AccessControl.xml", "ref/netstandard1.3/ja/System.Security.AccessControl.xml", "ref/netstandard1.3/ko/System.Security.AccessControl.xml", "ref/netstandard1.3/ru/System.Security.AccessControl.xml", "ref/netstandard1.3/zh-hans/System.Security.AccessControl.xml", "ref/netstandard1.3/zh-hant/System.Security.AccessControl.xml", "ref/netstandard2.0/System.Security.AccessControl.dll", "ref/netstandard2.0/System.Security.AccessControl.xml", "ref/uap10.0.16299/_._", "runtimes/win/lib/net46/System.Security.AccessControl.dll", "runtimes/win/lib/net461/System.Security.AccessControl.dll", "runtimes/win/lib/netcoreapp2.0/System.Security.AccessControl.dll", "runtimes/win/lib/netstandard1.3/System.Security.AccessControl.dll", "runtimes/win/lib/uap10.0.16299/_._", "system.security.accesscontrol.4.5.0.nupkg.sha512", "system.security.accesscontrol.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Security.Cryptography.Pkcs/8.0.1": {"sha512": "CoCRHFym33aUSf/NtWSVSZa99dkd0Hm7OCZUxORBjRB16LNhIEOf8THPqzIYlvKM0nNDAPTRBa1FxEECrgaxxA==", "type": "package", "path": "system.security.cryptography.pkcs/8.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Security.Cryptography.Pkcs.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Security.Cryptography.Pkcs.targets", "lib/net462/System.Security.Cryptography.Pkcs.dll", "lib/net462/System.Security.Cryptography.Pkcs.xml", "lib/net6.0/System.Security.Cryptography.Pkcs.dll", "lib/net6.0/System.Security.Cryptography.Pkcs.xml", "lib/net7.0/System.Security.Cryptography.Pkcs.dll", "lib/net7.0/System.Security.Cryptography.Pkcs.xml", "lib/net8.0/System.Security.Cryptography.Pkcs.dll", "lib/net8.0/System.Security.Cryptography.Pkcs.xml", "lib/netstandard2.0/System.Security.Cryptography.Pkcs.dll", "lib/netstandard2.0/System.Security.Cryptography.Pkcs.xml", "lib/netstandard2.1/System.Security.Cryptography.Pkcs.dll", "lib/netstandard2.1/System.Security.Cryptography.Pkcs.xml", "runtimes/win/lib/net6.0/System.Security.Cryptography.Pkcs.dll", "runtimes/win/lib/net6.0/System.Security.Cryptography.Pkcs.xml", "runtimes/win/lib/net7.0/System.Security.Cryptography.Pkcs.dll", "runtimes/win/lib/net7.0/System.Security.Cryptography.Pkcs.xml", "runtimes/win/lib/net8.0/System.Security.Cryptography.Pkcs.dll", "runtimes/win/lib/net8.0/System.Security.Cryptography.Pkcs.xml", "system.security.cryptography.pkcs.8.0.1.nupkg.sha512", "system.security.cryptography.pkcs.nuspec", "useSharedDesignerContext.txt"]}, "System.Security.Cryptography.ProtectedData/9.0.4": {"sha512": "o94k2RKuAce3GeDMlUvIXlhVa1kWpJw95E6C9LwW0KlG0nj5+SgCiIxJ2Eroqb9sLtG1mEMbFttZIBZ13EJPvQ==", "type": "package", "path": "system.security.cryptography.protecteddata/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Security.Cryptography.ProtectedData.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.Security.Cryptography.ProtectedData.targets", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net462/System.Security.Cryptography.ProtectedData.dll", "lib/net462/System.Security.Cryptography.ProtectedData.xml", "lib/net8.0/System.Security.Cryptography.ProtectedData.dll", "lib/net8.0/System.Security.Cryptography.ProtectedData.xml", "lib/net9.0/System.Security.Cryptography.ProtectedData.dll", "lib/net9.0/System.Security.Cryptography.ProtectedData.xml", "lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll", "lib/netstandard2.0/System.Security.Cryptography.ProtectedData.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "system.security.cryptography.protecteddata.9.0.4.nupkg.sha512", "system.security.cryptography.protecteddata.nuspec", "useSharedDesignerContext.txt"]}, "System.Security.Cryptography.Xml/8.0.2": {"sha512": "aDM/wm0ZGEZ6ZYJLzgqjp2FZdHbDHh6/OmpGfb7AdZ105zYmPn/83JRU2xLIbwgoNz9U1SLUTJN0v5th3qmvjA==", "type": "package", "path": "system.security.cryptography.xml/8.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Security.Cryptography.Xml.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Security.Cryptography.Xml.targets", "lib/net462/System.Security.Cryptography.Xml.dll", "lib/net462/System.Security.Cryptography.Xml.xml", "lib/net6.0/System.Security.Cryptography.Xml.dll", "lib/net6.0/System.Security.Cryptography.Xml.xml", "lib/net7.0/System.Security.Cryptography.Xml.dll", "lib/net7.0/System.Security.Cryptography.Xml.xml", "lib/net8.0/System.Security.Cryptography.Xml.dll", "lib/net8.0/System.Security.Cryptography.Xml.xml", "lib/netstandard2.0/System.Security.Cryptography.Xml.dll", "lib/netstandard2.0/System.Security.Cryptography.Xml.xml", "system.security.cryptography.xml.8.0.2.nupkg.sha512", "system.security.cryptography.xml.nuspec", "useSharedDesignerContext.txt"]}, "System.Security.Principal.Windows/5.0.0": {"sha512": "t0MGLukB5WAVU9bO3MGzvlGnyJPgUlcwerXn1kzBRjwLKixT96XV0Uza41W49gVd8zEMFu9vQEFlv0IOrytICA==", "type": "package", "path": "system.security.principal.windows/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/System.Security.Principal.Windows.dll", "lib/net461/System.Security.Principal.Windows.dll", "lib/net461/System.Security.Principal.Windows.xml", "lib/netstandard1.3/System.Security.Principal.Windows.dll", "lib/netstandard2.0/System.Security.Principal.Windows.dll", "lib/netstandard2.0/System.Security.Principal.Windows.xml", "lib/uap10.0.16299/_._", "ref/net46/System.Security.Principal.Windows.dll", "ref/net461/System.Security.Principal.Windows.dll", "ref/net461/System.Security.Principal.Windows.xml", "ref/netcoreapp3.0/System.Security.Principal.Windows.dll", "ref/netcoreapp3.0/System.Security.Principal.Windows.xml", "ref/netstandard1.3/System.Security.Principal.Windows.dll", "ref/netstandard1.3/System.Security.Principal.Windows.xml", "ref/netstandard1.3/de/System.Security.Principal.Windows.xml", "ref/netstandard1.3/es/System.Security.Principal.Windows.xml", "ref/netstandard1.3/fr/System.Security.Principal.Windows.xml", "ref/netstandard1.3/it/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ja/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ko/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ru/System.Security.Principal.Windows.xml", "ref/netstandard1.3/zh-hans/System.Security.Principal.Windows.xml", "ref/netstandard1.3/zh-hant/System.Security.Principal.Windows.xml", "ref/netstandard2.0/System.Security.Principal.Windows.dll", "ref/netstandard2.0/System.Security.Principal.Windows.xml", "ref/uap10.0.16299/_._", "runtimes/unix/lib/netcoreapp2.0/System.Security.Principal.Windows.dll", "runtimes/unix/lib/netcoreapp2.0/System.Security.Principal.Windows.xml", "runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.dll", "runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.xml", "runtimes/win/lib/net46/System.Security.Principal.Windows.dll", "runtimes/win/lib/net461/System.Security.Principal.Windows.dll", "runtimes/win/lib/net461/System.Security.Principal.Windows.xml", "runtimes/win/lib/netcoreapp2.0/System.Security.Principal.Windows.dll", "runtimes/win/lib/netcoreapp2.0/System.Security.Principal.Windows.xml", "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.dll", "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.xml", "runtimes/win/lib/netstandard1.3/System.Security.Principal.Windows.dll", "runtimes/win/lib/uap10.0.16299/_._", "system.security.principal.windows.5.0.0.nupkg.sha512", "system.security.principal.windows.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Text.Encoding/4.3.0": {"sha512": "BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "type": "package", "path": "system.text.encoding/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Text.Encoding.dll", "ref/netcore50/System.Text.Encoding.xml", "ref/netcore50/de/System.Text.Encoding.xml", "ref/netcore50/es/System.Text.Encoding.xml", "ref/netcore50/fr/System.Text.Encoding.xml", "ref/netcore50/it/System.Text.Encoding.xml", "ref/netcore50/ja/System.Text.Encoding.xml", "ref/netcore50/ko/System.Text.Encoding.xml", "ref/netcore50/ru/System.Text.Encoding.xml", "ref/netcore50/zh-hans/System.Text.Encoding.xml", "ref/netcore50/zh-hant/System.Text.Encoding.xml", "ref/netstandard1.0/System.Text.Encoding.dll", "ref/netstandard1.0/System.Text.Encoding.xml", "ref/netstandard1.0/de/System.Text.Encoding.xml", "ref/netstandard1.0/es/System.Text.Encoding.xml", "ref/netstandard1.0/fr/System.Text.Encoding.xml", "ref/netstandard1.0/it/System.Text.Encoding.xml", "ref/netstandard1.0/ja/System.Text.Encoding.xml", "ref/netstandard1.0/ko/System.Text.Encoding.xml", "ref/netstandard1.0/ru/System.Text.Encoding.xml", "ref/netstandard1.0/zh-hans/System.Text.Encoding.xml", "ref/netstandard1.0/zh-hant/System.Text.Encoding.xml", "ref/netstandard1.3/System.Text.Encoding.dll", "ref/netstandard1.3/System.Text.Encoding.xml", "ref/netstandard1.3/de/System.Text.Encoding.xml", "ref/netstandard1.3/es/System.Text.Encoding.xml", "ref/netstandard1.3/fr/System.Text.Encoding.xml", "ref/netstandard1.3/it/System.Text.Encoding.xml", "ref/netstandard1.3/ja/System.Text.Encoding.xml", "ref/netstandard1.3/ko/System.Text.Encoding.xml", "ref/netstandard1.3/ru/System.Text.Encoding.xml", "ref/netstandard1.3/zh-hans/System.Text.Encoding.xml", "ref/netstandard1.3/zh-hant/System.Text.Encoding.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.text.encoding.4.3.0.nupkg.sha512", "system.text.encoding.nuspec"]}, "System.Text.Encoding.CodePages/4.5.1": {"sha512": "4J2JQXbftjPMppIHJ7IC+VXQ9XfEagN92vZZNoG12i+zReYlim5dMoXFC1Zzg7tsnKDM7JPo5bYfFK4Jheq44w==", "type": "package", "path": "system.text.encoding.codepages/4.5.1", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Text.Encoding.CodePages.dll", "lib/net461/System.Text.Encoding.CodePages.dll", "lib/netstandard1.3/System.Text.Encoding.CodePages.dll", "lib/netstandard2.0/System.Text.Encoding.CodePages.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/win/lib/net461/System.Text.Encoding.CodePages.dll", "runtimes/win/lib/netcoreapp2.0/System.Text.Encoding.CodePages.dll", "runtimes/win/lib/netstandard1.3/System.Text.Encoding.CodePages.dll", "runtimes/win/lib/netstandard2.0/System.Text.Encoding.CodePages.dll", "system.text.encoding.codepages.4.5.1.nupkg.sha512", "system.text.encoding.codepages.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Text.Encodings.Web/8.0.0": {"sha512": "yev/k9GHAEGx2Rg3/tU6MQh4HGBXJs70y7j1LaM1i/ER9po+6nnQ6RRqTJn1E7Xu0fbIFK80Nh5EoODxrbxwBQ==", "type": "package", "path": "system.text.encodings.web/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Text.Encodings.Web.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Text.Encodings.Web.targets", "lib/net462/System.Text.Encodings.Web.dll", "lib/net462/System.Text.Encodings.Web.xml", "lib/net6.0/System.Text.Encodings.Web.dll", "lib/net6.0/System.Text.Encodings.Web.xml", "lib/net7.0/System.Text.Encodings.Web.dll", "lib/net7.0/System.Text.Encodings.Web.xml", "lib/net8.0/System.Text.Encodings.Web.dll", "lib/net8.0/System.Text.Encodings.Web.xml", "lib/netstandard2.0/System.Text.Encodings.Web.dll", "lib/netstandard2.0/System.Text.Encodings.Web.xml", "runtimes/browser/lib/net6.0/System.Text.Encodings.Web.dll", "runtimes/browser/lib/net6.0/System.Text.Encodings.Web.xml", "runtimes/browser/lib/net7.0/System.Text.Encodings.Web.dll", "runtimes/browser/lib/net7.0/System.Text.Encodings.Web.xml", "runtimes/browser/lib/net8.0/System.Text.Encodings.Web.dll", "runtimes/browser/lib/net8.0/System.Text.Encodings.Web.xml", "system.text.encodings.web.8.0.0.nupkg.sha512", "system.text.encodings.web.nuspec", "useSharedDesignerContext.txt"]}, "System.Text.Json/9.0.5": {"sha512": "rnP61ZfloTgPQPe7ecr36loNiGX3g1PocxlKHdY/FUpDSsExKkTxpMAlB4X35wNEPr1X7mkYZuQvW3Lhxmu7KA==", "type": "package", "path": "system.text.json/9.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn3.11/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn3.11/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn4.0/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn4.4/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "buildTransitive/net461/System.Text.Json.targets", "buildTransitive/net462/System.Text.Json.targets", "buildTransitive/net8.0/System.Text.Json.targets", "buildTransitive/netcoreapp2.0/System.Text.Json.targets", "buildTransitive/netstandard2.0/System.Text.Json.targets", "lib/net462/System.Text.Json.dll", "lib/net462/System.Text.Json.xml", "lib/net8.0/System.Text.Json.dll", "lib/net8.0/System.Text.Json.xml", "lib/net9.0/System.Text.Json.dll", "lib/net9.0/System.Text.Json.xml", "lib/netstandard2.0/System.Text.Json.dll", "lib/netstandard2.0/System.Text.Json.xml", "system.text.json.9.0.5.nupkg.sha512", "system.text.json.nuspec", "useSharedDesignerContext.txt"]}, "System.Threading.AccessControl/8.0.0": {"sha512": "cIed5+HuYz+eV9yu9TH95zPkqmm1J9Qps9wxjB335sU8tsqc2kGdlTEH9FZzZeCS8a7mNSEsN8ZkyhQp1gfdEw==", "type": "package", "path": "system.threading.accesscontrol/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Threading.AccessControl.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Threading.AccessControl.targets", "lib/net462/System.Threading.AccessControl.dll", "lib/net462/System.Threading.AccessControl.xml", "lib/net6.0/System.Threading.AccessControl.dll", "lib/net6.0/System.Threading.AccessControl.xml", "lib/net7.0/System.Threading.AccessControl.dll", "lib/net7.0/System.Threading.AccessControl.xml", "lib/net8.0/System.Threading.AccessControl.dll", "lib/net8.0/System.Threading.AccessControl.xml", "lib/netstandard2.0/System.Threading.AccessControl.dll", "lib/netstandard2.0/System.Threading.AccessControl.xml", "runtimes/win/lib/net6.0/System.Threading.AccessControl.dll", "runtimes/win/lib/net6.0/System.Threading.AccessControl.xml", "runtimes/win/lib/net7.0/System.Threading.AccessControl.dll", "runtimes/win/lib/net7.0/System.Threading.AccessControl.xml", "runtimes/win/lib/net8.0/System.Threading.AccessControl.dll", "runtimes/win/lib/net8.0/System.Threading.AccessControl.xml", "system.threading.accesscontrol.8.0.0.nupkg.sha512", "system.threading.accesscontrol.nuspec", "useSharedDesignerContext.txt"]}, "System.Threading.Channels/8.0.0": {"sha512": "CMaFr7v+57RW7uZfZkPExsPB6ljwzhjACWW1gfU35Y56rk72B/Wu+sTqxVmGSk4SFUlPc3cjeKND0zktziyjBA==", "type": "package", "path": "system.threading.channels/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Threading.Channels.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Threading.Channels.targets", "lib/net462/System.Threading.Channels.dll", "lib/net462/System.Threading.Channels.xml", "lib/net6.0/System.Threading.Channels.dll", "lib/net6.0/System.Threading.Channels.xml", "lib/net7.0/System.Threading.Channels.dll", "lib/net7.0/System.Threading.Channels.xml", "lib/net8.0/System.Threading.Channels.dll", "lib/net8.0/System.Threading.Channels.xml", "lib/netstandard2.0/System.Threading.Channels.dll", "lib/netstandard2.0/System.Threading.Channels.xml", "lib/netstandard2.1/System.Threading.Channels.dll", "lib/netstandard2.1/System.Threading.Channels.xml", "system.threading.channels.8.0.0.nupkg.sha512", "system.threading.channels.nuspec", "useSharedDesignerContext.txt"]}, "System.Threading.Tasks/4.3.0": {"sha512": "LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "type": "package", "path": "system.threading.tasks/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Threading.Tasks.dll", "ref/netcore50/System.Threading.Tasks.xml", "ref/netcore50/de/System.Threading.Tasks.xml", "ref/netcore50/es/System.Threading.Tasks.xml", "ref/netcore50/fr/System.Threading.Tasks.xml", "ref/netcore50/it/System.Threading.Tasks.xml", "ref/netcore50/ja/System.Threading.Tasks.xml", "ref/netcore50/ko/System.Threading.Tasks.xml", "ref/netcore50/ru/System.Threading.Tasks.xml", "ref/netcore50/zh-hans/System.Threading.Tasks.xml", "ref/netcore50/zh-hant/System.Threading.Tasks.xml", "ref/netstandard1.0/System.Threading.Tasks.dll", "ref/netstandard1.0/System.Threading.Tasks.xml", "ref/netstandard1.0/de/System.Threading.Tasks.xml", "ref/netstandard1.0/es/System.Threading.Tasks.xml", "ref/netstandard1.0/fr/System.Threading.Tasks.xml", "ref/netstandard1.0/it/System.Threading.Tasks.xml", "ref/netstandard1.0/ja/System.Threading.Tasks.xml", "ref/netstandard1.0/ko/System.Threading.Tasks.xml", "ref/netstandard1.0/ru/System.Threading.Tasks.xml", "ref/netstandard1.0/zh-hans/System.Threading.Tasks.xml", "ref/netstandard1.0/zh-hant/System.Threading.Tasks.xml", "ref/netstandard1.3/System.Threading.Tasks.dll", "ref/netstandard1.3/System.Threading.Tasks.xml", "ref/netstandard1.3/de/System.Threading.Tasks.xml", "ref/netstandard1.3/es/System.Threading.Tasks.xml", "ref/netstandard1.3/fr/System.Threading.Tasks.xml", "ref/netstandard1.3/it/System.Threading.Tasks.xml", "ref/netstandard1.3/ja/System.Threading.Tasks.xml", "ref/netstandard1.3/ko/System.Threading.Tasks.xml", "ref/netstandard1.3/ru/System.Threading.Tasks.xml", "ref/netstandard1.3/zh-hans/System.Threading.Tasks.xml", "ref/netstandard1.3/zh-hant/System.Threading.Tasks.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.threading.tasks.4.3.0.nupkg.sha512", "system.threading.tasks.nuspec"]}, "System.Threading.Tasks.Extensions/4.6.0": {"sha512": "I5G6Y8jb0xRtGUC9Lahy7FUvlYlnGMMkbuKAQBy8Jb7Y6Yn8OlBEiUOY0PqZ0hy6Ua8poVA1ui1tAIiXNxGdsg==", "type": "package", "path": "system.threading.tasks.extensions/4.6.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "buildTransitive/net461/System.Threading.Tasks.Extensions.targets", "buildTransitive/net462/_._", "lib/net462/System.Threading.Tasks.Extensions.dll", "lib/net462/System.Threading.Tasks.Extensions.xml", "lib/netcoreapp2.1/_._", "lib/netstandard2.0/System.Threading.Tasks.Extensions.dll", "lib/netstandard2.0/System.Threading.Tasks.Extensions.xml", "system.threading.tasks.extensions.4.6.0.nupkg.sha512", "system.threading.tasks.extensions.nuspec"]}, "ZooKeeperNetEx/3.4.12.4": {"sha512": "YECtByVSH7TRjQKplwOWiKyanCqYE5eEkGk5YtHJgsnbZ6+p1o0Gvs5RIsZLotiAVa6Niez1BJyKY/RDY/L6zg==", "type": "package", "path": "zookeepernetex/3.4.12.4", "files": [".nupkg.metadata", ".signature.p7s", "ZooKeeperNetEx.png", "lib/net461/ZooKeeperNetEx.dll", "lib/net461/ZooKeeperNetEx.xml", "lib/netstandard2.0/ZooKeeperNetEx.dll", "lib/netstandard2.0/ZooKeeperNetEx.xml", "zookeepernetex.3.4.12.4.nupkg.sha512", "zookeepernetex.nuspec"]}, "Webaby/1.0.0": {"type": "project", "path": "../Webaby/Webaby.csproj", "msbuildProject": "../Webaby/Webaby.csproj"}}, "projectFileDependencyGroups": {"net9.0": ["Newtonsoft.Json >= 13.0.3", "Webaby >= 1.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\Workspaces\\CEP_NETCORE\\Poptech.CEP.ClientIntegration\\Poptech.CEP.ClientIntegration.csproj", "projectName": "Poptech.CEP.ClientIntegration", "projectPath": "C:\\Users\\<USER>\\source\\Workspaces\\CEP_NETCORE\\Poptech.CEP.ClientIntegration\\Poptech.CEP.ClientIntegration.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\Workspaces\\CEP_NETCORE\\Poptech.CEP.ClientIntegration\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\Users\\<USER>\\source\\Workspaces\\CEP_NETCORE\\Webaby\\Webaby.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\Workspaces\\CEP_NETCORE\\Webaby\\Webaby.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}}
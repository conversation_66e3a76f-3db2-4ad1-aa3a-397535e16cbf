﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;
using Webaby.Localization;
using AutoMapper;

namespace TinyCRM.DigitalChannel.Queries
{
    public class GetDigitalChannelByChannelCodeQuery : QueryBase<DigitalChannelEntity>
    {
        public string ChannelCode { get; set; }
    }

    internal class GetDigitalChannelByChannelCodeQueryHandler : QueryHandlerBase<GetDigitalChannelByChannelCodeQuery, DigitalChannelEntity>
    {
        public GetDigitalChannelByChannelCodeQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<DigitalChannelEntity>> ExecuteAsync(GetDigitalChannelByChannelCodeQuery query)
        {
            var digitalChannel = (await EntitySet.GetAsync<DigitalChannelEntity>()).FirstOrDefault(x => x.ChannelCode == query.ChannelCode);
            if (digitalChannel == null) throw new InvalidOperationException(T["Không tìm thấy kênh digital có channel code '{0}'", query.ChannelCode]);
            return new QueryResult<DigitalChannelEntity>(digitalChannel);
        }
    }
}
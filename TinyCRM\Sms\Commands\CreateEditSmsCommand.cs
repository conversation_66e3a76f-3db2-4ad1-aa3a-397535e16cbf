﻿using System;
using AutoMapper;
using Webaby;
using Webaby.Data;
using Webaby.Localization;
using System.Threading.Tasks;

namespace TinyCRM.Sms.Commands
{
    public class CreateEditSmsCommand : CommandBase
    {
        public Guid Id { get; set; }

        public string Content { get; set; }

        public string PhoneNumber { get; set; }

        public SmsStatus Status { get; set; }

        public DateTime? SentDate { get; set; }

        public Guid? GatewayId { get; set; }

        public int? RetryCount { get; set; }

        public Guid? ReferenceObjectId { get; set; }

        public string ReferenceObjectType { get; set; }
    }

    internal class CreateEditSmsCommandHandler : CommandHandlerBase<CreateEditSmsCommand>
    {
        public CreateEditSmsCommandHandler(
            IText text,
            IMapper mapper,
            IRepository repository,
            IEntitySet entitySet,
            IQueryExecutor queryExecutor,
            ICommandExecutor commandExecutor,
            ILocalTransactionManager transactionManager,
            IEventBus eventBus
        ) : base(text, mapper, repository, entitySet, queryExecutor, commandExecutor, transactionManager, eventBus) { }

        public override async Task ExecuteAsync(CreateEditSmsCommand command)
        {
            var entity = await EntitySet.GetAsync<SmsEntity>(command.Id) ?? new SmsEntity();
            Mapper.Map(command, entity);
            await Repository.SaveAsync(entity);
        }
    }
}

﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Webaby.Data;
using Webaby.Localization;
using Webaby;
using TinyCRM.Customer;
using TinyCRM.Customer.Queries;
using TinyCRM.Outbound.Prospect;

namespace TinyCRM.Outbound.Contact.Queries
{
    public class GetContactByAssignmentIdQuery : QueryBase<CustomerData>
    {
        public Guid ContactAssignmentId { get; set; }
    }

    internal class GetContactByAssignmentIdQueryHandler : QueryHandlerBase<GetContactByAssignmentIdQuery, CustomerData>
    {
        public GetContactByAssignmentIdQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<CustomerData>> ExecuteAsync(GetContactByAssignmentIdQuery query)
        {
            var contactAssignment = EntitySet.Get<ProspectEntity>();
            var contact = EntitySet.Get<CustomerEntity>();

            var contacts = (from ca in contactAssignment
                            join c in contact on ca.CustomerId equals c.Id
                            where ca.Id == query.ContactAssignmentId
                            select c);

            if (!await contacts.AnyAsync())
                throw new InvalidOperationException(T[$"Không tìm  thấy contact có id '{{0}}'", query.ContactAssignmentId]);

            var items = await contacts.Select(x => new CustomerData
            {
                Id = x.Id,
                Name = x.Name,
                SubName = x.SubName,
                CompanyType = x.CompanyType,
                Code = x.Code,
                B2BCode = x.B2BCode,
                Type = x.Type,
                CMND = x.CMND,
                Dob = x.Dob,
               
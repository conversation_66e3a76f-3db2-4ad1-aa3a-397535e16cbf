﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TinyCRM.DigitalChannel.Queries;
using TinyCRM.Utility;

namespace TinyCRM.DigitalCampaign.Queries
{
    public class CampaignSummaryForCostEstimationData
    { 
        public Guid CampaignId { get; set; }
        public string CampaignName { get; set; }
        public string CampaignCode { get { return LanguageHelper.ConvertVNToAscii(CampaignName); }  set { } }
        public int TotalInCampaign { get; set; }
        public int IsLinkedOACount { get; set; }
        public int PromotionCount { get; set; }
        public int ActiveDateOver365D { get; set; }
        public int ActiveDateOver7D { get; set; }         
         
    }
}
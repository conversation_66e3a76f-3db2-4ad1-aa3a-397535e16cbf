﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Webaby;
using Webaby.Core.Access.Queries;
using Webaby.Core.UserAccount;
using Webaby.Core.UserAccount.Queries;
using Webaby.Security;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;
using Webaby.Core.Access;

namespace TinyCRM.BusinessPermission
{
    public class GetBusinessPermissionsByParentIdQuery : QueryBase<AccessBusinessPermissionData>
    {
        public Guid ParentId { get; set; }
    }

    internal class GetBusinessPermissionsByParentIdQueryHandler : QueryHandlerBase<GetBusinessPermissionsByParentIdQuery, AccessBusinessPermissionData>
    {
        public GetBusinessPermissionsByParentIdQueryHandler(IServiceProvider serviceProvider)
            : base(serviceProvider) { }

        public override async Task<QueryResult<AccessBusinessPermissionData>> ExecuteAsync(GetBusinessPermissionsByParentIdQuery query)
        {
            var bpList = await EntitySet.GetAsync<BusinessPermissionEntity>();
            var abpList = await EntitySet.GetAsync<AccessBusinessPermissionEntity>();
            var aList = await EntitySet.GetAsync<AccessEntity>();
            var data = from bp in bpList
                       join abp in abpList on bp.Id equals abp.BusinessPermissionId
                       join a in aList on abp.AccessId equals a.Id
                       where bp.ParentId == query.ParentId
                       select new AccessBusinessPermissionData
                       {
                           Id = abp.Id,
                           AccessId = a.Id,
                           BusinessPermissionId = bp.Id,
                           ActionName = a.ActionName,
                           ControllerName = a.ControllerName,
                           BusinessPermissionOrder = bp.Order
                       };
            return new QueryResult<AccessBusinessPermissionData>(data);
        }
    }
}
﻿using System;
using Webaby;

namespace TinyCRM.ContentTemplate.Command
{
    public class DeleteContentTemplateCommand : CommandBase
    {
        public Guid Id { get; set; }
    }

    internal class DeleteKnowledgeItemCommandHandler : CommandHandlerBase<DeleteContentTemplateCommand>
    {
        public DeleteKnowledgeItemCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }
        public override async Task ExecuteAsync(DeleteContentTemplateCommand command)
        {
            await Repository.DeleteAsync<ContentTemplateEntity>(command.Id);
        }
    }
}

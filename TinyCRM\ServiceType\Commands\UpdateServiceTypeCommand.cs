﻿using System;
using AutoMapper;
using Webaby;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.ServiceType.Commands
{
    public class UpdateServiceTypeCommand : CommandBase
    {
        public Guid Id { get; set; }

        public Guid? Level1Id { get; set; }

        public Guid? Level2Id { get; set; }

        public Guid? Level3Id { get; set; }

        public Guid? Level4Id { get; set; }

        public string Code { get; set; }

        public Guid? AcceptDueTimeId { get; set; }

        public Guid? ProcessDueTimeId { get; set; }

        public Guid? WorkflowId { get; set; }

        public Guid? DynamicFormId { get; set; }

        public Guid? DefaultOrganizationId { get; set; }

        public Guid? TicketOwnerDefaultOrganizationId { get; set; }

        public bool WarningTaskCreatingWhenCreateTicket { get; set; }

        public TaskListViewMode TaskListViewMode { get; set; }

        public TicketCodeMode? TicketMode { get; set; }
        public string TemplateTicketCode { get; set; }

        public List<Guid> RoleIds { get; set; }

        public MultiRequestTicketMode MultiRequestTicketMode { get; set; }
        public bool? AllowManualSLA { get; set; }
    }

    internal class UpdateServiceTypeCommandHandler : CommandHandlerBase<UpdateServiceTypeCommand>
    {
        public UpdateServiceTypeCommandHandler(
            IText text,
            IMapper mapper,
            IRepository repository,
            IEntitySet entitySet,
            IQueryExecutor queryExecutor,
            ICommandExecutor commandExecutor,
            ILocalTransactionManager transactionManager,
            IEventBus eventBus
        ) : base(text, mapper, repository, entitySet, queryExecutor, commandExecutor, transactionManager, eventBus) { }

        public override async Task ExecuteAsync(UpdateServiceTypeCommand command)
        {
            var entity = await EntitySet.GetAsync<ServiceTypeEntity>(command.Id);
            if (entity == null)
            {
                throw new InvalidOperationException(T["Không tìm thấy dịch vụ yêu cầu"]);
            }

            var old = (await EntitySet.GetAsync<ServiceTypeRoleEntity>()).Where(x => x.ServiceTypeId == command.Id).ToArray();
            await Repository.DeleteAsync(old);
            if (command.RoleIds != null && command.RoleIds.Count > 0)
            {
                await Repository.SaveAsync(command.RoleIds.Select(x => new ServiceTypeRoleEntity
                {
                    Id = Guid.NewGuid(),
                    RoleId = x,
                    ServiceTypeId = command.Id,
                }));
            }

            Mapper.Map(command, entity);
            try
            {
                await Repository.SaveAsync(entity);
            }
            catch (Exception e)
            {
                if (e.Message.Contains("unique_4level"))
                {
                    throw new Exception(T["Loại dịch vụ đã tồn tại"]);
                }
                else
                {
                    throw;
                }
            }
        }
    }
}

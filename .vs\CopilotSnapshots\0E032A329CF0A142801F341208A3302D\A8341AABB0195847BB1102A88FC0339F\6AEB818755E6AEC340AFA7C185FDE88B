﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Webaby;

namespace TinyCRM.Outbound.Appointment.Queries
{
    public class GetAppointmentByIdQuery : QueryBase<AppointmentData>
    {
        public Guid Id
        {
            get;
            set;
        }
    }

    internal class GetAppointmentByIdQueryHandler : QueryHandlerBase<GetAppointmentByIdQuery, AppointmentData>
    {
        public override QueryResult<AppointmentData> Execute(GetAppointmentByIdQuery query)
        {
            var entity = EntitySet.Get<AppointmentEntity>(query.Id);
            if (entity == null) throw new InvalidOperationException(T["Cannt found Appointment with Id '{0}'", query.Id]);
            return new QueryResult<AppointmentData>(AppointmentData.FromEntity(entity, EntitySet));
        }
    }

    public class CreateInfoData
    {
        public Guid TmrId { get; set; }
        public Guid? TmrSupId { get; set; }
        public Guid TeamId { get; set; }
        public string TmrName { get; set; }
        public string TmrSupName { get; set; }
        public string TmrSupPhone { get; set; }
        public string TeamName { get; set; }
        public DateTime CreateTime { get; set; }
    }
    public class GetCreationAppointmentQuery : QueryBase<CreateInfoData>
    {
        public Guid Id
        {
            get;
            set;
        }
    }

    internal class GetCreationAppointmentQueryHandler : QueryHandlerBase<GetCreationAppointmentQuery, CreateInfoData>
    {
        public override QueryResult<CreateInfoData> Execute(GetCreationAppointmentQuery query)
        {
            SqlCommand command = new SqlCommand();
            command.CommandText = "GetCreateAppointmentInfo";
            command.CommandType = CommandType.StoredProcedure;

            command.Parameters.AddWithValue("@LeadAssignmentId", query.Id);

            var contactQuery = EntitySet.ExecuteReadCommand<CreateInfoData>(command);

            return new QueryResult<CreateInfoData>(contactQuery);
        }
    }
}
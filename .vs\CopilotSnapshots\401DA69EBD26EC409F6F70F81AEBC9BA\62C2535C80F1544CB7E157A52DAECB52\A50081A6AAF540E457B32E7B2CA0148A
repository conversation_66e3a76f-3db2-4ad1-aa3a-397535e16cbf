﻿using System;
using System.Collections.Generic;
using System.ComponentModel.Composition;
using System.Diagnostics.Tracing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using TinyCRM.AutomaticTask;
using TinyCRM.AutomaticTask.Command;
using TinyCRM.Phase;
using TinyCRM.Phase.Queries;
using TinyCRM.RequestTicket.Commands;
using TinyCRM.RequestTicket.Queries;
using TinyCRM.ServiceType.Queries;
using TinyCRM.Survey;
using Webaby;
using Webaby.Core.DynamicForm.Queries;
using Webaby.Security;

namespace TinyCRM.RequestTicket.Events
{
    public class RequestTicketUpdatedEvent : Event
    {
        public Guid RequestTicketId { get; set; }

        public RequestTicketData OldRequestTicketData { get; set; }

        public RequestTicketData NewRequestTicketData { get; set; }

        public Dictionary<Guid, string> OldDynamicFieldValues { get; set; }

        public Dictionary<Guid, string> NewDynamicFieldValues { get; set; }
    }

    public class RequestTicketUpdatedEventHandler : IEventHandler<RequestTicketUpdatedEvent>
    {
        public ICommandExecutor CommandExecutor { get; }
        public IQueryExecutor QueryExecutor { get; }
        public ITaskCommand TaskCommand { get; }

        public RequestTicketUpdatedEventHandler(
            ICommandExecutor commandExecutor,
            IQueryExecutor queryExecutor,
            ITaskCommand taskCommand)
        {
            CommandExecutor = commandExecutor;
            QueryExecutor = queryExecutor;
            TaskCommand = taskCommand;
        }

        public void Handle(RequestTicketUpdatedEvent @event)
        {
            Guid? phaseId = null;
            var currentPhase = QueryExecutor.ExecuteOne(new GetCurrentPhaseByRequestTicketIdQuery { RequestTicketId = @event.RequestTicketId });
            if (currentPhase != null)
            {
                phaseId = currentPhase.Id;
            }

            if (@event.NewRequestTicketData.WorkflowId.IsNotNullOrEmpty())
            {
                CommandExecutor.Execute(new CreateTasksByAutoNextTasksCommand
                {
                    TriggeredByTaskDone = false,
                    AutoTriggeredEvent = AutoTriggeredEvent.RequestTicketUpdated,

                    ServiceTypeId = @event.NewRequestTicketData.ServiceTypeId,
                    WorkflowId = @event.NewRequestTicketData.WorkflowId.Value,
                    RequestTicketId = @event.RequestTicketId,
                    PhaseId = phaseId,
                    RequestTicketCreatorId = @event.NewRequestTicketData.CreatedBy,
                    RequestTicketOwnerId = @event.NewRequestTicketData.OwnerId,

                    RequestTicketUpdatedEvent = @event,
                });
            }
        }
    }
}
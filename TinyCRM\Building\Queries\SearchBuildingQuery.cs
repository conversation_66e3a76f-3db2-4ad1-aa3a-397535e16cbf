﻿using Webaby.Data;
using AutoMapper;
using Webaby.Localization;
using Webaby;
using System.Threading.Tasks;

namespace TinyCRM.Building.Queries
{
    public class SearchBuildingQuery : QueryBase<BuildingData>
    {
    }

    internal class SearchBuildingQueryHandler : QueryHandlerBase<SearchBuildingQuery, BuildingData>
    {
        public SearchBuildingQueryHandler(IServiceProvider serviceProvider)
            : base(serviceProvider) { }

        public override async Task<QueryResult<BuildingData>> ExecuteAsync(SearchBuildingQuery query)
        {
            var entity = await EntitySet.GetAsync<BuildingEntity>();
            return QueryResult.Create(entity, Mapper.Map<BuildingData>);
        }
    }
}

﻿using System;
using Webaby;
using Webaby.Data;
using System.Data.SqlClient;
using System.Data;
using AutoMapper;
using Webaby.Localization;

namespace TinyCRM.Campaign.Commands
{
    public class CreateSurveyFeedbackFromCampaignTicketAssignWorkCommand : CommandBase
    {
        public Guid CampaignWorkId { get; set; }

        public Guid SurveyFeedbackId { get; set; }
    }

    internal class CreateSurveyFeedbackFromCampaignTicketAssignWorkCommandHandler : CommandHandlerBase<CreateSurveyFeedbackFromCampaignTicketAssignWorkCommand>
    {
        public CreateSurveyFeedbackFromCampaignTicketAssignWorkCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task ExecuteAsync(CreateSurveyFeedbackFromCampaignTicketAssignWorkCommand command)
        {
            var cmd = EntitySet.CreateDbCommand();
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@CampaignWorkId", command.CampaignWorkId));
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@SurveyFeedbackId", command.SurveyFeedbackId));

            cmd.CommandText = "CreateSurveyFeedbackFromCampaignTicketAssignWork";
            cmd.CommandType = CommandType.StoredProcedure;

            await EntitySet.ExecuteNonQueryAsync(cmd);
        }
    }
}
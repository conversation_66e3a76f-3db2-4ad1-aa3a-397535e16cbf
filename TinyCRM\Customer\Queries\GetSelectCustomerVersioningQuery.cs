﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Webaby;

namespace TinyCRM.Customer.Queries
{
    public class GetSelectCustomerVersioningQuery : QueryBase<string>
    {
    }

    internal class GetSelectCustomerVersioningQueryHandler : QueryHandlerBase<GetSelectCustomerVersioningQuery, string>
    {
        public GetSelectCustomerVersioningQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<string>> ExecuteAsync(GetSelectCustomerVersioningQuery query)
        {
            var customerversionEntity = EntitySet.Get<CustomerVersioningEntity>().GroupBy(x=>x.Version).Select(x=>x.Key).ToList();
            return new QueryResult<string>(customerversionEntity);
        }
    }
}

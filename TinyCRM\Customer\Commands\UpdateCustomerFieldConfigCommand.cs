﻿using AutoMapper;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TinyCRM.Customer.Queries;
using TinyCRM.Enums;
using Webaby;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.Customer.Commands
{
    public class UpdateCustomerFieldConfigCommand : CommandBase
    {
        public List<CustomerFieldConfigurationData> listconfig { get; set; }
    }

    internal class UpdateCustomerFieldConfigCommandHandler : CommandHandlerBase<UpdateCustomerFieldConfigCommand>
    {
        public UpdateCustomerFieldConfigCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task ExecuteAsync(UpdateCustomerFieldConfigCommand command)
        {
            List<IEntity> savedEntites = new List<IEntity>();
            foreach (var item in command.listconfig)
            {
                CustomerFieldConfigurationEntity entity = await EntitySet.GetAsync<CustomerFieldConfigurationEntity>(item.Id);
                if(entity != null)
                {
                    entity.Order = item.Order;
                    entity.GridSize = item.GridSize;
                    entity.Required = item.Required;
                    entity.IsHidden = item.IsHidden;
                    savedEntites.Add(entity);
                }
            }
            await Repository.SaveAsync(savedEntites);
        }
    }
}
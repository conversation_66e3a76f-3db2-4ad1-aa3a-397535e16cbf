﻿using AutoMapper;
using System;
using System.Linq;
using System.Threading.Tasks;
using Webaby;
using Webaby.Core.UserAccount;
using Webaby.Data;
using Webaby.Localization;
using Webaby.Security;

namespace TinyCRM.Campaign.Queries
{
    public class GetCampaignWorkerListQuery : QueryBase<CampaignWorkerListItem>
    {
        public Guid CampaignId { get; set; }
    }

    internal class GetCampaignWorkerListQueryHandler : QueryHandlerBase<GetCampaignWorkerListQuery, CampaignWorkerListItem>
    {
        public GetCampaignWorkerListQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<CampaignWorkerListItem>> ExecuteAsync(GetCampaignWorkerListQuery query)
        {
            var campaignWokerQuery = (await EntitySet.GetAsync<CampaignWorkerEntity>()).Where(cwr => cwr.CampaignId == query.CampaignId);
            var userProfileQuery = await EntitySet.GetAsync<AspNetUserEntity>();
            var mainQuery = (from cwr in campaignWokerQuery
                             join up in userProfileQuery on cwr.UserId equals up.Id
                             join u in userProfileQuery on up.Id equals u.Id
                             orderby up.FullName
                             select new CampaignWorkerListItem
                             {
                                 WorkerId = cwr.Id,
                                 UserId = up.Id,
                                 Name = up.FullName,
                                 UserName = u.UserName
                             });

            return QueryResult.Create(mainQuery);
        }
    }
}

﻿using System;
using System.Data;
using System.Data.SqlClient;
using Webaby;
using Webaby.Data;

namespace TinyCRM.Customer.Queries
{
    public class GetAgencyHierarchySummaryQuery : QueryBase<CustomerHierarchySummaryData>
    {
        public string Node { get; set; } 
    }

    internal class GetAgencyHierarchySummaryQueryHandler: QueryHandlerBase<GetAgencyHierarchySummaryQuery, CustomerHierarchySummaryData>
    {
        public GetAgencyHierarchySummaryQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }
        public override async Task<QueryResult<CustomerHierarchySummaryData>> ExecuteAsync(GetAgencyHierarchySummaryQuery query)
        {
            var cmd = EntitySet.CreateDbCommand();
            cmd.Parameters.AddRange(new[]
            {
                DbParameterHelper.AddNullableString(cmd, "@Node", query.Node)
            });

            cmd.CommandText = "dbo.GetAgencyHierarchySummary";
            cmd.CommandType = CommandType.StoredProcedure;
            var mainQuery = await EntitySet.ExecuteReadCommandAsync<CustomerHierarchySummaryData>(cmd);
            return new QueryResult<CustomerHierarchySummaryData>(mainQuery);
        }
    }
}

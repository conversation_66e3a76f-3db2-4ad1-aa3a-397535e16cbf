﻿using Webaby.Data;
using AutoMapper;
using Webaby.Localization;
using Webaby;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace TinyCRM.Campaign.Queries
{
    public class GetCampaignAssignmentByWorkIdQuery : QueryBase<CampaignAssignmentData>
    {
        public Guid WorkId { get; set; }
    }

    internal class GetCampaignAssignmentByWorkIdQueryHandler : QueryHandlerBase<GetCampaignAssignmentByWorkIdQuery, CampaignAssignmentData>
    {
        public GetCampaignAssignmentByWorkIdQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<CampaignAssignmentData>> ExecuteAsync(GetCampaignAssignmentByWorkIdQuery query)
        {
            var assignmentQuery = await EntitySet.GetAsync<CampaignAssignmentEntity>();
            var workQuery = await EntitySet.GetAsync<CampaignWorkEntity>();

            var mainQuery = (from w in workQuery
                             join a in assignmentQuery on w.CurrentAssignmentId equals a.Id
                             where w.Id == query.WorkId
                             select a);

            return QueryResult.Create(mainQuery, x => Mapper.Map<CampaignAssignmentData>(x));
        }
    }
}

﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;

namespace TinyCRM.AutomaticTask.Command
{
    public class DeleteAutoNextTaskCommand : CommandBase
    {
        public List<Guid> Ids { get; set; }
    }

    internal class DeleteAutoNextTaskCommandHandler : CommandHandlerBase<DeleteAutoNextTaskCommand>
    {
        public DeleteAutoNextTaskCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task ExecuteAsync(DeleteAutoNextTaskCommand command)
        {
            var delEntity = new List<AutoNextTaskEntity>();
            foreach (var item in command.Ids)
            {
                var link = await EntitySet.GetAsync<AutoNextTaskEntity>(item);
                delEntity.Add(link);
            }
            await Repository.DeleteAsync(delEntity);
        }
    }
}
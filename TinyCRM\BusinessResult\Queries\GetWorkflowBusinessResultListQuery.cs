﻿using AutoMapper;
using System;
using System.Linq;
using System.Threading.Tasks;
using TinyCRM.TaskType;
using TinyCRM.Workflow;
using Webaby;
using Webaby.Data;
using Webaby.Localization;
using Webaby.Security;

namespace TinyCRM.BusinessResult.Queries
{
    public class GetWorkflowBusinessResultListQuery : QueryBase<BusinessResultData>
    {
        public Guid WorkflowId { get; set; }
    }

    public class GetWorkflowBusinessResultListQueryHandler : QueryHandlerBase<GetWorkflowBusinessResultListQuery, BusinessResultData>
    {
        public GetWorkflowBusinessResultListQueryHandler(IServiceProvider serviceProvider)
            : base(serviceProvider) { }

        public override async Task<QueryResult<BusinessResultData>> ExecuteAsync(GetWorkflowBusinessResultListQuery query)
        {
            var allBr = (await EntitySet.GetAsync<BusinessResultEntity>()).ToList();
            var allRef = (await EntitySet.GetAsync<BusinessResultReferenceEntity>()).ToList();
            var entityList = (from br in allBr
                              join wfbr in allRef on br.Id equals wfbr.BusinessResultId
                              where wfbr.ReferenceObjectId == query.WorkflowId
                              orderby br.DisplayOrder
                              select br);
            var result = entityList.ToList();
            var mapped = result.Select(x => Mapper.Map<BusinessResultData>(x));
            return QueryResult.Create(mapped);
        }
    }
}
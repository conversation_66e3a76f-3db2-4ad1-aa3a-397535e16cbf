﻿using Webaby.Data;
using AutoMapper;
using Webaby.Localization;
using Webaby;
using System.Threading.Tasks;

namespace TinyCRM.AutomaticTask.Queries
{
    public class GetAllAutoConditionsQuery : QueryBase<AutoConditionData>
    {
    }

    internal class GetAllAutoConditionsQueryHandler : QueryHandlerBase<GetAllAutoConditionsQuery, AutoConditionData>
    {
        public GetAllAutoConditionsQueryHandler(IServiceProvider serviceProvider)
            : base(serviceProvider) { }

        public override async Task<QueryResult<AutoConditionData>> ExecuteAsync(GetAllAutoConditionsQuery query)
        {
            var entity = await EntitySet.GetAsync<AutoConditionEntity>();
            var mapped = entity.Select(x => Mapper.Map<AutoConditionData>(x));
            return QueryResult.Create(mapped);
        }
    }
}
﻿using System.Data;
using System.Data.SqlClient;
using TinyCRM.Enums;
using Webaby;
using Webaby.Data;

namespace TinyCRM.Customer.Queries
{
    public class SearchClassificationQuery : QueryBase<ClassificationData>
    {
        public string Name { get; set; }

        public string Code { get; set; }

        public CustomerType? Type { get; set; }
    }

    internal class SearchClassificationQueryHandler : QueryHandlerBase<SearchClassificationQuery, ClassificationData>
    {
        public SearchClassificationQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<ClassificationData>> ExecuteAsync(SearchClassificationQuery query)
        {
            int startRow = query.Pagination.Index * query.Pagination.Size + 1;
            int endRow = query.Pagination.Index * query.Pagination.Size + query.Pagination.Size;

            var cmd = EntitySet.CreateDbCommand();
            cmd.Parameters.AddRange(new[]
            {
                DbParameterHelper.AddNullableString(cmd, "@Name", query.Name),
                DbParameterHelper.AddNullableString(cmd, "@Code", query.Code),
                DbParameterHelper.AddNullableEnum(cmd, "@Type", query.Type),
                DbParameterHelper.AddNullableInt(cmd, "@StartRow", startRow),
                DbParameterHelper.AddNullableInt(cmd, "@EndRow", endRow)
            });

            cmd.CommandText = "dbo.SearchClassifications";
            cmd.CommandType = CommandType.StoredProcedure;

            var mainQuery = await EntitySet.ExecuteReadCommandAsync<ClassificationData>(cmd);
            return new QueryResult<ClassificationData>(mainQuery);
        }
    }
}

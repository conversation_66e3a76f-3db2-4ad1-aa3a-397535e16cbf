﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.EntityFrameworkCore;
using TinyCRM.TaskType;
using TinyCRM.Workflow;
using Webaby;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.BusinessResult.Queries
{
    public class GetTaskTypeBusinessResultListQuery : QueryBase<BusinessResultData>
    {
        public Guid TaskTypeId { get; set; }
        public bool IsTaskGroup { get; set; } = false;
    }

    public class GetTaskTypeBusinessResultListQueryHandler : QueryHandlerBase<GetTaskTypeBusinessResultListQuery, BusinessResultData>
    {
        public GetTaskTypeBusinessResultListQueryHandler(IServiceProvider serviceProvider)
            : base(serviceProvider) { }

        public override async Task<QueryResult<BusinessResultData>> ExecuteAsync(GetTaskTypeBusinessResultListQuery query)
        {
            var allBr = (await EntitySet.GetAsync<BusinessResultEntity>()).ToList();
            var allRef = (await EntitySet.GetAsync<BusinessResultReferenceEntity>()).ToList();
            var allWorkflowTaskType = (await EntitySet.GetAsync<WorkflowTaskTypeEntity>()).ToList();
            var entityList = (from br in allBr
                              join ttbr in allRef on br.Id equals ttbr.BusinessResultId
                              where ttbr.ReferenceObjectId == query.TaskTypeId
                              orderby br.DisplayOrder
                              select br);
            if (query.IsTaskGroup)
            {
                var data = new List<BusinessResultEntity>();
                var workflowtasktype = allWorkflowTaskType.Where(x => x.WorkflowTaskTypeGroupId == query.TaskTypeId);
                foreach(var item in workflowtasktype)
                {
                    var bsreult = (from br in allBr
                                      join ttbr in allRef on br.Id equals ttbr.BusinessResultId
                                      where ttbr.ReferenceObjectId == item.TaskTypeId
                                      orderby br.DisplayOrder
                                      select br).ToList();
                    if(data.Count == 0)
                    {
                        data.AddRange(bsreult);                      
                    } else
                    {
                        var result = (from root in data
                                      join update in bsreult on root.Id equals update.Id
                                      select root).ToList();
                        if (result.Count == 0)
                        {
                            var items = result.Select(x => new BusinessResultData
                            {
                                Id = x.Id,
                                Name = x.Name,
                                // ... map các property khác cần thiết
                            }).ToList();
                            return QueryResult.Create(items);
                        }
                        data = result;
                    }                    
                }
                var itemsGroup = data.Select(x => new BusinessResultData
                {
                    Id = x.Id,
                    Name = x.Name,
                    // ... map các property khác cần thiết
                }).ToList();
                return QueryResult.Create(itemsGroup);
            }
            var itemsSingle = entityList.Select(x => new BusinessResultData
            {
                Id = x.Id,
                Name = x.Name,
                // ... map các property khác cần thiết
            }).ToList();
            return QueryResult.Create(itemsSingle);
        }
    }
}
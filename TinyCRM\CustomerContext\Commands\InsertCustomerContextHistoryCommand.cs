﻿using AutoMapper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TinyCRM.CustomerContext.Events;
using Webaby;
using Webaby.Web;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.CustomerContext.Commands
{
    public class InsertCustomerContextHistoryCommand : CommandBase
    {
        public Guid Id { get; set; }

        public string Token { get; set; }

        public string Title { get; set; }

        public string Content { get; set; }

        public CustomerContextType ContextType { get; set; }

        public ContextFrom ContextFrom { get; set; }

        public DateTime CreatedDate { get; set; }

        public Guid CreatedBy { get; set; }

        public string CreatedByName { get; set; }
    }

    internal class InsertCustomerContextHistoryCommandHandler : CommandHandlerBase<InsertCustomerContextHistoryCommand>
    {
        [Import]
        public IHubEventBus HubEventBus { get; set; }

        public InsertCustomerContextHistoryCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task ExecuteAsync(InsertCustomerContextHistoryCommand command)
        {
            var customerContextHistoryEntity = new CustomerContextHistoryEntity();
            Mapper.Map(command, customerContextHistoryEntity);            

            await Repository.SaveAsync(customerContextHistoryEntity);

            CustomerContextEvent contextEvent = new CustomerContextEvent();
            contextEvent.Token = command.Token;
            contextEvent.ContextType = command.ContextType;
            contextEvent.ContextFrom = command.ContextFrom;
            contextEvent.Title = command.Title;
            contextEvent.Content = command.Content;
            contextEvent.CreatedByName = command.CreatedByName;
            contextEvent.CreatedDate = command.CreatedDate;

            HubEventBus.PublishServerEventGroup(contextEvent, "customer-context-" + command.Token);
        }
    }
}

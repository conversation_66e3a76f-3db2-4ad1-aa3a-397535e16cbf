﻿using System;
using System.Data.SqlClient;
using Webaby.Data;
using Webaby;
using System.Data;
using System.Threading.Tasks;
using AutoMapper;
using Webaby.Localization;

namespace TinyCRM.DigitalCampaign.Queries
{
    public class GetAnonymousDigitalContactRawListInCampaignQuery : QueryBase<CampaignAnonymousDigitalContactRawData>
    {
        public Guid CampaignId { get; set; }

        public Guid? ProspectAssignmentId { get; set; }
    }

    internal class GetAnonymousDigitalContactRawListInCampaignQueryHandler : QueryHandlerBase<GetAnonymousDigitalContactRawListInCampaignQuery, CampaignAnonymousDigitalContactRawData>
    {
        public GetAnonymousDigitalContactRawListInCampaignQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<CampaignAnonymousDigitalContactRawData>> ExecuteAsync(GetAnonymousDigitalContactRawListInCampaignQuery query)
        {
            var cmd = EntitySet.CreateDbCommand();
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@CampaignId", query.CampaignId));
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@ProspectAssignmentId", query.ProspectAssignmentId));
            cmd.CommandText = "dbo.GetAnonymousDigitalContactListInCampaign";
            cmd.CommandType = CommandType.StoredProcedure;
            var mainQuery = await EntitySet.ExecuteReadCommandAsync<CampaignAnonymousDigitalContactRawData>(cmd);
            return new QueryResult<CampaignAnonymousDigitalContactRawData>(mainQuery);
        }
    }
}
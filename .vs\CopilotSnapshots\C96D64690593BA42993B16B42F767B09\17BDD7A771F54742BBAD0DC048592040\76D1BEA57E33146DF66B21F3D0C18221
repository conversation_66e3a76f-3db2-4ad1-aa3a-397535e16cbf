﻿using AutoMapper;
using TinyCRM.Channel;
using TinyCRM.Channel.Commands;
using TinyCRM.Channel.Queries;
using TinyCRM.DynamicDefinedTable;
using TinyCRM.DynamicDefinedTable.Queries;
using TinyCRM.TicketHotButton;
using TinyCRM.TicketHotButton.Queries;
using TinyCRM.UserAccount;
using TinyCRM.UserAccount.Queries;
using TinyCRM.Customer;
using TinyCRM.Customer.Queries;
using Webaby.Security;

namespace TinyCRM
{
    public class AutoMapperProfile : Profile
    {
        public AutoMapperProfile()
        {
            // AspNetUser
            CreateMap<AspNetUserEntity, AspNetUserData>();
            CreateMap<AspNetUserEntity, AspNetUserListItem>();
            CreateMap<UserIdentity, UserProfileEntityData>();

            // Channel
            CreateMap<ChannelEntity, ChannelData>();
            CreateMap<CreateEditChannelCommand, ChannelEntity>();

            // TicketHotButton
            CreateMap<TicketHotButtonEntity, TicketHotButtonData>();

            // DynamicDefinedTable
            CreateMap<DynamicDefinedTableSchemaEntity, DynamicDefinedTableSchemaData>();
            CreateMap<DynamicDefinedTableColumnEntity, DynamicDefinedTableColumnData>();
            CreateMap<DynamicDefinedTableCellValueEntity, DynamicDefinedTableCellValueData>();

            // Customer mapping
            CreateMap<CustomerEntity, CustomerData>();
            CreateMap<CustomerData, CustomerEntity>();
        }
    }
}
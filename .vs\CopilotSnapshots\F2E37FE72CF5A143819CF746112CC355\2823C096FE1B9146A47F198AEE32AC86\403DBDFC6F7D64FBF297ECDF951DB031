﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Webaby;

namespace TinyCRM.Outbound.Campaign.Queries
{
    public class GetAllCampaignParameterListQuery : QueryBase<CampaignParameterData>
    {
    }

    internal class GetAllCampaignParameterListQueryHandler : QueryHandlerBase<GetAllCampaignParameterListQuery, CampaignParameterData>
    {
        public override QueryResult<CampaignParameterData> Execute(GetAllCampaignParameterListQuery query)
        {
            var campaignParameterQuery = EntitySet.Get<CampaignParameterEntity>();
            return QueryResult.Create(campaignParameterQuery, CampaignParameterData.FromEntity);
        }
    }
}
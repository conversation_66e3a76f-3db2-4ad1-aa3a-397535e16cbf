﻿using System;
using System.Data;
using System.Data.SqlClient;
using Webaby;
using Webaby.Data;

namespace TinyCRM.Outbound.Campaign.Queries
{
    public class GetAllCampaignQuery : QueryBase<CampaignData>
    {
    }

    public class GetAllCampaignQueryHandler : QueryHandlerBase<GetAllCampaignQuery, CampaignData>
    {
        public override QueryResult<CampaignData> Execute(GetAllCampaignQuery query)
        {
            var result = EntitySet.Get<CampaignEntity>();
            return QueryResult.Create(result, CampaignData.FromEntity);
        }
    }
}
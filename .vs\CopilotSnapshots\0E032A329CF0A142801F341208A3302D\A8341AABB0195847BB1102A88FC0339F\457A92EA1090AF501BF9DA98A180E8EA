﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TinyCRM.Outbound.TemplateCodeCallResult;
using Webaby;

namespace TinyCRM.Outbound.CallResult.Queries
{
    public class GetCallResultListByTemplateQuery : QueryBase<CallResultData>
    {
        public Guid CampaignTemplateCode
        {
            get;
            set;
        }
    }

    internal class GetCallResultListByTemplateQueryHandler : QueryHandlerBase<GetCallResultListByTemplateQuery, CallResultData>
    {
        public override QueryResult<CallResultData> Execute(GetCallResultListByTemplateQuery query)
        {
            var templateCodeCallResultQuery = EntitySet.Get<TemplateCodeCallResultEntity>();
            var callResultQuery = EntitySet.Get<CallResultEntity>();

            var mainQuery = (from c in callResultQuery
                             join tc in templateCodeCallResultQuery on c.Id equals tc.CallResultId
                             where tc.CampaignTemplateCodeId == query.CampaignTemplateCode
                             orderby c.Code
                             select c);

            return QueryResult.Create(mainQuery, CallResultData.FromEntity);
        }
    }
}
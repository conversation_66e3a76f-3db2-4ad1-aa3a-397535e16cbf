﻿using Webaby.Data;
using AutoMapper;
using Webaby.Localization;
using Webaby;
using System;
using System.Linq;
using System.Threading.Tasks;
using TinyCRM.Outbound.Campaign;
using TinyCRM.Enums;

namespace TinyCRM.Campaign.Queries
{
    public class GetCampaignWorkSummaryQuery : QueryBase<CampaignWorkSummaryInfo>
    {
        public Guid CampaignId { get; set; }
        public string ReferenceObjectType { get; set; }
    }

    internal class GetCampaignWorkSummaryQueryHandler : QueryHandlerBase<GetCampaignWorkSummaryQuery, CampaignWorkSummaryInfo>
    {
        public GetCampaignWorkSummaryQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<CampaignWorkSummaryInfo>> ExecuteAsync(GetCampaignWorkSummaryQuery query)
        {
            var campList = await EntitySet.GetAsync<CampaignEntity>();
            var campWorkList = await EntitySet.GetAsync<CampaignWorkEntity>();
            var campAssignList = await EntitySet.GetAsync<CampaignAssignmentEntity>();
            var summaryQuery = from camp in campList
                               join campWork in campWorkList on camp.Id equals campWork.CampaignId
                               join campAssign in campAssignList on campWork.CurrentAssignmentId equals campAssign.Id into campAssignN
                               from campAssignX in campAssignN.DefaultIfEmpty()
                               group new { campWork, campAssignX } by new { camp.Id, campWork.ReferenceObjectType }
                               into campWorkGroup
                               where campWorkGroup.Key.Id == query.CampaignId
                               select new CampaignWorkSummaryInfo
                               {
                                   CampaignId = campWorkGroup.Key.Id,
                                   ReferenceObjectType = campWorkGroup.Key.ReferenceObjectType,
                                   TotalCount = campWorkGroup.Count(),
                                   DistributedCount = campWorkGroup.Count(x => x.campAssignX != null),
                                   CompletedCount = campWorkGroup.Count(x => x.campAssignX != null && x.campAssignX.Status == AssignmentStatus.Done)
                               };
            return new QueryResult<CampaignWorkSummaryInfo>(summaryQuery);
        }
    }
}

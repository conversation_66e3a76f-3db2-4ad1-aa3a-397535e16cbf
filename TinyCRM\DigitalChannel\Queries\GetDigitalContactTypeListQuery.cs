﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;

namespace TinyCRM.DigitalChannel.Queries
{
    public class GetDigitalContactTypeListQuery : QueryBase<DigitalContactTypeData>
    {
    }

    internal class GetDigitalContactTypeListQueryHandler : QueryHandlerBase<GetDigitalContactTypeListQuery, DigitalContactTypeData>
    {
        public GetDigitalContactTypeListQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<DigitalContactTypeData>> ExecuteAsync(GetDigitalContactTypeListQuery query)
        {
            var entity = (await EntitySet.GetAsync<DigitalContactTypeEntity>()).OrderBy(dct => dct.PriorityOrder);
            return QueryResult.Create(entity, x => Mapper.Map<DigitalContactTypeData>(x));
        }
    }
}
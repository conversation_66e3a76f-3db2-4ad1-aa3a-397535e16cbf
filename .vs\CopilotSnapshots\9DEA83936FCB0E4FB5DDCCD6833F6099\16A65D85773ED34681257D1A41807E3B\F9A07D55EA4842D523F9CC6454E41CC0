﻿using System;
using Webaby;
using System.Linq;
using TinyCRM.Outbound.Prospect;
using TinyCRM.Outbound.ProspectAssignment;
using Webaby.Core.File;
using Webaby.Core.File.Queries;
using System.Collections.Generic;
using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.Outbound.Campaign.Queries
{
    public class GetCampaignByCampaignNameQuery : QueryBase<CampaignData>
    {
        public string CampaignName { get; set; }
    }

    internal class GetCampaignByCampaignNameQueryHandler : QueryHandlerBase<GetCampaignByCampaignNameQuery, CampaignData>
    {
        public GetCampaignByCampaignNameQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<CampaignData>> ExecuteAsync(GetCampaignByCampaignNameQuery query)
        {
            var campaign = await EntitySet.Get<CampaignEntity>().FirstOrDefaultAsync(x => x.CampaignName == query.CampaignName);
            var campaignData = Mapper.Map<CampaignData>(campaign);
            return new QueryResult<CampaignData>(campaignData);
        }
    }
}
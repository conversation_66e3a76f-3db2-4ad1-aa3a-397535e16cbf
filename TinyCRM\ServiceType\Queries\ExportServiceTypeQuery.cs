﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using TinyCRM.ServiceCategory;
using Webaby;
using Webaby.Core.UserAccount;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.Workflow.Queries
{
    public class ExportServiceTypeQuery : QueryBase<ExportServiceTypeQuery.Result>
    {
        public class Result
        {
            public string Script { get; set; }
        }
        public Guid ServiceTypeId { get; set; }
    }

    internal class ExportServiceTypeQueryHandle : QueryHandlerBase<ExportServiceTypeQuery, ExportServiceTypeQuery.Result>
    {
        public ExportServiceTypeQueryHandle(IEntitySet entitySet, IRepository repository, IText text, IMapper mapper)
            : base(entitySet, repository, text, mapper) { }

        public override async Task<QueryResult<ExportServiceTypeQuery.Result>> ExecuteAsync(ExportServiceTypeQuery query)
        {
            var cmd = EntitySet.CreateDbCommand();
            DbParameterHelper.AddNullableGuid(cmd, "@ServiceTypeId", query.ServiceTypeId);
            cmd.CommandText = "dbo.ExportServiceType";
            cmd.CommandType = CommandType.StoredProcedure;
            var dbSet = await EntitySet.ExecuteReadCommandAsync(cmd);
            List<ExportServiceTypeQuery.Result> result = new List<ExportServiceTypeQuery.Result>();
            foreach (DataTable table in dbSet.Tables)
            {
                result.Add(new ExportServiceTypeQuery.Result
                {
                    Script = table.Rows[0][0].ToString()
                });
            }
            return new QueryResult<ExportServiceTypeQuery.Result>(result);
        }
    }
}
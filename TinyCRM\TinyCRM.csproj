﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup Label="Globals">
    <SccProjectName>SAK</SccProjectName>
    <SccProvider>SAK</SccProvider>
    <SccAuxPath>SAK</SccAuxPath>
    <SccLocalPath>SAK</SccLocalPath>
  </PropertyGroup>
  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>disable</Nullable>
  </PropertyGroup>
  <ItemGroup>
    <Compile Remove="**\*.cs" />
    <Compile Remove="Appeal\**" />
    <Compile Remove="BusinessResult\Validators\**" />
    <Compile Remove="DetailPayment\**" />
    <Compile Remove="DynamicTable\**" />
    <Compile Remove="EastSpring\**" />
    <Compile Remove="Endorsement\**" />
    <Compile Remove="FlightRoute\**" />
    <Compile Remove="GanttProject\Command\**" />
    <Compile Remove="GanttProject\Queries\**" />
    <Compile Remove="NotificationCase\NotificationCaseServices\**" />
    <Compile Remove="NotificationCase\Tasks\**" />
    <Compile Remove="obj\**" />
    <Compile Remove="Organization\**" />
    <Compile Remove="Outbound\Campaign\Commands\**" />
    <Compile Remove="Outbound\Campaign\Events\**" />
    <Compile Remove="Outbound\Campaign\Tasks\**" />
    <Compile Remove="PaymentRequest\**" />
    <Compile Remove="Phase\EventHandlers\**" />
    <Compile Remove="Phase\Events\**" />
    <Compile Remove="Query\Command\**" />
    <Compile Remove="Query\Queries\**" />
    <Compile Remove="RequestTicket\Events\**" />
    <Compile Remove="RequestTicket\Validators\**" />
    <Compile Remove="Sms\Events\**" />
    <Compile Remove="TripRoute\**" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Remove="Appeal\**" />
    <EmbeddedResource Remove="BusinessResult\Validators\**" />
    <EmbeddedResource Remove="DetailPayment\**" />
    <EmbeddedResource Remove="DynamicTable\**" />
    <EmbeddedResource Remove="EastSpring\**" />
    <EmbeddedResource Remove="Endorsement\**" />
    <EmbeddedResource Remove="FlightRoute\**" />
    <EmbeddedResource Remove="GanttProject\Command\**" />
    <EmbeddedResource Remove="GanttProject\Queries\**" />
    <EmbeddedResource Remove="NotificationCase\NotificationCaseServices\**" />
    <EmbeddedResource Remove="NotificationCase\Tasks\**" />
    <EmbeddedResource Remove="obj\**" />
    <EmbeddedResource Remove="Organization\**" />
    <EmbeddedResource Remove="Outbound\Campaign\Commands\**" />
    <EmbeddedResource Remove="Outbound\Campaign\Events\**" />
    <EmbeddedResource Remove="Outbound\Campaign\Tasks\**" />
    <EmbeddedResource Remove="PaymentRequest\**" />
    <EmbeddedResource Remove="Phase\EventHandlers\**" />
    <EmbeddedResource Remove="Phase\Events\**" />
    <EmbeddedResource Remove="Query\Command\**" />
    <EmbeddedResource Remove="Query\Queries\**" />
    <EmbeddedResource Remove="RequestTicket\Events\**" />
    <EmbeddedResource Remove="RequestTicket\Validators\**" />
    <EmbeddedResource Remove="Sms\Events\**" />
    <EmbeddedResource Remove="TicketHotButton\Commands\**" />
    <EmbeddedResource Remove="TripRoute\**" />
    <EmbeddedResource Remove="UserAccount\ActiveDirectory\**" />
    <None Remove="Appeal\**" />
    <None Remove="BusinessResult\Validators\**" />
    <None Remove="DetailPayment\**" />
    <None Remove="DynamicTable\**" />
    <None Remove="EastSpring\**" />
    <None Remove="Endorsement\**" />
    <None Remove="FlightRoute\**" />
    <None Remove="GanttProject\Command\**" />
    <None Remove="GanttProject\Queries\**" />
    <None Remove="NotificationCase\NotificationCaseServices\**" />
    <None Remove="NotificationCase\Tasks\**" />
    <None Remove="obj\**" />
    <None Remove="Organization\**" />
    <None Remove="Outbound\Campaign\Commands\**" />
    <None Remove="Outbound\Campaign\Events\**" />
    <None Remove="Outbound\Campaign\Tasks\**" />
    <None Remove="PaymentRequest\**" />
    <None Remove="Phase\EventHandlers\**" />
    <None Remove="Phase\Events\**" />
    <None Remove="Query\Command\**" />
    <None Remove="Query\Queries\**" />
    <None Remove="RequestTicket\Events\**" />
    <None Remove="RequestTicket\Validators\**" />
    <None Remove="Sms\Events\**" />
    <None Remove="TicketHotButton\Commands\**" />
    <None Remove="TripRoute\**" />
    <None Remove="UserAccount\ActiveDirectory\**" />
  </ItemGroup>
  <ItemGroup>    <Compile Include="AutomaticTask\Queries\AutoNextTaskData.cs" />    <Compile Include="Channel\Commands\CreateEditChannelCommand.cs" />    <Compile Include="Channel\Queries\ChannelData.cs" />    <Compile Include="Channel\Queries\GetChannelByIdQuery.cs" />    <Compile Include="Channel\Queries\SearchChannelQuery.cs" />    <Compile Include="ContentTemplate\Queries\ContentTemplateData.cs" />    <Compile Include="Customer\Queries\GetCustomerByIdQuery.cs" />
    <Compile Include="DigitalChannel\Queries\DigitalChannelData.cs" />
    <Compile Include="DynamicDefinedTable\Commands\CreateEditDynamicDefinedTableCellValueListCommand.cs" />
    <Compile Include="DynamicDefinedTable\Commands\UpdateEntityLinkByLinkedTicketColumnCommand.cs" />
    <Compile Include="DynamicDefinedTable\Queries\DynamicDefinedTableCellValueData.cs" />
    <Compile Include="DynamicDefinedTable\Queries\DynamicDefinedTableColumnData.cs" />
    <Compile Include="DynamicDefinedTable\Queries\DynamicDefinedTableFileCellValue.cs" />
    <Compile Include="DynamicDefinedTable\Queries\GetDynamicDefinedTableCellValueListByDynamicFieldValueIdQuery.cs" />
    <Compile Include="DynamicDefinedTable\Queries\GetDynamicDefinedTableColumnByIdQuery.cs" />
    <Compile Include="DynamicDefinedTable\Queries\GetDynamicDefinedTableColumnListByTableSchemaQuery.cs" />
    <Compile Include="DynamicDefinedTable\Queries\GetDynamicDefinedTableLinkedTicketColumnListByColumnIdQuery.cs" />
    <Compile Include="DynamicDefinedTable\Queries\GetDynamicDefinedTableSchemaQuery.cs" />
    <Compile Include="Enums\AssignmentStatus.cs" />
    <Compile Include="Enums\Behavior.cs" />
    <Compile Include="Enums\CampaignStatus.cs" />
    <Compile Include="Enums\CampaignType.cs" />
    <Compile Include="Enums\Channel.cs" />
    <Compile Include="Enums\CheckStatus.cs" />
    <Compile Include="Enums\CustomerType.cs" />
    <Compile Include="Enums\Difficulty.cs" />
    <Compile Include="Enums\DigitalMessageResultCode.cs" />
    <Compile Include="Enums\DigitalMessageStatus.cs" />
    <Compile Include="Enums\ExchangeDataCompareOptions.cs" />
    <Compile Include="Enums\ExchangeSourceType.cs" />
    <Compile Include="Enums\Gender.cs" />
    <Compile Include="Enums\GeolocationType.cs" />
    <Compile Include="Enums\OrganizationType.cs" />
    <Compile Include="Enums\PartEnum.cs" />
    <Compile Include="Enums\PrimaryCustomer.cs" />
    <Compile Include="Enums\RequestTicketStatus.cs" />
    <Compile Include="Enums\RetrievalStatus.cs" />
    <Compile Include="Enums\SchedulerType.cs" />
    <Compile Include="Enums\ServiceCategoryType.cs" />
    <Compile Include="Enums\TaskEnum.cs" />
    <Compile Include="Enums\TaskTypeEnum.cs" />
    <Compile Include="Enums\UsedStatus.cs" />
    <Compile Include="Enums\WorkMode.cs" />
    <Compile Include="Mail\MailStatus.cs" />
    <Compile Include="Notification\Events\NotificationEvent.cs" />
    <Compile Include="Notification\Tasks\NotificationScheduledTask.cs" />
    <Compile Include="Outbound\Campaign\Queries\CampaignData.cs" />
    <Compile Include="Outbound\UserAccount\Roles.cs" />
    <Compile Include="RequestTicket\Queries\GetRequestTicketByCodeQuery.cs" />
    <Compile Include="RequestTicket\Queries\GetRequestTicketByIdQuery.cs" />
    <Compile Include="RequestTicket\Queries\RequestTicketData.cs" />
    <Compile Include="RequestTicket\RequestTicketCreateEditDynamicModel.cs" />
    <Compile Include="Retrieval\Audit\RetrievalAuditField.cs" />
    <Compile Include="ServiceType\Commands\CloneServiceTypeCommand.cs" />
    <Compile Include="ServiceType\Commands\DeleteServiceTypeCommand.cs" />
    <Compile Include="ServiceType\Commands\ImportServiceTypeCommandCommand.cs" />
    <Compile Include="ServiceType\Commands\InsertServiceTypeCommand.cs" />
    <Compile Include="ServiceType\Commands\UpdateServiceTypeCommand.cs" />
    <Compile Include="ServiceType\Events\ServiceTypeDeleteEvent.cs" />
    <Compile Include="ServiceType\Queries\CheckPermissionQuery.cs" />
    <Compile Include="ServiceType\Queries\ExpenseItemLinkServiceTypeQuery.cs" />
    <Compile Include="ServiceType\Queries\ExportServiceTypeQuery.cs" />
    <Compile Include="ServiceType\Queries\GetAllServiceTypeDataQuery.cs" />
    <Compile Include="ServiceType\Queries\GetBusinessResultByServiceTypeId.cs" />
    <Compile Include="ServiceType\Queries\GetLandingServiceTypeByServiceTypeIdQuery.cs" />
    <Compile Include="ServiceType\Queries\GetLandingServiceTypeListQuery.cs" />
    <Compile Include="ServiceType\Queries\GetRequestTicketExportDetailMappingByServiceTypeQuery.cs" />
    <Compile Include="ServiceType\Queries\GetServiceTypeByDueTimeIdQuery.cs" />
    <Compile Include="ServiceType\Queries\GetServiceTypeByDynamicFieldNameQuery.cs" />
    <Compile Include="ServiceType\Queries\GetServiceTypeByDynamicFormIdQuery.cs" />
    <Compile Include="ServiceType\Queries\GetServiceTypeByIdQuery.cs" />
    <Compile Include="ServiceType\Queries\GetServiceTypeByLevelQuery.cs" />
    <Compile Include="ServiceType\Queries\GetServiceTypeDataByLevelQuery.cs" />
    <Compile Include="ServiceType\Queries\GetServiceTypeDataByWorkflowIdQuery.cs" />
    <Compile Include="ServiceType\Queries\GetServiceTypeForLandingQuery.cs" />
    <Compile Include="ServiceType\Queries\GetServiceTypeTreeQuery.cs" />
    <Compile Include="ServiceType\Queries\SearchServiceTypeByTextQuery.cs" />
    <Compile Include="ServiceType\Queries\SearchServiceTypeQuery.cs" />
    <Compile Include="SMSLog\Commands\CreateEditSMSLogCommand.cs" />
    <Compile Include="SMSLog\Queries\GetSMSLogListQuery.cs" />
    <Compile Include="Sms\Commands\CreateEditGatewayCommand.cs" />
    <Compile Include="Sms\Commands\CreateEditSmsCommand.cs" />
    <Compile Include="Sms\Commands\DeleteGatewayCommand.cs" />
    <Compile Include="Sms\Queries\GatewayListItem.cs" />
    <Compile Include="Sms\Queries\GetGatewayByIdQuery.cs" />
    <Compile Include="Sms\Queries\GetGatewayDefaultQuery.cs" />
    <Compile Include="Sms\Queries\GetNewSmsListQuery.cs" />
    <Compile Include="Sms\Queries\SearchGatewayQuery.cs" />
    <Compile Include="Sms\SmsStatus.cs" />
    <Compile Include="Survey\Commands\CreateEditSurveyAnswerCommand.cs" />
    <Compile Include="Survey\Commands\CreateEditSurveyAnswerSuiteAnswerCommand.cs" />
    <Compile Include="Survey\Commands\CreateEditSurveyAnswerSuiteCommand.cs" />
    <Compile Include="Survey\Commands\CreateEditSurveyCampaignCommand.cs" />
    <Compile Include="Survey\Commands\CreateEditSurveyCampaignExecutionCommand.cs" />
    <Compile Include="Survey\Commands\CreateEditSurveyCampaignResponseExecutionCommand.cs" />
    <Compile Include="Survey\Commands\CreateEditSurveyCommand.cs" />
    <Compile Include="Survey\Commands\CreateEditSurveyFeedbackCommand.cs" />
    <Compile Include="Survey\Commands\CreateEditSurveyQuestionCommand.cs" />
    <Compile Include="Survey\Commands\CreateEditSurveyQuestionSectionCommand.cs" />
    <Compile Include="Survey\Commands\CreateSurveyFeedbackCommand.cs" />
    <Compile Include="Survey\Commands\CreateTicketWorkCommand.cs" />
    <Compile Include="Survey\Commands\DeleteSurveyAnswerCommand.cs" />
    <Compile Include="Survey\Commands\DeleteSurveyAnswerSuiteAnswerCommand.cs" />
    <Compile Include="Survey\Commands\DeleteSurveyAnswerSuiteCommand.cs" />
    <Compile Include="Survey\Commands\DeleteSurveyCampaignCommand.cs" />
    <Compile Include="Survey\Commands\DeleteSurveyCampaignExecutionCommand.cs" />
    <Compile Include="Survey\Commands\DeleteSurveyCampaignResponseExecutionCommand.cs" />
    <Compile Include="Survey\Commands\DeleteSurveyQuestionCommand.cs" />
    <Compile Include="Survey\Commands\DeleteSurveyQuestionSectionCommand.cs" />
    <Compile Include="Survey\Commands\ProcessSurveyCampaignResponseExecutionCommand.cs" />
    <Compile Include="Survey\Commands\SubmitAnwserSurrveyAllCommand.cs" />
    <Compile Include="Survey\Commands\SubmitAnwserSurrveyCommand.cs" />
    <Compile Include="Survey\Commands\SubmitSurveyFeedbackCommand.cs" />
    <Compile Include="Survey\Commands\UpdateAssignmentStatusCommand.cs" />
    <Compile Include="Survey\Queries\CampaignSurveyCampaignInfo.cs" />
    <Compile Include="Survey\Queries\GetAgentDoSurveyByServiceTypeQuery.cs" />
    <Compile Include="Survey\Queries\GetAllSurveyQuery.cs" />
    <Compile Include="Survey\Queries\GetCampaignInfoByCampaignExecutionServiceTypeIdQuery.cs" />
    <Compile Include="Survey\Queries\GetCampaignSurveyQuestionListQuery.cs" />
    <Compile Include="Survey\Queries\GetDistributePlanQuery.cs" />
    <Compile Include="Survey\Queries\GetLoopNextQuestionListQuery.cs" />
    <Compile Include="Survey\Queries\GetQuestionListByAnswerSuiteInCampaignQuery.cs" />
    <Compile Include="Survey\Queries\GetSurrveyAnswerListInSuiteQuery.cs" />
    <Compile Include="Survey\Queries\GetSurrveyAnswersByQuestionQuery.cs" />
    <Compile Include="Survey\Queries\GetSurrveyQuestionItemsBySurveyIdQuery.cs" />
    <Compile Include="Survey\Queries\GetSurrveyQuestionsBySectionIdQuery.cs" />
    <Compile Include="Survey\Queries\GetSurrveyQuestionsBySurveyIdQuery.cs" />
    <Compile Include="Survey\Queries\GetSurveyAnswerByIdQuery.cs" />
    <Compile Include="Survey\Queries\GetSurveyAnswerSuiteByIdQuery.cs" />
    <Compile Include="Survey\Queries\GetSurveyAnswerSuiteListQuery.cs" />
    <Compile Include="Survey\Queries\GetSurveyByIdQuery.cs" />
    <Compile Include="Survey\Queries\GetSurveyCampaignByCampaignIdQuery.cs" />
    <Compile Include="Survey\Queries\GetSurveyCampaignByIdQuery.cs" />
    <Compile Include="Survey\Queries\GetSurveyCampaignByServiceTypeQuery.cs" />
    <Compile Include="Survey\Queries\GetSurveyCampaignExecutionByIdQuery.cs" />
    <Compile Include="Survey\Queries\GetSurveyCampaignExecutionBySurveyCampaignIdQuery.cs" />
    <Compile Include="Survey\Queries\GetSurveyCampaignExecutionResponseBySurveyCampaignIdQuery.cs" />
    <Compile Include="Survey\Queries\GetSurveyCampaignItemListQuery.cs" />
    <Compile Include="Survey\Queries\GetSurveyCampaignQuery.cs" />
    <Compile Include="Survey\Queries\GetSurveyCampaignResponseExecutionByIdQuery.cs" />
    <Compile Include="Survey\Queries\GetSurveyFeedbackByCodeQuery.cs" />
    <Compile Include="Survey\Queries\GetSurveyFeedbackByRequestTicketIdQuery.cs" />
    <Compile Include="Survey\Queries\GetSurveyFeedbackExpiredDate.cs" />
    <Compile Include="Survey\Queries\GetSurveyFeedbackListByCustomerQuery.cs" />
    <Compile Include="Survey\Queries\GetSurveyListQuery.cs" />
    <Compile Include="Survey\Queries\GetSurveyQuestionAndAnswerQuery.cs" />
    <Compile Include="Survey\Queries\GetSurveyQuestionByAnswerQuery.cs" />
    <Compile Include="Survey\Queries\GetSurveyQuestionByIdQuery.cs" />
    <Compile Include="Survey\Queries\GetSurveyQuestionSectionByIdQuery.cs" />
    <Compile Include="Survey\Queries\GetSurveyQuestionSectionListQuery.cs" />
    <Compile Include="Survey\Queries\GetSurveyReportQuery.cs" />
    <Compile Include="Survey\Queries\GetSurveyResultQuery.cs" />
    <Compile Include="Survey\Queries\GetSurveyWithAnswerSuiteReportQuery.cs" />
    <Compile Include="Survey\Queries\GetTargetSurveyQuery.cs" />
    <Compile Include="Survey\Queries\GetTicketOwnerSettingFromSurveyFeedbackQuery.cs" />
    <Compile Include="Survey\Queries\PreviewSurveyResultQuery.cs" />
    <Compile Include="Survey\Queries\SearchCampaignWorkerQuery.cs" />
    <Compile Include="Survey\Queries\SurveyCampaignData.cs" />
    <Compile Include="Survey\SurveyEnum.cs" />
    <Compile Include="TaskType\Commands\CreateEditContentTemplateListInTaskTypeCommand.cs" />
    <Compile Include="TaskType\Commands\CreateEditOrganizationTaskTypeCommand.cs" />
    <Compile Include="TaskType\Commands\CreateEditTaskTypeCommand.cs" />
    <Compile Include="TaskType\Commands\DeleteTaskTypeCommand.cs" />
    <Compile Include="TaskType\Commands\SetAssignmentPriorityOrganizationCommand.cs" />
    <Compile Include="TaskType\Queries\ExportFormQuery.cs" />
    <Compile Include="TaskType\Queries\GetAllDynamicFieldRelatedTaskTypeQuery.cs" />
    <Compile Include="TaskType\Queries\GetContentTemplateByTaskTypeIdQuery.cs" />
    <Compile Include="TaskType\Queries\GetOrganizationTypeQuery.cs" />
    <Compile Include="TaskType\Queries\GetTaskTypeAssignmentPriorityOrganizationQuery.cs" />
    <Compile Include="TaskType\Queries\GetTaskTypeByIdQuery.cs" />
    <Compile Include="TaskType\Queries\GetTaskTypeByTaskIdQuery.cs" />
    <Compile Include="TaskType\Queries\GetTaskTypeForOrganizationQuery.cs" />
    <Compile Include="TaskType\Queries\GetTaskTypeForTicketQuery.cs" />
    <Compile Include="TaskType\Queries\GetTaskTypeInWorkflowByServiceTypeIdQuery.cs" />
    <Compile Include="TaskType\Queries\GetTaskTypeListByServiceTypeQuery.cs" />
    <Compile Include="TaskType\Queries\GetTaskTypeListByWorkflowQuery.cs" />
    <Compile Include="TaskType\Queries\GetTaskTypeNotificationEventQuery.cs" />
    <Compile Include="TaskType\Queries\GetUserAssignByTaskTypeIdQuery.cs" />
    <Compile Include="TaskType\Queries\GetWorkflowListByTaskTypeIdQuery.cs" />
    <Compile Include="TaskType\Queries\GetWorkflowTaskTypeListQuery.cs" />
    <Compile Include="TaskType\Queries\SearchTaskTypeQuery.cs" />
    <Compile Include="TaskType\Queries\TaskTypeData.cs" />
    <Compile Include="TaskType\Queries\TaskTypeInWorkflowInfo.cs" />
    <Compile Include="TaskType\Queries\TaskTypeNotificationEventData.cs" />
    <Compile Include="TaskType\Queries\TaskTypeReportListQuery.cs" />
    <Compile Include="Tax\Commands\CreateEditTaxCommand.cs" />
    <Compile Include="Tax\Commands\DeleteTaxCommand.cs" />
    <Compile Include="Tax\Queries\GetTaxByIdQuery.cs" />
    <Compile Include="Tax\Queries\GetTaxListQuery.cs" />
    <Compile Include="TbCallback\Commands\CreateEditTbCallbackCallCommand.cs" />
    <Compile Include="TbCallback\Commands\CreateEditTbCallbackCommand.cs" />
    <Compile Include="TbCallback\Queries\GetTbCallbackByIdQuery.cs" />
    <Compile Include="TbCallback\Queries\GetTbCallbackByReferenceObjectIdQuery.cs" />
    <Compile Include="TbCallback\Queries\GetTbCallbackCallHistoriesQuery.cs" />
    <Compile Include="TbCallback\Queries\GetTbCallbackCallResultQuery.cs" />
    <Compile Include="TbCallback\Queries\TbCallbackData.cs" />
    <Compile Include="TemplateLibrary\Queries\GetDynamicFieldDefinitionsQuery.cs" />
    <Compile Include="TemplateLibrary\Queries\GetTemplateLibraryItemByRequestTicketQuery.cs" />
    <Compile Include="TicketHotButton\Queries\GetTicketHotButtonByIdQuery.cs" />
    <Compile Include="TicketHotButton\Queries\GetTicketHotButtonListByCustomerQuery.cs" />
    <Compile Include="TicketHotButton\Queries\GetTicketHotButtonListQuery.cs" />    <Compile Include="TicketHotButton\Queries\TicketHotButtonData.cs" />    <Compile Include="AutoMapperProfile.cs" />
    <Compile Include="NotificationDefinitionContext.cs" />
    <Compile Include="AutomaticTask\AutoConditionEntity.cs" />
    <Compile Include="AutomaticTask\AutomaticTaskConstants.cs" />
    <Compile Include="AutomaticTask\AutoNextTaskEntity.cs" />
    <Compile Include="AutomaticTask\AutoNextTaskErrorLogEntity.cs" />
    <Compile Include="AutomaticTask\UserPathSelectorEntity.cs" />
    <Compile Include="AutomaticTask\Queries\AutoConditionData.cs" />
    <Compile Include="AutomaticTask\Queries\AutoNextTaskErrorLogData.cs" />
    <Compile Include="AutomaticTask\Queries\RequestTicketFirstTaskAutoNextTaskErrorItem.cs" />
    <Compile Include="AutomaticTask\Queries\UserPathSelectorData.cs" />
    <Compile Include="Behavior\BehaviorEntity.cs" />
    <Compile Include="Behavior\Queries\BehaviorData.cs" />
    <Compile Include="Building\BuildingData.cs" />
    <Compile Include="Building\BuildingEntity.cs" />
    <Compile Include="BuiltInCommand\BuiltInCommandInfo.cs" />
    <Compile Include="BusinessPermission\BusinessPermissionContants.cs" />
    <Compile Include="BusinessResult\BusinessResultEntity.cs" />
    <Compile Include="BusinessResult\Queries\BusinessResultData.cs" />
    <Compile Include="Callback\CallbackDetailEntity.cs" />
    <Compile Include="Callback\CallbackEntity.cs" />
    <Compile Include="Callback\CallbackSettingsEntity.cs" />
    <Compile Include="Campaign\CampaignAssignmentEntity.cs" />
    <Compile Include="Campaign\CampaignDfoContactEntity.cs" />
    <Compile Include="Campaign\CampaignLinkEntity.cs" />
    <Compile Include="Campaign\CampaignWorkEntity.cs" />
    <Compile Include="Campaign\CampaignWorkerEntity.cs" />
    <Compile Include="Campaign\Queries\CampaignAssignmentData.cs" />
    <Compile Include="Campaign\Queries\CampaignListItem.cs" />
    <Compile Include="Campaign\Queries\CampaignWorkData.cs" />
    <Compile Include="Campaign\Queries\CampaignWorkerData.cs" />
    <Compile Include="Campaign\Queries\CampaignWorkerListItem.cs" />
    <Compile Include="Campaign\Queries\CampaignWorkSummaryInfo.cs" />
    <Compile Include="Campaign\Queries\TicketAssignmentListItem.cs" />
    <Compile Include="Campaign\Queries\WorkCustomerListItem.cs" />
    <Compile Include="Campaign\Queries\WorkTicketListItem.cs" />
    <Compile Include="Channel\ChannelEntity.cs" />
    <Compile Include="ContentTemplate\AlternativeNotiChannelContentTemplateEntity.cs" />
    <Compile Include="ContentTemplate\AutoCompleteContentTemplateEntity.cs" />
    <Compile Include="ContentTemplate\ContentTemplateEntity.cs" />
    <Compile Include="ContentTemplate\ContentTemplateTaskTypeEntity.cs" />
    <Compile Include="ContentTemplate\Queries\AutoCompleteContentTemplateData.cs" />    <Compile Include="Customer\ClassificationChannelEntity.cs" />
    <Compile Include="Customer\ClassificationEntity.cs" />
    <Compile Include="Customer\CustomerAlternativeAddressEntity.cs" />
    <Compile Include="Customer\CustomerBillingCodeEntity.cs" />
    <Compile Include="Customer\CustomerEntity.cs" />
    <Compile Include="Customer\CustomerFieldConfigurationEntity.cs" />
    <Compile Include="Customer\CustomerVersioningEntity.cs" />
    <Compile Include="Customer\Queries\ClassificationData.cs" />
    <Compile Include="Customer\Queries\CustomerAlternativeAddressData.cs" />
    <Compile Include="Customer\Queries\CustomerAlternativeAddressItem.cs" />
    <Compile Include="Customer\Queries\CustomerAppartmentInfo.cs" />
    <Compile Include="Customer\Queries\CustomerBillingCodeData.cs" />
    <Compile Include="Customer\Queries\CustomerData.cs" />
    <Compile Include="Customer\Queries\CustomerDedupDefinition.cs" />
    <Compile Include="Customer\Queries\CustomerFieldConfigurationData.cs" />
    <Compile Include="Customer\Queries\CustomerHierarchySummaryData.cs" />
    <Compile Include="Customer\Queries\CustomerListItem.cs" />
    <Compile Include="CustomerContext\CustomerContextHistoryEntity.cs" />
    <Compile Include="CustomerContext\Queries\CustomerContextHistoryData.cs" />
    <Compile Include="CustomerVersionName\CustomerVersionNameEntity.cs" />
    <Compile Include="Dashboard\Bucket\BucketItem.cs" />
    <Compile Include="DigitalCampaign\Queries\CampaignAnonymousDigitalContactData.cs" />
    <Compile Include="DigitalCampaign\Queries\CampaignAnonymousDigitalContactRawData.cs" />
    <Compile Include="DigitalCampaign\Queries\CampaignCustomerDigitalContactData.cs" />
    <Compile Include="DigitalCampaign\Queries\CampaignCustomerDigitalContactRawData.cs" />
    <Compile Include="DigitalCampaign\Queries\CampaignPushCodeInfoForCostEstimationData.cs" />
    <Compile Include="DigitalChannel\DigitalChannelEntity.cs" />
    <Compile Include="DigitalChannel\DigitalContactTypeEntity.cs" />
    <Compile Include="DigitalChannel\DigitalServiceConfigurationEntity.cs" />
    <Compile Include="DigitalChannel\Queries\DigitalContactTypeData.cs" />
    <Compile Include="DigitalChannelMessageTemplate\DigitalChannelMessageTemplateEntity.cs" />
    <Compile Include="DigitalChannelMessageTemplate\Queries\DigitalChannelMessageTemplateData.cs" />
    <Compile Include="DigitalContact\DigitalContactData.cs" />
    <Compile Include="DigitalContact\DigitalContactEntity.cs" />
    <Compile Include="DigitalContact\ProspectDigitalContactEntity.cs" />
    <Compile Include="DigitalContact\Queries\CustomerDigitalContactListItem.cs" />
    <Compile Include="DigitalContact\Queries\CustomerDigitalContactSummaryItem.cs" />
    <Compile Include="DigitalContact\Queries\DigitalContactListItem.cs" />    <Compile Include="DigitalMessage\DigitalDeliverMessagesData.cs" />
    <Compile Include="DigitalMessage\DigitalDeliverMessagesHistoryData.cs" />
    <Compile Include="DigitalMessage\DigitalMessageEntity.cs" />
    <Compile Include="DigitalMessage\DigitalMessageLogEntity.cs" />
    <Compile Include="DigitalMessageTemplate\DigitalMessageTemplateEntity.cs" />
    <Compile Include="DigitalPushCode\DigitalPushCodeEntity.cs" />
    <Compile Include="DigitalPushCode\DigitalPushCodeOnChannelEntity.cs" />
    <Compile Include="DigitalPushCode\Queries\DigitalPushCodeData.cs" />
    <Compile Include="DigitalPushCode\Queries\DigitalPushCodeOnChannelData.cs" />
    <Compile Include="DigitalPushCode\Queries\DigitalPushCodeOnChannelInfo.cs" />
    <Compile Include="DynamicDefinedTable\DynamicDefinedTableCellValueEntity.cs" />
    <Compile Include="DynamicDefinedTable\DynamicDefinedTableColumnEntity.cs" />
    <Compile Include="DynamicDefinedTable\DynamicDefinedTableColumnOnDynamicFormEntity.cs" />
    <Compile Include="DynamicDefinedTable\DynamicDefinedTableLinkedTicketColumnEntity.cs" />
    <Compile Include="DynamicDefinedTable\DynamicDefinedTableSchemaEntity.cs" />
    <Compile Include="DynamicDefinedTable\Queries\DynamicDefinedTableLinkedTicketColumnData.cs" />
    <Compile Include="DynamicDefinedTable\Queries\DynamicDefinedTableSchemaData.cs" />
    <Compile Include="ECommerce\ECommerceUploadedFileEntity.cs" />
    <Compile Include="EntityLink\EntityLinkBusinessSpecificEntity.cs" />
    <Compile Include="EntityLink\EntityLinkEntity.cs" />
    <Compile Include="EntityLink\Queries\EntityLinkBusinessSpecificData.cs" />
    <Compile Include="EntityLink\Queries\EntityLinkData.cs" />
    <Compile Include="EntityLink\Queries\GeneralLinkEntityListData.cs" />
    <Compile Include="ExpenseItem\ExpenseItemEntity.cs" />
    <Compile Include="ExternalApiEndpoint\ExternalApiEndpointEntity.cs" />
    <Compile Include="ExternalApiEndpoint\Queries\ExternalApiEndpointData.cs" />    <Compile Include="FeeCategory\FeeCategoryEntity.cs" />
    <Compile Include="FeeCategory\FeeCategoryFormulaEntity.cs" />
    <Compile Include="FeeCategory\FeeCategoryTaxEntity.cs" />
    <Compile Include="FeeCategory\Queries\FeeCategoryData.cs" />
    <Compile Include="FeeCategory\Queries\FeeCategoryFormulaData.cs" />
    <Compile Include="FeeCategory\Queries\FeeCategoryTaxData.cs" />
    <Compile Include="FeeCategory\Queries\FeeCategoryTaxInfo.cs" />    <Compile Include="FeeCategoryPart\FeeCategoryPartEntity.cs" />
    <Compile Include="FeeCategoryPart\Queries\FeeCategoryPartData.cs" />
    <Compile Include="FeeCategoryPart\Queries\FeeCategoryPartInfo.cs" />    <Compile Include="FinishRequestTicketByImport\DoneRequestTicketImportSessionEntity.cs" />
    <Compile Include="FinishRequestTicketByImport\Queries\DoneRequestTicketImportSessionData.cs" />
    <Compile Include="FinishRequestTicketByImport\Queries\DoneRequestTicketImportSessionResultItem.cs" />
    <Compile Include="GanttProject\GanttProject_TaskDependencyEntity.cs" />
    <Compile Include="Geolocation\EmployeeGeolocationEntity.cs" />
    <Compile Include="Geolocation\GeolocationEntity.cs" />
    <Compile Include="Geolocation\Queries\GeolocationData.cs" />
    <Compile Include="GridDynamicField\DynamicFieldFinishStepConentGrid\DynamicFieldFinishStepConentListItemEntity.cs" />
    <Compile Include="GridDynamicField\DynamicFieldKnowledgeGrid\DynamicFieldKnowledgeListItemEntity.cs" />
    <Compile Include="GridDynamicField\DynamicFieldKnowledgeGrid\Queries\DynamicFieldKnowledgeListItemData.cs" />
    <Compile Include="GridDynamicField\DynamicFieldVerifierListItem\DynamicFieldVerifierListItemEntity.cs" />
    <Compile Include="GridDynamicField\DynamicFieldVerifierListItem\Queries\DynamicFieldVerifierListItemData.cs" />
    <Compile Include="GridDynamicField\DynamicFieldVerifyResultGrid\DynamicFieldVerifyResultListItemEntity.cs" />
    <Compile Include="GridDynamicField\DynamicFieldVerifyResultGrid\Queries\DynamicFieldVerifyResultListItemData.cs" />
    <Compile Include="Image\ImageEntity.cs" />
    <Compile Include="Import\ImportDataStatisticItem.cs" />
    <Compile Include="ImportB2BCustomersSession\ImportB2BCustomersSessionEntity.cs" />
    <Compile Include="ImportB2BCustomersSession\Queries\ImportB2BCustomersResultItem.cs" />
    <Compile Include="ImportCustomerRaw\ImportCustomerSessionEntity.cs" />
    <Compile Include="ImportCustomerSession\ImportCustomerSessionEntity.cs" />
    <Compile Include="ImportRequestTicket\ImportRequestTicketDataItemEntity.cs" />
    <Compile Include="ImportRequestTicket\ImportRequestTicketSessionCancelledEntity.cs" />
    <Compile Include="ImportRequestTicket\ImportRequestTicketSessionEntity.cs" />
    <Compile Include="ImportRequestTicket\Queries\ImportRequestTicketDataItemData.cs" />
    <Compile Include="ImportRequestTicket\Queries\ImportRequestTicketSessionData.cs" />
    <Compile Include="ImportRequestTicket\Queries\ImportRequestTicketSessionListItem.cs" />
    <Compile Include="ImportSOSession\ImportSOSessionEntity.cs" />
    <Compile Include="ImportSOSession\Queries\ImportSOResultItem.cs" />
    <Compile Include="ImportTask\ImportTaskDataItemEntity.cs" />
    <Compile Include="ImportTask\ImportTaskSessionCancelledEntity.cs" />
    <Compile Include="ImportTask\ImportTaskSessionEntity.cs" />
    <Compile Include="ImportTask\Queries\ImportTaskDataItemData.cs" />
    <Compile Include="ImportTask\Queries\ImportTaskSessionData.cs" />
    <Compile Include="ImportTask\Queries\ImportTaskSessionListItem.cs" />
    <Compile Include="InfoList\InfoListEntity.cs" />    <Compile Include="InfoList\Queries\InfoListData.cs" />
    <Compile Include="KnowledgeBase\KnowledgeItemEntity.cs" />
    <Compile Include="KnowledgeBase\KnowledgeReferenceEntity.cs" />    <Compile Include="KnowledgeBase\Queries\KnowledgeItem.cs" />
    <Compile Include="KnowledgeBase\Queries\KnowledgeItemApiData.cs" />
    <Compile Include="Mail\Mail.cs" />
    <Compile Include="Mail\MailEntity.cs" />
    <Compile Include="Mail\MailGateway.cs" />
    <Compile Include="Mail\MailLogEntity.cs" />
    <Compile Include="Mail\Queries\AttachmentFileInfo.cs" />
    <Compile Include="Mail\Queries\MailData.cs" />
    <Compile Include="Maintenance\MaintenanceData.cs" />
    <Compile Include="Maintenance\MaintenanceEntity.cs" />
    <Compile Include="Maintenance\MaintenanceTemplateGroupData.cs" />
    <Compile Include="Maintenance\MaintenanceTemplateGroupEntity.cs" />
    <Compile Include="Maintenance\MaintenanceTemplateItemData.cs" />
    <Compile Include="Maintenance\MaintenanceTemplateItemEntity.cs" />
    <Compile Include="MobileNotification\MobileApiResult.cs" />
    <Compile Include="MobileNotification\MobileNotificationData.cs" />
    <Compile Include="MobileNotification\MobileNotificationEntity.cs" />
    <Compile Include="MobileNotification\MobileNotificationPartCustomerEntity.cs" />
    <Compile Include="MobileNotification\MobileNotificationResultData.cs" />
    <Compile Include="MobileNotification\MobileNotificationResultEntity.cs" />
    <Compile Include="MobileNotification\Queries\MobileNotificationListItem.cs" />
    <Compile Include="MonthlyPartFee\MonthlyFeeDetailExportTemplateEntity.cs" />
    <Compile Include="MonthlyPartFee\MonthlyPartFeeDebitReminderEntity.cs" />
    <Compile Include="MonthlyPartFee\MonthlyPartFeeEntity.cs" />
    <Compile Include="MonthlyPartFee\MonthlyPartFeeFileEntity.cs" />
    <Compile Include="MonthlyPartFee\MonthlyPartFeeItemEntity.cs" />
    <Compile Include="MonthlyPartFee\MonthlyPartFeeItemTaxEntity.cs" />    <Compile Include="MonthlyPartFee\Events\MonthlyPartFeeCreatedEvent.cs" />
    <Compile Include="MonthlyPartFee\ExportServices\IMonthlyFeeDetailExport.cs" />    <Compile Include="MonthlyPartFee\Queries\MonthlyFeeDetailExportTemplateData.cs" />
    <Compile Include="MonthlyPartFee\Queries\MonthlyPartFeeData.cs" />
    <Compile Include="MonthlyPartFee\Queries\MonthlyPartFeeDebitReminderInfo.cs" />
    <Compile Include="MonthlyPartFee\Queries\MonthlyPartFeeExportItemInfo.cs" />
    <Compile Include="MonthlyPartFee\Queries\MonthlyPartFeeFileData.cs" />
    <Compile Include="MonthlyPartFee\Queries\MonthlyPartFeeInfo.cs" />
    <Compile Include="MonthlyPartFee\Queries\MonthlyPartFeeItemData.cs" />
    <Compile Include="MonthlyPartFee\Queries\MonthlyPartFeeItemInfo.cs" />    <Compile Include="MonthlyPartFeeBatch\MonthlyPartFeeBatchEntity.cs" />
    <Compile Include="MonthlyPartFeeBatch\MonthlyPartFeeBatchItemEntity.cs" />    <Compile Include="MonthlyPartFeeBatch\Queries\MonthlyPartFeeBatchData.cs" />
    <Compile Include="MonthlyPartFeeBatch\Queries\MonthlyPartFeeBatchItemData.cs" />
    <Compile Include="NotificationCase\DefaultNotificationChannelContentTemplates.cs" />
    <Compile Include="NotificationCase\INotificationCaseService.cs" />
    <Compile Include="NotificationCase\NotificationCaseEntity.cs" />
    <Compile Include="NotificationCase\Queries\NotificationCaseData.cs" />
    <Compile Include="NotificationChanelSetting\CustomNotificationChannelConditionServiceTypeEntity.cs" />
    <Compile Include="NotificationChanelSetting\CustomNotificationChannelSettingEntity.cs" />
    <Compile Include="NotificationChanelSetting\CustomNotificationChannelToUsersEntity.cs" />
    <Compile Include="NotificationChanelSetting\NotificationChanelSettingEntity.cs" />
    <Compile Include="NotificationChanelSetting\Queries\CustomNotificationChannelConditionServiceTypeData.cs" />
    <Compile Include="NotificationChanelSetting\Queries\CustomNotificationChannelSettingData.cs" />
    <Compile Include="NotificationChanelSetting\Queries\CustomNotificationChannelSettingListItem.cs" />
    <Compile Include="NotificationChanelSetting\Queries\CustomNotificationChannelToUsersData.cs" />
    <Compile Include="NotificationChanelSetting\Queries\NotificationChannelSettingData.cs" />
    <Compile Include="Order\OrderCancelReasonEntity.cs" />
    <Compile Include="Order\OrderDetailEntity.cs" />
    <Compile Include="Order\OrderEntity.cs" />
    <Compile Include="Order\OrderForwardStatusEntity.cs" />
    <Compile Include="Order\OrderPOSStatusEntity.cs" />
    <Compile Include="Order\OrderTypeEntity.cs" />
    <Compile Include="Order\OrderVESStatusEntity.cs" />
    <Compile Include="Order\PaymentMethodEntity.cs" />
    <Compile Include="Order\PaymentStatusEntity.cs" />
    <Compile Include="Order\SellChannelEntity.cs" />
    <Compile Include="Order\Queries\OrderData.cs" />
    <Compile Include="Order\Queries\OrderDetailData.cs" />
    <Compile Include="Outbound\SearchFieldConfig.cs" />
    <Compile Include="Outbound\AdditionalDataTemplate\AdditionalDataTemplateEntity.cs" />
    <Compile Include="Outbound\AdditionalDataTemplate\Queries\AdditionalDataTemplateInfo.cs" />
    <Compile Include="Outbound\AgentFieldSaleCouple\AgentFieldSaleCoupleData.cs" />
    <Compile Include="Outbound\AgentFieldSaleCouple\AgentFieldSaleCoupleEntity.cs" />
    <Compile Include="Outbound\AgentFieldSaleCouple\Queries\FieldSaleAppointmentInfo.cs" />
    <Compile Include="Outbound\Appointment\AppointmentEntity.cs" />
    <Compile Include="Outbound\Appointment\AppointmentImportResult.cs" />
    <Compile Include="Outbound\Appointment\Queries\AppointmentHistoryData.cs" />
    <Compile Include="Outbound\Appointment\Queries\AppointmentInfo.cs" />
    <Compile Include="Outbound\Appointment\Queries\OldMeetAppointmentFieldSale.cs" />
    <Compile Include="Outbound\Appointment\Queries\OldNotMeetAppointmentFieldSaleTeam.cs" />
    <Compile Include="Outbound\Appointment\Queries\SuggestionAppointmentFieldSale.cs" />
    <Compile Include="Outbound\Appointment\Queries\TeamAssignedSummryInfo.cs" />
    <Compile Include="Outbound\AppointmentResultCode\AppointmentResultCodeEntity.cs" />
    <Compile Include="Outbound\AppointmentResultImport\AppointmentExcelImportResult.cs" />
    <Compile Include="Outbound\Brand\BrandEntity.cs" />    <Compile Include="Outbound\Brand\Events\BrandCreatedEvent.cs" />
    <Compile Include="Outbound\Brand\Queries\BrandCompanyData.cs" />
    <Compile Include="Outbound\Brand\Queries\BrandData.cs" />
    <Compile Include="Outbound\CallResult\CallResultCallPlanStrategyEntity.cs" />
    <Compile Include="Outbound\CallResult\CallResultEntity.cs" />    <Compile Include="Outbound\CallResult\Queries\CallResultData.cs" />
    <Compile Include="Outbound\CallResult\Queries\CallResultListItem.cs" />
    <Compile Include="Outbound\CallResult\Queries\CallStrategyData.cs" />
    <Compile Include="Outbound\CallResult\Queries\ResultCodeSuiteData.cs" />    <Compile Include="Outbound\Campaign\CampaignCoreServiceCallbackEntity.cs" />
    <Compile Include="Outbound\Campaign\CampaignEntity.cs" />
    <Compile Include="Outbound\Campaign\CampaignExecutingTimeEntity.cs" />
    <Compile Include="Outbound\Campaign\CampaignParameterEntity.cs" />
    <Compile Include="Outbound\Campaign\CampaignTeamAssignmentEntity.cs" />
    <Compile Include="Outbound\Campaign\TableData.cs" />
    <Compile Include="Outbound\Campaign\Queries\AgentCampaignAssignmentListItem.cs" />
    <Compile Include="Outbound\Campaign\Queries\CampaignAssignedListItem.cs" />
    <Compile Include="Outbound\Campaign\Queries\CampaignCustomerDigitalContactListItem.cs" />
    <Compile Include="Outbound\Campaign\Queries\CampaignExecutingTimeData.cs" />
    <Compile Include="Outbound\Campaign\Queries\CampaignListItem.cs" />
    <Compile Include="Outbound\Campaign\Queries\CampaignParameterData.cs" />
    <Compile Include="Outbound\Campaign\Queries\CampaignSaleSupportListItem.cs" />
    <Compile Include="Outbound\Campaign\Queries\CampaignTeamAssignmentData.cs" />
    <Compile Include="Outbound\Campaign\Queries\CampaignTeamLeadListItem.cs" />
    <Compile Include="Outbound\Campaign\Queries\CampaignWorkSummaryInfo.cs" />
    <Compile Include="Outbound\CampaignCompletionCode\CampaignCompletionCodeEntity.cs" />    <Compile Include="Outbound\CampaignCompletionCode\Events\CampaignCompletionCodeCreatedEvent.cs" />
    <Compile Include="Outbound\CampaignTemplateCode\CampaignTemplateCodeEntity.cs" />    <Compile Include="Outbound\CampaignTemplateCode\Events\CompletionCategoryCreatedEvent.cs" />
    <Compile Include="Outbound\CampaignTemplateCode\Queries\CampaignTemplateCodeData.cs" />
    <Compile Include="Outbound\Company\CompanyEntity.cs" />    <Compile Include="Outbound\Company\Events\CompanyAddedEvent.cs" />
    <Compile Include="Outbound\Company\Queries\CompanyData.cs" />
    <Compile Include="Outbound\CompletionCategory\CompletionCategoryEntity.cs" />    <Compile Include="Outbound\CompletionCategory\Events\CompletionCategoryCreatedEvent.cs" />
    <Compile Include="Outbound\CompletionCategory\Queries\CompletionCategoryData.cs" />
    <Compile Include="Outbound\Contact\ContactEntity.cs" />
    <Compile Include="Outbound\Contact\Queries\ContactData.cs" />
    <Compile Include="Outbound\Contact\Queries\ContactDataListItem.cs" />
    <Compile Include="Outbound\Contact\Queries\ContactItem.cs" />
    <Compile Include="Outbound\Contact\Queries\ContactNotesHistory.cs" />
    <Compile Include="Outbound\Contact\Queries\EventContactItem.cs" />    <Compile Include="Outbound\ContactCall\ContactCallEntity.cs" />
    <Compile Include="Outbound\ContactCall\Queries\ContactCallData.cs" />
    <Compile Include="Outbound\ContactCall\Queries\ContactCallItem.cs" />
    <Compile Include="Outbound\ContactCallResultServiceCallback\ContactCallResultServiceCallbackEntity.cs" />    <Compile Include="Outbound\ContactCallResultServiceCallback\Queries\ContactCallResultServiceCallbackData.cs" />
    <Compile Include="Outbound\ContactRelationship\ContactRelationshipEntity.cs" />    <Compile Include="Outbound\ContactRelationship\Queries\ContactRelationshipData.cs" />
    <Compile Include="Outbound\Contract\ContractStatus.cs" />
    <Compile Include="Outbound\Contract\PolicyData.cs" />
    <Compile Include="Outbound\District\DistrictEntity.cs" />    <Compile Include="Outbound\District\Events\CreateEditDistrictEvent.cs" />
    <Compile Include="Outbound\District\Queries\DistrictProviceData.cs" />
    <Compile Include="Outbound\DynamicForm\DynamicFormValueListItem.cs" />
    <Compile Include="Outbound\ExcelFormula\ExcelFormulaEntity.cs" />
    <Compile Include="Outbound\ExcelFormula\Queries\ExcelFormulaData.cs" />
    <Compile Include="Outbound\ExcelImport\ColumnMappingEntity.cs" />
    <Compile Include="Outbound\ExcelImport\ContactRawEntity.cs" />
    <Compile Include="Outbound\ExcelImport\ImportSessionEntity.cs" />
    <Compile Include="Outbound\ExcelImport\Queries\CustomerImportReport.cs" />
    <Compile Include="Outbound\ExcelImport\Queries\CustomerImportResult.cs" />
    <Compile Include="Outbound\ExcelImport\Queries\ImportSessionData.cs" />
    <Compile Include="Outbound\HotListCampaignImportSession\HotListCampaignImportSessionEntity.cs" />    <Compile Include="Outbound\HotListCampaignImportSession\Queries\AgentDistributionItem.cs" />    <Compile Include="Outbound\HotListCampaignImportSession\Queries\ScanInputDataError.cs" />
    <Compile Include="Outbound\HotListCampaignImportSession\Queries\ScanInputStatistic.cs" />
    <Compile Include="Outbound\HotListGroup\HotListGroupEntity.cs" />    <Compile Include="Outbound\HotListGroup\Queries\HotListGroupData.cs" />
    <Compile Include="Outbound\HotListGroup\Queries\HotListGroupUserData.cs" />
    <Compile Include="Outbound\HotListGroupUser\HotListGroupEntity.cs" />
    <Compile Include="Outbound\ImportSession\ContactDedupEntity.cs" />
    <Compile Include="Outbound\ImportSession\ImportMappingSchema.cs" />
    <Compile Include="Outbound\ImportSession\ImportSessionEntity.cs" />    <Compile Include="Outbound\ImportSession\Events\ImportSessionCreatedEvent.cs" />    <Compile Include="Outbound\Lead\LeadEntity.cs" />
    <Compile Include="Outbound\Lead\LeadSummaryStatusEntity.cs" />
    <Compile Include="Outbound\Lead\Queries\LeadData.cs" />
    <Compile Include="Outbound\Lead\Queries\LeadItem.cs" />
    <Compile Include="Outbound\Lead\Queries\LeadListItem.cs" />
    <Compile Include="Outbound\LeadAssignment\LeadAssignmentEntity.cs" />
    <Compile Include="Outbound\LeadAssignment\Queries\LeadAssignmentData.cs" />
    <Compile Include="Outbound\Organization\OrganizationTypes.cs" />
    <Compile Include="Outbound\Organization\Queries\FieldSaleTeamInfo.cs" />
    <Compile Include="Outbound\ParentContact\ParentContactEntity.cs" />
    <Compile Include="Outbound\ParentContact\Queries\ParentContactData.cs" />
    <Compile Include="Outbound\PaymentAdjustment\PaymentAdjustmentEntity.cs" />
    <Compile Include="Outbound\Product\ProductInCampaign\ProductInCampaignEntity.cs" />
    <Compile Include="Outbound\Prospect\ProspectEntity.cs" />
    <Compile Include="Outbound\Prospect\Queries\AgentAssignmentInfo.cs" />
    <Compile Include="Outbound\Prospect\Queries\HotListImportedResultItem.cs" />
    <Compile Include="Outbound\Prospect\Queries\ProspectData.cs" />
    <Compile Include="Outbound\Prospect\Queries\ProspectSummary.cs" />
    <Compile Include="Outbound\Prospect\Queries\ProspectSummaryInfo.cs" />
    <Compile Include="Outbound\Prospect\Queries\TeamContactNumberAssignment.cs" />
    <Compile Include="Outbound\Prospect\Queries\TeamRegionContactNumberAssignment.cs" />
    <Compile Include="Outbound\ProspectAssignment\ProspectAssignmentEntity.cs" />
    <Compile Include="Outbound\ProspectAssignment\Queries\CustomerProspectAssignmentData.cs" />
    <Compile Include="Outbound\ProspectAssignment\Queries\ProspectAssignmentData.cs" />
    <Compile Include="Outbound\Province\ProvinceEntity.cs" />    <Compile Include="Outbound\Province\Events\CreateEditProvinceEvent.cs" />
    <Compile Include="Outbound\Province\Queries\ProvinceListItem.cs" />
    <Compile Include="Outbound\Region\RegionEntity.cs" />    <Compile Include="Outbound\Region\Events\CreateEditRegionEvent.cs" />
    <Compile Include="Outbound\Reminder\ReminderEntity.cs" />    <Compile Include="Outbound\Reminder\Events\ReminderEvent.cs" />
    <Compile Include="Outbound\Reminder\Queries\AgentReminderItem.cs" />
    <Compile Include="Outbound\Reminder\Queries\ReminderData.cs" />
    <Compile Include="Outbound\Report\TMRSpeedAndRessultCareAppointmentItem.cs" />
    <Compile Include="Outbound\Report\TMRSpeedAndRessultCareProtectedLeadItem.cs" />
    <Compile Include="Outbound\Report\TMRSpeedAndRessultNewCallingItem.cs" />
    <Compile Include="Outbound\Report\DynamicFormValue\DynamicFormValueStatusReportItem.cs" />
    <Compile Include="Outbound\Role\StaticRole.cs" />
    <Compile Include="Outbound\Sale\SaleEntity.cs" />
    <Compile Include="Outbound\SlotTime\SlotTimeEntity.cs" />
    <Compile Include="Outbound\StagingContact\StagingContactEntity.cs" />    <Compile Include="Outbound\StagingContact\Events\StagingContactCreatedEvent.cs" />
    <Compile Include="Outbound\StagingContact\Queries\StagingContactData.cs" />
    <Compile Include="Outbound\SubContact\SubContactEntity.cs" />    <Compile Include="Outbound\SubContact\Events\ChildContactCreatedEvent.cs" />
    <Compile Include="Outbound\SubContact\Queries\SubContactData.cs" />
    <Compile Include="Outbound\SysConfigs\SysConfigsEntity.cs" />
    <Compile Include="Outbound\SysConfigs\Queries\SysConfigsData.cs" />
    <Compile Include="Outbound\Team\TeamEntity.cs" />    <Compile Include="Outbound\Team\Queries\CampaignTeamSelectingData.cs" />
    <Compile Include="Outbound\Team\Queries\ChienDichTeamData.cs" />    <Compile Include="Outbound\Team\Queries\TeamData.cs" />
    <Compile Include="Outbound\TeamLeadCampaign\ContactDataInCampaign.cs" />
    <Compile Include="Outbound\TemplateCodeCallResult\TemplateCodeCallResultEntity.cs" />    <Compile Include="Outbound\UserCheckIn\UserCheckInEntity.cs" />    <Compile Include="Outbound\UserCheckIn\Queries\UserCheckInData.cs" />
    <Compile Include="Outbound\VNMContactImportSession\GetImportPlanDetailsEntity.cs" />
    <Compile Include="Outbound\VNMContactImportSession\NotInsertContactInImportSessionEntity.cs" />
    <Compile Include="Outbound\VNMContactImportSession\ScanInputDataErrorEntity.cs" />
    <Compile Include="Outbound\VNMContactImportSession\StagingContactEntity.cs" />
    <Compile Include="Outbound\VNMContactImportSession\VNMContactImportSessionEntity.cs" />    <Compile Include="Outbound\VNMContactImportSession\Queries\VNMContactImportSessionData.cs" />
    <Compile Include="Outbound\Ward\WardEntity.cs" />    <Compile Include="Outbound\Ward\Events\CreateEditWardEvent.cs" />
    <Compile Include="Outbound\Ward\Queries\WardListItem.cs" />
    <Compile Include="Part\PartBookableTimeEntity.cs" />
    <Compile Include="Part\PartCategoryEntity.cs" />
    <Compile Include="Part\PartData.cs" />
    <Compile Include="Part\PartEntity.cs" />
    <Compile Include="Part\PartErrorCategoryEntity.cs" />
    <Compile Include="Part\PartMaintenanceListItem.cs" />
    <Compile Include="Part\PartTemplateData.cs" />
    <Compile Include="Part\PartTypeEntity.cs" />
    <Compile Include="Part\SearchPartServiceTypeConfigEntity.cs" />
    <Compile Include="Part\ServiceTypePartEntity.cs" />
    <Compile Include="Part\Queries\PartBookableTimeData.cs" />
    <Compile Include="Part\Queries\PartTypeData.cs" />
    <Compile Include="Part\Queries\SearchPartServiceTypeConfigData.cs" />
    <Compile Include="PartBooking\PartBookingEntity.cs" />    <Compile Include="PartBooking\Queries\PartBookingData.cs" />
    <Compile Include="PartBooking\Queries\PartBookingListItem.cs" />
    <Compile Include="PartCustomer\PartCustomerData.cs" />
    <Compile Include="PartCustomer\PartCustomerEntity.cs" />
    <Compile Include="PartCustomer\Queries\PartCustomerInfo.cs" />
    <Compile Include="PartCustomer\Queries\PartCustomerItem.cs" />
    <Compile Include="PartRepairing\PartRepairingData.cs" />
    <Compile Include="PartRepairing\PartRepairingEntity.cs" />
    <Compile Include="PartRepairing\PartRepairReportItem.cs" />
    <Compile Include="PartServiceUsedHistory\PartServiceUsedHistoryEntity.cs" />    <Compile Include="PartServiceUsedHistory\Queries\PartServiceUsedHistoryData.cs" />
    <Compile Include="PartServiceUsedHistory\Queries\PartServiceUsedHistoryInfo.cs" />
    <Compile Include="PartServiceUsedHistory\Queries\ScanImportResultInfo.cs" />
    <Compile Include="Phase\PhaseEntity.cs" />
    <Compile Include="Phase\TaskEntity.cs" />
    <Compile Include="Phase\TaskFeedbackEntity.cs" />
    <Compile Include="Phase\TaskTypeBusinessPermissionEntity.cs" />
    <Compile Include="Phase\UpdateTaskFromEmailContent.cs" />
    <Compile Include="Phase\WorkflowGroupEntity.cs" />
    <Compile Include="Phase\Queries\PhaseInfo.cs" />
    <Compile Include="Phase\Queries\TaskSummaryData.cs" />
    <Compile Include="PlanJob\PlanJobEntity.cs" />
    <Compile Include="PlanJob\ServiceTypeComplianceEntity.cs" />    <Compile Include="PlanJob\Queries\PlanJobData.cs" />
    <Compile Include="PlanJob\Queries\PlanJobInfo.cs" />
    <Compile Include="PlanJob\Queries\ServiceTypeComplianceData.cs" />
    <Compile Include="Product\ProductEntity.cs" />
    <Compile Include="Product\Queries\ProductData.cs" />
    <Compile Include="Product\Queries\ProductHistory.cs" />
    <Compile Include="Product\Queries\ProductListItem.cs" />
    <Compile Include="Properties\Settings.Designer.cs" />
    <Compile Include="Query\QueryEntity.cs" />
    <Compile Include="Report\AgentSurveyRatingSummaryReportItem.cs" />
    <Compile Include="Report\BaoCaoNangSuatLaoDong02Item.cs" />
    <Compile Include="Report\BehaviorsReportItem.cs" />
    <Compile Include="Report\ComplainedProductQuantityItem.cs" />
    <Compile Include="Report\LeadTimeDataReportItem.cs" />
    <Compile Include="Report\PredefinedListReportItem.cs" />
    <Compile Include="Report\ReportByServiceCategoryData.cs" />
    <Compile Include="Report\ReportByServiceCategoryItem.cs" />
    <Compile Include="Report\ReportF3Item.cs" />
    <Compile Include="Report\ReportF4Item.cs" />
    <Compile Include="Report\RequestTicketDataConversionItem.cs" />
    <Compile Include="Report\RequestTicketOwnershipSummaryReportItem.cs" />
    <Compile Include="Report\RequestTicketProcessStatusReportItem.cs" />
    <Compile Include="Report\RequestTicketReportItem.cs" />
    <Compile Include="Report\RequestTicketSumaryItem.cs" />
    <Compile Include="Report\RequestTicketSummaryByOwnerItem.cs" />
    <Compile Include="Report\RequestTicketSummaryByServiceTypeItem.cs" />
    <Compile Include="Report\RequestTicketSummaryByTimeData.cs" />
    <Compile Include="Report\RetrievalDataReportItem.cs" />
    <Compile Include="Report\SLATongHopReportItem.cs" />
    <Compile Include="Report\SLATraSoatReportItem.cs" />
    <Compile Include="Report\SourceChanelReportItem.cs" />
    <Compile Include="Report\TaskProgressInfo.cs" />
    <Compile Include="Report\TheoDoiSoLuongKhieuNai.cs" />
    <Compile Include="Report\TicketReportByOwnerItem.cs" />
    <Compile Include="Report\TicketReportByServiceTypeItem.cs" />
    <Compile Include="Report\TinhHinhGiaiQuyetKhieuNaiTheoKhuVucItem.cs" />
    <Compile Include="Report\Queries\RequestTicketDetailReportItem.cs" />
    <Compile Include="Report\VenusCorp\ChecklistReportItem.cs" />
    <Compile Include="Report\VenusCorp\DetailChecklistReportItem.cs" />
    <Compile Include="RequestTicket\InteractionHistoryData.cs" />
    <Compile Include="RequestTicket\ProductExchangeEntity.cs" />
    <Compile Include="RequestTicket\ProductRequestTicketEntity.cs" />
    <Compile Include="RequestTicket\RequestTicketBehaviorEntity.cs" />
    <Compile Include="RequestTicket\RequestTicketCallEntity.cs" />
    <Compile Include="RequestTicket\RequestTicketEntity.cs" />
    <Compile Include="RequestTicket\RequestTicketWebchatEntity.cs" />
    <Compile Include="RequestTicket\Commands\CustomerTicketByServiceType.cs" />
    <Compile Include="RequestTicket\Queries\ExportRequestTicketItem.cs" />
    <Compile Include="RequestTicket\Queries\ExportSearchRequestTicketResultItem.cs" />
    <Compile Include="RequestTicket\Queries\ExportTicketProductExchangeItem.cs" />
    <Compile Include="RequestTicket\Queries\ProductExchangeData.cs" />
    <Compile Include="RequestTicket\Queries\ProductRequestTicketItem.cs" />
    <Compile Include="RequestTicket\Queries\RequestTicketBoardData.cs" />
    <Compile Include="RequestTicket\Queries\RequestTicketCallData.cs" />
    <Compile Include="RequestTicket\Queries\RequestTicketWebchatData.cs" />
    <Compile Include="RequestTicketDynamicModel\RequestTicketDynamicModelEntity.cs" />
    <Compile Include="ResultCode\ResultCodeEntity.cs" />
    <Compile Include="ResultCode\ResultCodeSuiteEntity.cs" />
    <Compile Include="Retrieval\RequestTicketTracingItemEntity.cs" />
    <Compile Include="Retrieval\RetrievalEntity.cs" />
    <Compile Include="Retrieval\TracingItemEntity.cs" />
    <Compile Include="Retrieval\Audit\RetrievalAuditEntity.cs" />
    <Compile Include="Retrieval\Queries\RetrievalData.cs" />
    <Compile Include="Retrieval\Queries\TracingItemData.cs" />
    <Compile Include="ScheduledTaskConfig\ScheduledTaskConfigEntity.cs" />
    <Compile Include="ServiceCategory\ServiceCategoryEntity.cs" />
    <Compile Include="ServiceCategory\ServiceCategoryOrganizationEntity.cs" />
    <Compile Include="ServiceCategory\Queries\ServiceCategoryData.cs" />
    <Compile Include="ServiceCategory\Queries\ServiceCategoryOrganizationData.cs" />
    <Compile Include="ServiceCategory\Queries\ServiceCategoryTreeData.cs" />
    <Compile Include="ServiceType\LandingServiceTypeEntity.cs" />
    <Compile Include="ServiceType\RequestTicketExportDetailMappingEntity.cs" />
    <Compile Include="ServiceType\ServiceTypeEntity.cs" />
    <Compile Include="ServiceType\ServiceTypeRoleEntity.cs" />
    <Compile Include="ServiceType\Queries\LandingServiceTypeData.cs" />
    <Compile Include="ServiceType\Queries\RequestTicketExportDetailMappingData.cs" />
    <Compile Include="ServiceType\Queries\ServiceTypeAndDynamicFieldData.cs" />
    <Compile Include="ServiceType\Queries\ServiceTypeData.cs" />
    <Compile Include="ServiceType\Queries\ServiceTypeTreeData.cs" />
    <Compile Include="Sms\GatewayEntity.cs" />
    <Compile Include="Sms\GatewayIntergration.cs" />
    <Compile Include="Sms\ISmsBroker.cs" />
    <Compile Include="Sms\SmsEntity.cs" />
    <Compile Include="Sms\SmsLogEntity.cs" />
    <Compile Include="Sms\Queries\GatewayData.cs" />
    <Compile Include="Sms\Queries\SmsData.cs" />
    <Compile Include="SMSLog\SMSLogEntity.cs" />
    <Compile Include="SMSLog\Queries\SMSLogListItem.cs" />
    <Compile Include="Survey\EmailTemplateLinkEntityParams.cs" />
    <Compile Include="Survey\SurveyAnswerEntity.cs" />
    <Compile Include="Survey\SurveyAnswerSuiteAnswerEntity.cs" />
    <Compile Include="Survey\SurveyAnswerSuiteEntity.cs" />
    <Compile Include="Survey\SurveyCampaignEntity.cs" />
    <Compile Include="Survey\SurveyCampaignExecutionEntity.cs" />
    <Compile Include="Survey\SurveyCampaignResponseExecutionEntity.cs" />
    <Compile Include="Survey\SurveyEntity.cs" />
    <Compile Include="Survey\SurveyFeedbackAnswerEntity.cs" />
    <Compile Include="Survey\SurveyFeedbackEntity.cs" />
    <Compile Include="Survey\SurveyQuestionEntity.cs" />
    <Compile Include="Survey\SurveyQuestionSectionEntity.cs" />
    <Compile Include="Survey\Queries\SurveyAnswerData.cs" />
    <Compile Include="Survey\Queries\SurveyAnswerItem.cs" />
    <Compile Include="Survey\Queries\SurveyAnswerSuiteData.cs" />
    <Compile Include="Survey\Queries\SurveyCampaignExecutionData.cs" />
    <Compile Include="Survey\Queries\SurveyCampaignExecutionListItem.cs" />
    <Compile Include="Survey\Queries\SurveyCampaignListItem.cs" />
    <Compile Include="Survey\Queries\SurveyCampaignResponseExecutionData.cs" />
    <Compile Include="Survey\Queries\SurveyCampaignResponseExecutionListItem.cs" />
    <Compile Include="Survey\Queries\SurveyData.cs" />
    <Compile Include="Survey\Queries\SurveyFeedbackData.cs" />
    <Compile Include="Survey\Queries\SurveyListItem.cs" />
    <Compile Include="Survey\Queries\SurveyQuestionAnswerSuiteReportItem.cs" />
    <Compile Include="Survey\Queries\SurveyQuestionData.cs" />
    <Compile Include="Survey\Queries\SurveyQuestionListItem.cs" />
    <Compile Include="Survey\Queries\SurveyQuestionSectionData.cs" />
    <Compile Include="TaskType\BusinessResultReferenceEntity.cs" />
    <Compile Include="TaskType\TaskTypeEntity.cs" />
    <Compile Include="TaskType\TaskTypeNotificationEventEntity.cs" />
    <Compile Include="TaskType\Queries\TaskTypeInfo.cs" />
    <Compile Include="TaskType\Queries\TaskTypeListItem.cs" />
    <Compile Include="TaskType\Queries\TaskTypeReportInfo.cs" />
    <Compile Include="Tax\TaxEntity.cs" />    <Compile Include="Tax\Queries\TaxData.cs" />
    <Compile Include="TbCallback\TbCallbackCallEntity.cs" />
    <Compile Include="TbCallback\TbCallbackCallResultEntity.cs" />
    <Compile Include="TbCallback\TbCallbackEntity.cs" />
    <Compile Include="TbCallback\Queries\TbCallbackCallData.cs" />
    <Compile Include="TbCallback\Queries\TbCallbackCallInfo.cs" />
    <Compile Include="TbCallback\Queries\TbCallbackCallResultData.cs" />
    <Compile Include="TemplateLibrary\Queries\TemplateLibraryItem.cs" />
    <Compile Include="TicketHotButton\TicketHotButtonEntity.cs" />
    <Compile Include="Ticket\Queries\GetDropdownLoadUserOptionQuery.cs" />
    <Compile Include="Ticket\Queries\GetOrganizationForTicketQuery.cs" />
    <Compile Include="UserAccount\Queries\AspNetUserData.cs" />
    <Compile Include="UserAccount\Queries\AspNetUserListItem.cs" />
    <Compile Include="UserAccount\Queries\SearchUsersQuery.cs" />
    <Compile Include="UserAccount\UserProfileEntityData.cs" />
    <Compile Include="UserTaskAssignmentRouting\Commands\CreateEditUserTaskAssignmentRoutingCommand.cs" />
    <Compile Include="UserTaskAssignmentRouting\Queries\GetUserTaskAssignmentRoutingByIdQuery.cs" />
    <Compile Include="UserTaskAssignmentRouting\Queries\GetUserTaskAssignmentRoutingListQuery.cs" />
    <Compile Include="User\OwnerData.cs" />
    <Compile Include="UserTaskAssignmentRouting\UserTaskAssignmentRoutingEntity.cs" />
    <Compile Include="UserTaskAssignmentRouting\Queries\UserTaskAssignmentRoutingData.cs" />
    <Compile Include="UserTaskAssignmentRouting\Queries\UserTaskAssignmentRoutingListItem.cs" />
    <Compile Include="Utility\DateTimeUtility.cs" />
    <Compile Include="Utility\LanguageHelper.cs" />
    <Compile Include="Utility\ServerDirectoryHelper.cs" />
    <Compile Include="VirtualOffice\VirtualOfficeDisplaySlotEntity.cs" />
    <Compile Include="VirtualOffice\VirtualOfficeEmployeeEntity.cs" />
    <Compile Include="VirtualOffice\VirtualOfficeFloorEntity.cs" />
    <Compile Include="VirtualOffice\VirtualOfficeWorkspaceEntity.cs" />
    <Compile Include="VirtualOffice\Data\VirtualOfficeEmployeeData.cs" />
    <Compile Include="WebChat\Commands\CreateEditMsTeamsAccountByAadObjectIdCommand.cs" />
    <Compile Include="WebChat\Commands\InsertWebChatCommand.cs" />
    <Compile Include="WebChat\Commands\InsertWebChatMessageCommand.cs" />
    <Compile Include="WebChat\Commands\InsertWebChatMessageListCommand.cs" />
    <Compile Include="WebChat\Commands\InsertWebChatUserMemberCommand.cs" />
    <Compile Include="WebChat\Commands\UpdateMsTeamsAccountNamesByAadObjectIdCommand.cs" />
    <Compile Include="WebChat\MsTeamsAccountEntity.cs" />
    <Compile Include="WebChat\Queries\GetMsTeamsAccountListQuery.cs" />
    <Compile Include="WebChat\Queries\GetWebChatGatewayCustomDataQuery.cs" />
    <Compile Include="WebChat\Queries\GetWebChatMessageListQuery.cs" />
    <Compile Include="WebChat\Queries\SearchWebChatListQuery.cs" />
    <Compile Include="WebChat\WebChatEntity.cs" />
    <Compile Include="WebChat\WebChatGatewayCustomDataEntity.cs" />
    <Compile Include="WebChat\WebChatMessageEntity.cs" />
    <Compile Include="WebChat\WebChatUserMemberEntity.cs" />    <Compile Include="WebChat\Queries\MsTeamsAccountData.cs" />
    <Compile Include="WebChat\Queries\WebChatData.cs" />
    <Compile Include="WebChat\Queries\WebChatGatewayCustomData.cs" />
    <Compile Include="WebChat\Queries\WebChatMessageData.cs" />
    <Compile Include="WorkflowTaskTypeGroup\Commands\AddRemoveTaskToGroupCommand.cs" />
    <Compile Include="WorkflowTaskTypeGroup\Commands\CreateEditGroupCommand.cs" />
    <Compile Include="WorkflowTaskTypeGroup\Commands\CreateLinkToGroup.cs" />
    <Compile Include="WorkflowTaskTypeGroup\Commands\CreateTaskTypeGroupCommand.cs" />
    <Compile Include="WorkflowTaskTypeGroup\Commands\DeleteWorkflowTaskTypeGroupCommand.cs" />
    <Compile Include="WorkflowTaskTypeGroup\Queries\GetTaskTypeDataListByTaskTypeGroupIdQuery.cs" />
    <Compile Include="WorkflowTaskTypeGroup\Queries\GetTaskTypeListInGroupQuery.cs" />
    <Compile Include="WorkflowTaskTypeGroup\Queries\GetWorkflowTaskTypeGroupByIdQuery.cs" />
    <Compile Include="WorkflowTaskTypeGroup\Queries\GetWorkflowTaskTypeGroupListQuery.cs" />
    <Compile Include="WorkflowTaskTypeStage\Commands\CreateEditListTaskTypeStageCommand.cs" />
    <Compile Include="WorkflowTaskTypeStage\Commands\CreateTaskTypeStageCommand.cs" />
    <Compile Include="WorkflowTaskTypeStage\Commands\DeleteWorkflowTaskTypeGroupCommand.cs" />
    <Compile Include="WorkflowTaskTypeStage\Queries\GetWorkflowTaskTypeStageByIdQuery.cs" />
    <Compile Include="Workflow\ApproveItemWorkflowEvent.cs" />
    <Compile Include="Workflow\Commands\CloneWorkflowCommand.cs" />
    <Compile Include="Workflow\Commands\CreateEditTaskTypeInWorkflowCommand.cs" />
    <Compile Include="Workflow\Commands\CreateEditTaskTypeListInWorkflowCommand.cs" />
    <Compile Include="Workflow\Commands\CreateEditWorkflowCommand.cs" />
    <Compile Include="Workflow\Commands\CreateEditWrapForWorkflowCommand.cs" />
    <Compile Include="Workflow\Commands\DeleteWorkflowCommand.cs" />
    <Compile Include="Workflow\Commands\DeleteWorkflowTaskTypeCommand.cs" />
    <Compile Include="Workflow\Commands\FullCloneWorkflowCommand.cs" />
    <Compile Include="Workflow\Commands\ImportWorkflowCommand.cs" />
    <Compile Include="Workflow\Queries\ExportWorkflowQuery.cs" />
    <Compile Include="Workflow\Queries\GetAllWorkflowQuery.cs" />
    <Compile Include="Workflow\Queries\GetWorkflowByIdQuery.cs" />
    <Compile Include="Workflow\Queries\GetWorkflowMermaidCodeQuery.cs" />
    <Compile Include="Workflow\Queries\GetWorkflowTaskTypeByIdQuery.cs" />
    <Compile Include="Workflow\Queries\GetWorkflowTaskTypeByWorkflowAndTaskGroupIdQuery.cs" />
    <Compile Include="Workflow\Queries\GetWorkflowTaskTypeByWorkflowAndTaskTypeIdQuery.cs" />
    <Compile Include="Workflow\Queries\GetWorkflowTaskTypeByWorkflowIdQuery.cs" />
    <Compile Include="Workflow\Queries\SearchWorkflowQuery.cs" />
    <Compile Include="Workflow\Queries\WorkflowData.cs" />
    <Compile Include="Workflow\Queries\WorkflowTaskTypeData.cs" />
    <Compile Include="Workflow\WorkflowBusinessResultEntity.cs" />
    <Compile Include="Workflow\WorkflowEntity.cs" />
    <Compile Include="Workflow\WorkflowTaskTypeEntity.cs" />
    <Compile Include="Workflow\ApprovedItems_Workflow\IApprovedItemInWorkflow.cs" />
    <Compile Include="WorkflowTaskTypeGroup\WorkflowTaskTypeGroupEntity.cs" />    
	<Compile Include="WorkflowTaskTypeGroup\Queries\WorkflowTaskTypeGroupData.cs" />
    <Compile Include="WorkflowTaskTypeStage\WorkflowTaskTypeStageEntity.cs" />  
	<Compile Include="WorkflowTaskTypeStage\Queries\WorkflowTaskTypeStageData.cs" />  
	<Compile Include="WorkItemSummary\Commands\CreateEditWorkItemSummaryCommand.cs" />  
	<Compile Include="WorkItemSummary\Commands\CreateEditWorkItemSummaryCustomerServiceCommand.cs" />  
	<Compile Include="WorkItemSummary\Commands\CreateEditWorkItemSummarySaleEffectivenessCommand.cs" />  
	<Compile Include="WorkItemSummary\Queries\GetWorkItemSummaryByIdQuery.cs" />  
	<Compile Include="WorkItemSummary\Queries\GetWorkItemSummaryByInteractionIdQuery.cs" />  
	<Compile Include="WorkItemSummary\Queries\GetWorkItemSummaryCustomerServiceByWorkItemIdQuery.cs" />  
	<Compile Include="WorkItemSummary\Queries\GetWorkItemSummarySaleEffectivenessByWorkItemIdQuery.cs" />
    <Compile Include="WorkItemSummary\WorkItemSummaryCustomerServiceEntity.cs" />
    <Compile Include="WorkItemSummary\WorkItemSummaryEntity.cs" />
    <Compile Include="WorkItemSummary\WorkItemSummarySaleEffectivenessEntity.cs" />
    <Compile Include="WorkItemSummary\Queries\WorkItemSummaryCustomerServiceData.cs" />
    <Compile Include="WorkItemSummary\Queries\WorkItemSummaryData.cs" />
    <Compile Include="WorkItemSummary\Queries\WorkItemSummarySaleEffectivenessData.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Remove="TinyCRM.csproj.vspscc" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.0.4" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Poptech.CEP.ClientIntegration\Poptech.CEP.ClientIntegration.csproj" />
    <ProjectReference Include="..\Webaby.Core\Webaby.Core.csproj" />
    <ProjectReference Include="..\Webaby\Webaby.csproj" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Access\" />
    <Folder Include="AutomaticTask\Events\" />
    <Folder Include="DigitalMessageLog\" />
    <Folder Include="DynamicForm\Command\" />
    <Folder Include="DynamicForm\Queries\" />
    <Folder Include="Phase\Command\" />
    <Folder Include="Phase\Notifications\" />
    <Folder Include="TaskType\AutoSetStatusMethods\" />
    <Folder Include="UserAccount\CustomerIdentity\" />
  </ItemGroup>
</Project>

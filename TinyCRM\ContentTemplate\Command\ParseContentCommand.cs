﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text.RegularExpressions;
using TinyCRM.ContentTemplate.Queries;
using TinyCRM.Survey;
using Webaby;
using Webaby.Data;

namespace TinyCRM.ContentTemplate.Command
{
    public class ParseContentCommand : CommandBase
    {
        public Action<List<string>, List<string>, DataRow, List<Guid>> Handle { get; set; }

        /// <summary>
        /// Có thể null
        /// </summary>
        public Func<string, string, DataRow, List<Guid>, string, string> Parser { get; set; }

        public List<Guid> ContentTemplateList { get; set; }

        public List<Guid> KeyList { get; set; }

        /// <summary>
        /// Có thể thêm vào những param mà trong template content không có
        /// Vẫn giữ cấu trúc {{...}}
        /// </summary>
        public List<string> AdditionalParams { get; set; }

        /// <summary>
        /// 1 type trong app setting của key ContentTemplateLinkEntityParams
        /// có thể null, sử dụng để mapping display param
        /// </summary>
        public string LinkEntityParamsType { get; set; }

        public string StoredProcedureHandle { get; set; }
    }

    internal class ParseContentCommandHandler : CommandHandlerBase<ParseContentCommand>
    {
        public ParseContentCommandHandler(IServiceProvider serviceProvider, ILogger<ParseContentCommandHandler> logger, IConfiguration configuration) : base(serviceProvider) { _logger = logger; _configuration = configuration;  }
        
        public string ContentTemplateLinkEntityParams { get { return _configuration.GetValue<string>("content.template.link.entity.params");  } }

        IConfiguration _configuration { get; set; }
        ILogger<ParseContentCommandHandler> _logger { get; set; }        

        public override async Task ExecuteAsync(ParseContentCommand command)
        {
            try
            {
                var sort = command.ContentTemplateList.Select((x, i) => new
                {
                    Value = x,
                    Index = i
                });
                var titleTemplate = EntitySet.Get<ContentTemplateEntity>().Where(x => command.ContentTemplateList.Contains(x.Id)).AsEnumerable().OrderBy(x => sort.First(s => s.Value == x.Id).Index).Select(x => x.Title).ToList();
                var contentTemplate = EntitySet.Get<ContentTemplateEntity>().Where(x => command.ContentTemplateList.Contains(x.Id)).AsEnumerable().OrderBy(x => sort.First(s => s.Value == x.Id).Index).Select(x => x.Content).ToList();

                List<StaticContentTemplate> staticContentTemplates = new List<StaticContentTemplate>();
                for (int i = 0; i < contentTemplate.Count; i++)
                {
                    StaticContentTemplate staticContentTemplate = new StaticContentTemplate();
                    staticContentTemplate.ContentTemplate = contentTemplate[i];
                    staticContentTemplate.Title = string.Empty;
                    if (titleTemplate.Count > i)
                    {
                        staticContentTemplate.Title = titleTemplate[i];
                    }
                    staticContentTemplates.Add(staticContentTemplate);
                }

                await CommandExecutor.ExecuteAsync(new ParseStaticContentCommand
                {
                    KeyList = command.KeyList,
                    LinkEntityParamsType = command.LinkEntityParamsType,
                    StoredProcedureHandle = command.StoredProcedureHandle,
                    StaticContentTemplates = staticContentTemplates,
                    ParseStaticContentHandleFunc = (titles, contents, dr, attachs) =>
                    {
                        command.Handle(titles, contents, dr, attachs);
                    },
                    StaticContentParserFunc = command.Parser
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(string.Format("Error ParseContentCommand: ContentTemplateIds: {0}. RootEntityIds: {1}. LinkEntityParamsType: {2}. StoredProcedureHandle: {3}. Error Message: {4}", string.Join<Guid>(";", command.ContentTemplateList), string.Join<Guid>(";", command.KeyList), command.LinkEntityParamsType, command.StoredProcedureHandle, ex.ToString()));
            }
        }
    }
}

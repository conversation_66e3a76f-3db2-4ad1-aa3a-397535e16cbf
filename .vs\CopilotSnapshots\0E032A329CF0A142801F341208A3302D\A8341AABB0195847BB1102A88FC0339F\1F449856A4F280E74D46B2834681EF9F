﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Webaby.Data;
using Webaby.Localization;
using Webaby;
using System;
using System.Threading.Tasks;

namespace TinyCRM.Outbound.Brand.Queries
{
    public class GetBrandByIdQuery : QueryBase<BrandData>
    {
        public Guid Id { get; set; }
    }

    internal class GetBrandByIdQueryHandler : QueryHandlerBase<GetBrandByIdQuery, BrandData>
    {
        public GetBrandByIdQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<BrandData>> ExecuteAsync(GetBrandByIdQuery query)
        {
            var brand = await EntitySet.GetAsync<BrandEntity>(query.Id);
            if (brand == null) throw new InvalidOperationException(T["Không tìm  thấy Sản phẩm có id '{0}'", query.Id]);
            return new QueryResult<BrandData>(Mapper.Map<BrandData>(brand));
        }
    }
}
﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;

namespace TinyCRM.AutomaticTask.Queries
{
    public class GetAutoNextTaskErrorLogListByRequestTicketIdQuery : QueryBase<AutoNextTaskErrorLogData>
    {
        public Guid RequestTicketId { get; set; }
    }

    internal class GetAutoNextTaskErrorLogListByRequestTicketIdQueryHandler : QueryHandlerBase<GetAutoNextTaskErrorLogListByRequestTicketIdQuery, AutoNextTaskErrorLogData>
    {
        public GetAutoNextTaskErrorLogListByRequestTicketIdQueryHandler(IServiceProvider serviceProvider)
            : base(serviceProvider) { }

        public override async Task<QueryResult<AutoNextTaskErrorLogData>> ExecuteAsync(GetAutoNextTaskErrorLogListByRequestTicketIdQuery query)
        {
            var mainQuery = from antErr in EntitySet.Get<AutoNextTaskErrorLogEntity>()
                            where antErr.RequestTicketId == query.RequestTicketId
                            select antErr;
            var result = mainQuery.ToList();
            var mapped = result.Select(x => Mapper.Map<AutoNextTaskErrorLogData>(x));
            return QueryResult.Create(mapped);
        }
    }
}
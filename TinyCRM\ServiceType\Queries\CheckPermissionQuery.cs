﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Webaby;
using Webaby.Security;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;

namespace TinyCRM.ServiceType.Queries
{
    public class CheckPermissionQuery : QueryBase<bool>
    {
        public Guid ServiceTypeId { get; set; }
        public Guid UserId { get; set; }
    }

    internal class CheckPermissionQueryHandler : QueryHandlerBase<CheckPermissionQuery, bool>
    {
        public CheckPermissionQueryHandler(IEntitySet entitySet, IRepository repository, IText text, IMapper mapper)
            : base(entitySet, repository, text, mapper) { }

        public override async Task<QueryResult<bool>> ExecuteAsync(CheckPermissionQuery query)
        {
            var serviceTypeRoles = await EntitySet.GetAsync<ServiceTypeRoleEntity>();
            if (!serviceTypeRoles.Any(x => x.ServiceTypeId == query.ServiceTypeId))
            {
                return QueryResult.Create(new bool[] { true });
            }
            var userInRoles = await EntitySet.GetAsync<AspNetUserRoleEntity>();
            var permission =
                from uir in userInRoles
                join rst in serviceTypeRoles on uir.RoleId equals rst.RoleId
                where uir.UserId == query.UserId && rst.ServiceTypeId == query.ServiceTypeId
                select 1;
            return QueryResult.Create(new bool[] { permission.Any() });
        }
    }
}

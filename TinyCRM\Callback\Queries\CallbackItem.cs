﻿using AutoMapper;
using System;
using Webaby;
using Webaby.Data;

namespace TinyCRM.Callback.Queries
{
    public class CallbackItem
    {
        public Guid Id { get; set; }

        public DateTime CreatedTime { get; set; }

        public string CallingPhoneNumber { get; set; }

        public string SysStatus { get; set; }

        public string CallbackPhoneNumber { get; set; }

        public string Status { get; set; }

        public string Result { get; set; }

        public string AgentID { get; set; }

        public string Station { get; set; }

        public string MessageUrl { get; set; }

        public int? Tries { get; set; }

        public string TelephonyPort { get; set; }

        public DateTime? LastUpdateTime { get; set; }

        public string Note { get; set; }

        public string Service { get; set; }

        public int TotalCount { get; set; }
    }
}
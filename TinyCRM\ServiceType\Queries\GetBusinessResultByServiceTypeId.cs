﻿using System;
using System.Linq;
using TinyCRM.TaskType;
using Webaby;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;
using System.Threading.Tasks;

namespace TinyCRM.ServiceType.Queries
{
    public class GetBusinessResultByServiceTypeId : QueryBase<BusinessResultReferenceEntity>
    {
        public Guid ServiceTypeId { get; set; }
    }

    internal class GetBusinessResultByServiceTypeIdQueryHandler : QueryHandlerBase<GetBusinessResultByServiceTypeId, BusinessResultReferenceEntity>
    {
        public GetBusinessResultByServiceTypeIdQueryHandler(IEntitySet entitySet, IRepository repository, IText text, IMapper mapper)
            : base(entitySet, repository, text, mapper) { }

        public override async Task<QueryResult<BusinessResultReferenceEntity>> ExecuteAsync(GetBusinessResultByServiceTypeId query)
        {
            var businessResultEntity = (await EntitySet.GetAsync<BusinessResultReferenceEntity>()).Where(x => x.ReferenceObjectId == query.ServiceTypeId);
            return QueryResult.Create(businessResultEntity);
        }
    }
}

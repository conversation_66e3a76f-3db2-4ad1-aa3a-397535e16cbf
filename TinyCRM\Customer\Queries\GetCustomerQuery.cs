﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;

namespace TinyCRM.Customer.Queries
{
    public class GetCustomerQuery : QueryBase<CustomerData>
    {
        public string Code { get; set; }
        public string PhoneNumber { get; set; }
    }

    internal class GetCustomerQueryHandler : QueryHandlerBase<GetCustomerQuery, CustomerData>
    {
        public GetCustomerQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<CustomerData>> ExecuteAsync(GetCustomerQuery query)
        {
            var customerEntity = await EntitySet.GetAsync<CustomerEntity>();
            if (query.Code.IsNotNullOrEmpty())
            {
                customerEntity = customerEntity.Where(x => x.Code == query.Code);
            }
            if (query.PhoneNumber.IsNotNullOrEmpty())
            {
                customerEntity = customerEntity.Where(x => x.Phone1 == query.PhoneNumber || x.Phone2 == query.PhoneNumber || x.Phone3 == query.PhoneNumber);
            }
            return QueryResult.Create(customerEntity, Mapper.Map<CustomerData>);
        }
    }
}

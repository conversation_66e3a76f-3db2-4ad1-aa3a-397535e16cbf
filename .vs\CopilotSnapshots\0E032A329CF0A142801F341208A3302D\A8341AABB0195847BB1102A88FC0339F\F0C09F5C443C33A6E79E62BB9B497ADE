﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Webaby.Data;
using Webaby.Localization;
using Webaby;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace TinyCRM.Outbound.Brand.Queries
{
    public class GetBrandListByCompanyQuery : QueryBase<BrandData>
    {
        public Guid CompanyId { get; set; }
    }

    internal class GetBrandListByProvinceQueryHandler : QueryHandlerBase<GetBrandListByCompanyQuery, BrandData>
    {
        public GetBrandListByProvinceQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<BrandData>> ExecuteAsync(GetBrandListByCompanyQuery query)
        {
            var brandQuery = EntitySet.Get<BrandEntity>();
            brandQuery = brandQuery.Where(b => b.CompanyId == query.CompanyId);
            var entities = await brandQuery.ToListAsync();
            var mapped = entities.Select(x => Mapper.Map<BrandData>(x));
            return QueryResult.Create(mapped);
        }
    }
}
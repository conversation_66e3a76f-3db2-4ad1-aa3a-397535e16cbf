﻿using Webaby.Data;
using AutoMapper;
using Webaby.Localization;
using Webaby;
using System;
using System.Linq;
using System.Threading.Tasks;
using Webaby.Core.UserAccount;
using Webaby.Core.UserAccount.Queries;
using Webaby.Security;

namespace TinyCRM.Access
{
    public class GetUserProfileByAccessOrgQuery : QueryBase<ApplicationUser>
    {
        public Guid CodeAccessId { get; set; }
        public Guid OrganizationId { get; set; }
    }

    internal class GetUserProfileByAccessOrgQueryHandler : QueryHandlerBase<GetUserProfileByAccessOrgQuery, ApplicationUser>
    {
        public GetUserProfileByAccessOrgQueryHandler(IServiceProvider serviceProvider)
            : base(serviceProvider) { }

        public override async Task<QueryResult<ApplicationUser>> ExecuteAsync(GetUserProfileByAccessOrgQuery query)
        {
            var userProfileEntities = from up in EntitySet.Get<AspNetUserEntity>()
                                      join uir in EntitySet.Get<AspNetUserRoleEntity>() on up.Id equals uir.UserId
                                      join rb in EntitySet.Get<RoleBusinessPermissionEntity>() on uir.RoleId equals rb.RoleId
                                      join ab in EntitySet.Get<AccessBusinessPermissionEntity>() on rb.BusinessPermissionId equals ab.BusinessPermissionId
                                      where ab.AccessId == query.CodeAccessId && up.OrganizationId == query.OrganizationId && up.IsApproved
                                      select up;
            var result = userProfileEntities.ToList();
            var mapped = result.Select(x => Mapper.Map<ApplicationUser>(x));
            return QueryResult.Create(mapped);
        }
    }
}

﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TinyCRM.Outbound.ContactCall;
using Webaby;
using Webaby.Data;

namespace TinyCRM.Outbound.Appointment.Queries
{
    public class GetAppointmentListByProspectAssignmentQuery : QueryBase<AppointmentInfo>
    {
        public Guid ProspectAssignmentId
        {
            get;
            set;
        }
    }

    internal class GetAppointmentListByProspectAssignmentQueryHandler : QueryHandlerBase<GetAppointmentListByProspectAssignmentQuery, AppointmentInfo>
    {
        public override QueryResult<AppointmentInfo> Execute(GetAppointmentListByProspectAssignmentQuery query)
        {
            var appointmentQuery = EntitySet.Get<AppointmentEntity>();
            var contactCallQuery = EntitySet.Get<ContactCallEntity>();

            var mainQuery = (from ap in appointmentQuery
                             join cc in contactCallQuery on ap.ContactCallId equals cc.Id
                             where cc.ProspectAssignmentId == query.ProspectAssignmentId
                             orderby ap.CreatedDate descending
                             select ap);

            return QueryResult.Create(mainQuery, query.Pagination, AppointmentInfo.FromEntity);
        }
    }
}
﻿using AutoMapper;
using System;
using System.Data;
using System.Data.SqlClient;
using Webaby;
using Webaby.Data;
using Webaby.Localization;
using Microsoft.Data.SqlClient;

namespace TinyCRM.Callback.Commands
{
    public class UpdateCallbackStatusResultCommand : CommandBase
    {
        public Guid Id { get; set; }

        public String Status { get; set; }

        public String Result { get; set; }
    }

    internal class UpdateCallbackStatusResultCommandHandler : CommandHandlerBase<UpdateCallbackStatusResultCommand>
    {
        public UpdateCallbackStatusResultCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }
        public override async Task ExecuteAsync(UpdateCallbackStatusResultCommand command)
        {
            var entity = await EntitySet.GetAsync<CallbackEntity>(command.Id);
            if (entity != null)
            {
                var cmd = new SqlCommand
                {
                    CommandTimeout = 3600,
                    CommandType = CommandType.Text,
                    CommandText = "Update CB_Callbacks Set Status = @Status, Result = @Result Where Id = @Id"
                };
                cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd ,"@Id", command.Id));
                cmd.Parameters.Add(DbParameterHelper.AddNullableString(cmd ,"@Status", command.Status.IsNullOrEmpty() ? string.Empty : command.Status));
                cmd.Parameters.Add(DbParameterHelper.AddNullableString(cmd ,"@Result", command.Result.IsNullOrEmpty() ? string.Empty : command.Result));

                await EntitySet.ExecuteNonQueryAsync(cmd);
            }
        }
    }
}
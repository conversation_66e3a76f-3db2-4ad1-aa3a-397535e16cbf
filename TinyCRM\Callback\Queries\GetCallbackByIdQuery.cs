﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;
using Webaby.Localization;
using AutoMapper;

namespace TinyCRM.Callback.Queries
{
    public class GetCallbackByIdQuery : QueryBase<CallbackData>
    {
        public Guid Id { get; set; }
    }

    internal class GetCallbackByIdQueryHandler : QueryHandlerBase<GetCallbackByIdQuery, CallbackData>
    {
        public GetCallbackByIdQueryHandler(IServiceProvider serviceProvider)
            : base(serviceProvider) { }

        public override async Task<QueryResult<CallbackData>> ExecuteAsync(GetCallbackByIdQuery query)
        {
            var callback = await EntitySet.GetAsync<CallbackEntity>(query.Id);
            if (callback == null) throw new InvalidOperationException(T[$"Không tìm  thấy Callback có id '{query.Id}'"]);
            return new QueryResult<CallbackData>(Mapper.Map<CallbackData>(callback));
        }
    }
}
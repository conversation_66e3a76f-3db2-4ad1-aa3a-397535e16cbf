﻿using System.Data;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;

namespace TinyCRM.Behavior.Queries
{
    public class SearchBehaviorQuery : QueryBase<BehaviorData>
    {
        public string Name { get; set; }
        public int? Code { get; set; }
        public bool IsDisabled { get; set; }
    }

    internal class SearchBehaviorQueryHandler : QueryHandlerBase<SearchBehaviorQuery, BehaviorData>
    {
        public SearchBehaviorQueryHandler(IServiceProvider serviceProvider)
            : base(serviceProvider) { }

        public override async Task<QueryResult<BehaviorData>> ExecuteAsync(SearchBehaviorQuery query)
        {
            int startRow = query.Pagination.Index * query.Pagination.Size + 1;
            int endRow = query.Pagination.Index * query.Pagination.Size + query.Pagination.Size;

            var cmd = EntitySet.CreateDbCommand();
            cmd.Parameters.Add(DbParameterHelper.AddNullableString(cmd, "@Name", query.Name));
            cmd.Parameters.Add(DbParameterHelper.AddNullableInt(cmd, "@Code", query.Code));
            cmd.Parameters.Add(DbParameterHelper.NewNullableBooleanParameter(cmd, "@IsDisabled", query.IsDisabled));
            cmd.Parameters.Add(DbParameterHelper.AddNullableInt(cmd, "@StartRow", startRow));
            cmd.Parameters.Add(DbParameterHelper.AddNullableInt(cmd, "@EndRow", endRow));
            cmd.CommandText = "dbo.SearchBehaviors";
            cmd.CommandType = CommandType.StoredProcedure;

            var mainQuery = await EntitySet.ExecuteReadCommandAsync<BehaviorData>(cmd);
            return new QueryResult<BehaviorData>(mainQuery);
        }
    }
}

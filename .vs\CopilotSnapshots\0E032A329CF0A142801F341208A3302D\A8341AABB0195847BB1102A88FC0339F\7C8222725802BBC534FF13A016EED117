﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Webaby.Data;
using Webaby.Localization;
using Webaby;
using System;
using System.Threading.Tasks;

namespace TinyCRM.Outbound.CallResult.Queries
{
    public class GetCallStrategyByIdQuery : QueryBase<CallStrategyData>
    {
        public Guid Id { get; set; }
    }

    internal class GetCallStrategyByIdQueryHandler : QueryHandlerBase<GetCallStrategyByIdQuery, CallStrategyData>
    {
        public GetCallStrategyByIdQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<CallStrategyData>> ExecuteAsync(GetCallStrategyByIdQuery query)
        {
            var callStrategyResult = await EntitySet.GetAsync<CallResultCallPlanStrategyEntity>(query.Id);
            if (callStrategyResult == null) throw new InvalidOperationException(T["Không tìm  thấy chiến lược gọi có id '{0}'", query.Id]);
            return new QueryResult<CallStrategyData>(Mapper.Map<CallStrategyData>(callStrategyResult));
        }
    }
}
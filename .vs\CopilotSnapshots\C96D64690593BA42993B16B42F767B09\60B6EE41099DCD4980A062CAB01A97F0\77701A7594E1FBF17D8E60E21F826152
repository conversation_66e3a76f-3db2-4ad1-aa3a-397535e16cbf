﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Webaby.Data;
using Webaby.Localization;
using Webaby;
using TinyCRM.Customer;
using TinyCRM.Customer.Queries;
using TinyCRM.Outbound.Prospect;

namespace TinyCRM.Outbound.Contact.Queries
{
    public class GetContactByAssignmentIdQuery : QueryBase<CustomerData>
    {
        public Guid ContactAssignmentId { get; set; }
    }

    internal class GetContactByAssignmentIdQueryHandler : QueryHandlerBase<GetContactByAssignmentIdQuery, CustomerData>
    {
        public GetContactByAssignmentIdQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<CustomerData>> ExecuteAsync(GetContactByAssignmentIdQuery query)
        {
            var contactAssignment = EntitySet.Get<ProspectEntity>();
            var contact = EntitySet.Get<CustomerEntity>();

            var contacts = (from ca in contactAssignment
                            join c in contact on ca.CustomerId equals c.Id
                            where ca.Id == query.ContactAssignmentId
                            select c);

            var contactEntity = await contacts.FirstOrDefaultAsync();
            if (contactEntity == null)
                throw new InvalidOperationException(T[$"Không tìm  thấy contact có id '{{0}}'", query.ContactAssignmentId]);

            // Map thủ công sang CustomerData
            var customerData = new CustomerData
            {
                Id = contactEntity.Id,
                Name = contactEntity.Name,
                SubName = contactEntity.SubName,
                CompanyType = contactEntity.CompanyType,
                Code = contactEntity.Code,
                B2BCode = contactEntity.B2BCode,
                Type = contactEntity.Type,
                CMND = contactEntity.CMND,
                Dob = contactEntity.Dob,
               
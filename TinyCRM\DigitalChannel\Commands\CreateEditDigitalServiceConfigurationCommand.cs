﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using TinyCRM.DigitalChannel.Queries;
using TinyCRM.DigitalPushCode.Queries;
using Webaby;

namespace TinyCRM.DigitalChannel.Commands
{
    public class CreateEditDigitalServiceConfigurationCommand : CommandBase
    {
        public Guid Id { get; set; }

        public string Name { get; set; }

        public string Descriptions { get; set; }

        public string Configuration { get; set; }

        public string AuthorizationCode { get; set; }
    }

    internal class CreateEditDigitalServiceConfigurationCommandHandler : CommandHandlerBase<CreateEditDigitalServiceConfigurationCommand>
    {
        public CreateEditDigitalServiceConfigurationCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }
        public override async Task ExecuteAsync(CreateEditDigitalServiceConfigurationCommand command)
        {
            if (command.AuthorizationCode.IsNotNullOrEmpty())
            {
                var configService = await EntitySet.GetAsync<DigitalServiceConfigurationEntity>(command.Id);

                var resConfigDataString = "";
                var new_access_token = "";
                var new_refresh_token = "";

                var jsonConfig = configService.Configuration;
                var zaloConfig = Newtonsoft.Json.JsonConvert.DeserializeObject<dynamic>(jsonConfig);
                if (zaloConfig != null)
                {
                    var app_id = zaloConfig.app_id.Value;
                    var secret_key = zaloConfig.secret_key.Value;
                    //Call API
                    var baseAddress = new Uri("https://oauth.zaloapp.com");
                    var cookieContainer = new CookieContainer();
                    using (var handler = new HttpClientHandler() { CookieContainer = cookieContainer })
                    {
                        using (HttpClient client = new HttpClient(handler) { BaseAddress = baseAddress })
                        {
                            client.DefaultRequestHeaders.Clear();
                            client.DefaultRequestHeaders.Add("secret_key", secret_key);

                            List<KeyValuePair<string, string>> formData = new List<KeyValuePair<string, string>>();
                            formData.Add(new KeyValuePair<string, string>("code", command.AuthorizationCode));
                            formData.Add(new KeyValuePair<string, string>("app_id", app_id));
                            formData.Add(new KeyValuePair<string, string>("grant_type", "authorization_code"));

                            var formUrlEncodedContent = new FormUrlEncodedContent(formData);
                            var post = client.PostAsync("/v4/oa/access_token", formUrlEncodedContent).Result;

                            string rsString = post.Content.ReadAsStringAsync().Result;
                            try
                            {
                                var resData = Newtonsoft.Json.JsonConvert.DeserializeObject<dynamic>(rsString);
                                if (resData != null)
                                {
                                    new_access_token = resData.access_token;
                                    new_refresh_token = resData.refresh_token;
                                    if (new_access_token.IsNotNullOrEmpty() && new_refresh_token.IsNotNullOrEmpty())
                                    {
                                        zaloConfig.access_token = new_access_token;
                                        zaloConfig.refresh_token = new_refresh_token;
                                        resConfigDataString = JsonConvert.SerializeObject(zaloConfig);
                                    }
                                    else
                                    {
                                        throw new Exception("Có vấn đề về API get token của Server Zalo");
                                    }
                                }
                            }
                            catch (Exception e)
                            {
                                throw new Exception(e.ToString());
                            }
                        }
                    }

                    configService.Configuration = resConfigDataString;
                    await Repository.SaveAsync(configService);
                }
            } else
            {
                var configService = await EntitySet.GetAsync<DigitalServiceConfigurationEntity>(command.Id);
                if (configService == null)
                {
                    configService = new DigitalServiceConfigurationEntity();
                    configService.Id = command.Id;
                }
                configService.Name = command.Name;
                configService.Descriptions = command.Descriptions;
                configService.Configuration = command.Configuration;
                await Repository.SaveAsync(configService);
            }
        }
    }
}

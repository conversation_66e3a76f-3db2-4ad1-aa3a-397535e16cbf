﻿using AutoMapper;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using TinyCRM.TaskType;
using Webaby;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.BusinessResult.Queries
{
    public class GetBusinessResultListhierarchyQuery : QueryBase<NodeResult>
    {
        public Guid? TaskTypeId { get; set; }
    }

    public class NodeResult
    {
        public Guid? id { get; set; }
        public string text { get; set; }
        public List<Child> children { get; set; }
    }
    public class Child
    {
        public Guid? id { get; set; }
        public string text { get; set; }
        [JsonIgnore]
        public Guid? ParentId { get; set; }
    }

    public class GetBusinessResultListHierarchyQuery : QueryHandlerBase<GetBusinessResultListhierarchyQuery, NodeResult>
    {
        public GetBusinessResultListHierarchyQuery(IServiceProvider serviceProvider)
            : base(serviceProvider) { }
        public override async Task<QueryResult<NodeResult>> ExecuteAsync(GetBusinessResultListhierarchyQuery query)
        {
            var nodeparents = new List<NodeResult>();

            if (query.TaskTypeId != null)
            {
                nodeparents = (from br in EntitySet.Get<BusinessResultEntity>()
                               join ttbr in EntitySet.Get<BusinessResultReferenceEntity>() on br.Id equals ttbr.BusinessResultId
                               where ttbr.ReferenceObjectId == query.TaskTypeId && br.ParentId == null
                               select new
                               {
                                   id = br.Id,
                                   text = br.Name,
                               }).AsEnumerable().Select(x => new NodeResult { id = x.id, text = x.text, children = null }).ToList();
            }

            else if(query.TaskTypeId == null || nodeparents.Count() == 0)
            {
                nodeparents = (from br in EntitySet.Get<BusinessResultEntity>()
                               where br.ParentId == null
                               select new
                               {
                                   id = br.Id,
                                   text = br.Name,
                               }).AsEnumerable().Select(x => new NodeResult { id = x.id, text = x.text, children = null }).ToList();
            }

            var nodechild = (from br in EntitySet.Get<BusinessResultEntity>()
                             join ttbr in EntitySet.Get<BusinessResultReferenceEntity>() on br.Id equals ttbr.BusinessResultId
                             where br.ParentId != null && ttbr.ReferenceObjectId == query.TaskTypeId
                             select new Child
                             {
                                 id = br.Id,
                                 text = br.Name,
                                 ParentId = br.ParentId
                             }).ToList();

            foreach (var item in nodechild)
            {
                var check = nodeparents.Find(g => g.id == item.ParentId);
                if(check.children == null)
                {
                    check.children = new List<Child>();
                }
                check.children.Add(item);
            }
            return new QueryResult<NodeResult>(nodeparents);
        }
    }
}
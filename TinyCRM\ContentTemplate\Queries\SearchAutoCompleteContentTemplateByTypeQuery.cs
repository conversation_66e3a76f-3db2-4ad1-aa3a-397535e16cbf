﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;

namespace TinyCRM.ContentTemplate.Queries
{
    public class SearchAutoCompleteContentTemplateByTypeQuery : QueryBase<SearchAutoCompleteContentTemplateByTypeQuery.Result>
    {
        public class Result
        {
            public Guid Id { get; set; }
            public string Keyword { get; set; }
            public string Content { get; set; }
        }
        public TemplateType Type { get; set; }
    }

    internal class SearchAutoCompleteContentTemplateByTypeQueryHandler : QueryHandlerBase<SearchAutoCompleteContentTemplateByTypeQuery, SearchAutoCompleteContentTemplateByTypeQuery.Result>
    {
        public SearchAutoCompleteContentTemplateByTypeQueryHandler(IServiceProvider serviceProvider)
            : base(serviceProvider) { }

        public override async Task<QueryResult<SearchAutoCompleteContentTemplateByTypeQuery.Result>> ExecuteAsync(SearchAutoCompleteContentTemplateByTypeQuery query)
        {
            var result = EntitySet.Get<AutoCompleteContentTemplateEntity>().Where(x => x.Type == query.Type);
            // QueryResult.Create supports async enumeration if needed, but here we just wrap the result
            return QueryResult.Create(result.Select(x => new SearchAutoCompleteContentTemplateByTypeQuery.Result
            {
                Content = x.Content,
                Id = x.Id,
                Keyword = x.Keyword
            }), query.Pagination);
        }
    }
}

﻿using System;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using TinyCRM.ServiceType;
using Webaby;
using Webaby.Core.DynamicForm;
using Webaby.Core.DynamicForm.Queries;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.AutomaticTask.Queries
{
    public class GetAllDynamicFieldConditionsQuery : QueryBase<DynamicFieldDefinitionData>
    {
        public Guid? WorkflowId { get; set; }
    }

    internal class GetAllDynamicFieldConditionsQueryHandler : QueryHandlerBase<GetAllDynamicFieldConditionsQuery, DynamicFieldDefinitionData>
    {
        public GetAllDynamicFieldConditionsQueryHandler(IServiceProvider serviceProvider)
            : base(serviceProvider) { }

        public override async Task<QueryResult<DynamicFieldDefinitionData>> ExecuteAsync(GetAllDynamicFieldConditionsQuery query)
        {
            var mainquery = (from st in EntitySet.Get<ServiceTypeEntity>()
                             join dfd in EntitySet.Get<DynamicFieldDefinitionEntity>() on st.DynamicFormId equals dfd.DynamicFormId
                             where st.WorkflowId.Value == query.WorkflowId.Value && dfd.FieldCondition.Value == true
                             select dfd);
            var result = mainquery.ToList();
            var mapped = result.Select(x => Mapper.Map<DynamicFieldDefinitionData>(x));
            return QueryResult.Create(mapped);
        }
    }
}
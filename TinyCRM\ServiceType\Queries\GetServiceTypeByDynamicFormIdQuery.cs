﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;

namespace TinyCRM.ServiceType.Queries
{
    public class GetServiceTypeByDynamicFormIdQuery : QueryBase<ServiceTypeData>
    {
        public Guid DynamicFormId { get; set; }
    }

    internal class GetServiceTypeByDynamicFormIdQueryHandler : QueryHandlerBase<GetServiceTypeByDynamicFormIdQuery, ServiceTypeData>
    {
        public GetServiceTypeByDynamicFormIdQueryHandler(IEntitySet entitySet, IRepository repository, IText text, IMapper mapper)
            : base(entitySet, repository, text, mapper) { }

        public override async Task<QueryResult<ServiceTypeData>> ExecuteAsync(GetServiceTypeByDynamicFormIdQuery query)
        {
            var serviceTypeEntity = (await EntitySet.GetAsync<ServiceTypeEntity>()).Where(df => df.DynamicFormId == query.DynamicFormId);
            return QueryResult.Create(serviceTypeEntity, Mapper.Map<ServiceTypeData>);
        }
    }
}

﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Webaby.Data;
using Webaby.Localization;
using Webaby;
using TinyCRM.Outbound.TemplateCodeCallResult;

namespace TinyCRM.Outbound.CallResult.Queries
{
    public class GetCallResultListByTemplateQuery : QueryBase<CallResultData>
    {
        public Guid CampaignTemplateCode { get; set; }
    }

    internal class GetCallResultListByTemplateQueryHandler : QueryHandlerBase<GetCallResultListByTemplateQuery, CallResultData>
    {
        public GetCallResultListByTemplateQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<CallResultData>> ExecuteAsync(GetCallResultListByTemplateQuery query)
        {
            var templateCodeCallResultQuery = EntitySet.Get<TemplateCodeCallResultEntity>();
            var callResultQuery = EntitySet.Get<CallResultEntity>();

            var mainQuery = (from c in callResultQuery
                             join tc in templateCodeCallResultQuery on c.Id equals tc.CallResultId
                             where tc.CampaignTemplateCodeId == query.CampaignTemplateCode
                             orderby c.Code
                             select c);

            var entities = await mainQuery.ToListAsync();
            var mapped = entities.Select(x => Mapper.Map<CallResultData>(x));
            return QueryResult.Create(mapped);
        }
    }
}
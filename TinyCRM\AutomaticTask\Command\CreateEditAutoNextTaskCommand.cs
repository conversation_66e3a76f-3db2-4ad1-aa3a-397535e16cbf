﻿using System;
using Webaby;
using AutoMapper;
using TinyCRM.Workflow.Queries;
using DocumentFormat.OpenXml.EMMA;
using System.Linq;
using TinyCRM.Workflow;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.AutomaticTask.Command
{
    public class CreateEditAutoNextTaskCommand : CommandBase
    {
        public Guid Id { get; set; }

        public Guid ReferenceObjectId { get; set; }

        public string ReferenceType { get; set; }

        public Guid? TaskBusinessResultId { get; set; }

        public int EventOrder { get; set; }

        public string EventCondition { get; set; }

        public bool MultiTaskTriggered { get; set; }

        public string NextTaskFormula { get; set; }

        public Guid? AssignedUserPathSelectorId { get; set; }

        public Guid? AutoConditionId { get; set; }

        public Guid? DynamicFieldConditionId { get; set; }

        public Guid? NextTaskId { get; set; }

        public AutoNextAction AutoAction { get; set; }

        public Guid? RequestTicketClosedBusinessResultId { get; set; }
        public AutoTriggeredEvent? TriggeredEvent { get; set; }
        public string DynamicFieldConditionValue { get; set; }
        public Guid? WorkflowTaskTypeClosedBusinessResultId { get; set; }
        public string FreeConditionalStatement { get; set; }
        public string TaskConditionName { get; set; }
        public string ObjectApproveType { get; set; }
        public string ObjectApproveAction { get; set; }
        public bool TaskAssignmentRouting { get; set; }
    }

    internal class CreateEditAutoNextTaskCommandHandler : CommandHandlerBase<CreateEditAutoNextTaskCommand>
    {
        public CreateEditAutoNextTaskCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task ExecuteAsync(CreateEditAutoNextTaskCommand command)
        {
            var entity = await EntitySet.GetAsync<AutoNextTaskEntity>(command.Id);
            if (entity == null)
            {
                entity = new AutoNextTaskEntity();
            }
            Mapper.Map(command, entity);

            if (command.ReferenceType != "WorkflowTaskTypeGroup")
            {
                if (command.AutoAction == AutoNextAction.CreateNextTask && command.NextTaskFormula.IsNullOrEmpty() && command.NextTaskId.IsNotNullOrEmpty())
                {
                    if (command.ReferenceType.IsEqualIgnoreCase("Workflow"))
                    {
                        var listNextTask = (await QueryExecutor.ExecuteManyAsync(new GetWorkflowTaskTypeByWorkflowIdQuery { WorkflowId = command.ReferenceObjectId })).ToList();
                        var nextTask = listNextTask.FirstOrDefault(y => y.TaskTypeId == command.NextTaskId.Value);
                        if (nextTask != null)
                        {
                            entity.NextTaskFormula = nextTask.WorkingOrder.ToString();
                        }
                    }
                    else if (command.ReferenceType.IsEqualIgnoreCase("WorkflowTaskType"))
                    {
                        WorkflowTaskTypeEntity workflowTaskTypeEntity = await EntitySet.GetAsync<WorkflowTaskTypeEntity>(command.ReferenceObjectId);
                        if (workflowTaskTypeEntity != null)
                        {
                            var listNextTask = (await QueryExecutor.ExecuteManyAsync(new GetWorkflowTaskTypeByWorkflowIdQuery { WorkflowId = workflowTaskTypeEntity.WorkflowId })).ToList();
                            var nextTask = listNextTask.FirstOrDefault(y => y.TaskTypeId == command.NextTaskId.Value);
                            if (nextTask != null)
                            {
                                entity.NextTaskFormula = nextTask.WorkingOrder.ToString();
                            }
                        }
                    }
                }
            }
            else
            {
                if (command.AutoAction == AutoNextAction.CreateNextTask && command.NextTaskFormula.IsNullOrEmpty() && command.NextTaskId.IsNotNullOrEmpty())
                {
                    var workflowTaskTypeEntity = (await EntitySet.GetAsync<WorkflowTaskTypeEntity>()).FirstOrDefault(x => x.WorkflowTaskTypeGroupId == command.ReferenceObjectId);
                    if (workflowTaskTypeEntity != null)
                    {
                        var listNextTask = (await QueryExecutor.ExecuteManyAsync(new GetWorkflowTaskTypeByWorkflowIdQuery { WorkflowId = workflowTaskTypeEntity.WorkflowId })).ToList();
                        var nextTask = listNextTask.FirstOrDefault(y => y.TaskTypeId == command.NextTaskId.Value);
                        if (nextTask != null)
                        {
                            entity.NextTaskFormula = nextTask.WorkingOrder.ToString();
                        }
                    }
                }
            }

            await Repository.SaveAsync(entity);
        }
    }
}
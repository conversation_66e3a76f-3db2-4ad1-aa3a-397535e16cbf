﻿using System;
using System.Threading.Tasks;
using AutoMapper;
using TinyCRM.Enums;
using Webaby;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.Customer.Commands
{
    public class UpdateCustomerCommand : CommandBase
    {
        public Guid Id { get; set; }

        public string Name { get; set; }

        public CustomerType Type { get; set; }

        public Guid SourceClassificationId { get; set; }

        public string AddressNumber { get; set; }

        public string AddressStreet { get; set; }

        public Guid? RegionId { get; set; }

        public Guid? AreaId { get; set; }

        public Guid? ProvinceId { get; set; }

        public Guid? DistrictId { get; set; }

        public Guid? WardId { get; set; }

        public string Phone1 { get; set; }

        public string Phone2 { get; set; }

        public string Phone3 { get; set; }

        public string ContactPhone { get; set; }

        public string Email { get; set; }

        public string FacebookId { get; set; }

        public string WorkAddress { get; set; }

        public string Job { get; set; }

        public string Notes { get; set; }
    }

    internal class UpdateCustomerCommandHandler : CommandHandlerBase<UpdateCustomerCommand>
    {
        public UpdateCustomerCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task ExecuteAsync(UpdateCustomerCommand command)
        {
            var entity = await EntitySet.GetAsync<CustomerEntity>(command.Id);
            if (entity == null)
            {
                throw new InvalidOperationException(T["Không tìm thấy khách hàng"]);
            }
            Mapper.Map(command, entity);
            await Repository.SaveAsync(entity);
        }
    }
}

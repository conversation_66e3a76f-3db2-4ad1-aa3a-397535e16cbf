﻿using System;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using TinyCRM.TaskType;
using Webaby;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.BusinessResult.Queries
{
    public class GetDefaultTaskTypeBusinessResultListQuery : QueryBase<BusinessResultData>
    {
    }

    public class GetDefaultTaskTypeBusinessResultListQueryHandler : QueryHandlerBase<GetDefaultTaskTypeBusinessResultListQuery, BusinessResultData>
    {
        public GetDefaultTaskTypeBusinessResultListQueryHandler(IServiceProvider serviceProvider)
            : base(serviceProvider) { }

        public override async Task<QueryResult<BusinessResultData>> ExecuteAsync(GetDefaultTaskTypeBusinessResultListQuery query)
        {
            var entityList = (await EntitySet.GetAsync<BusinessResultEntity>())
                .Where(br => br.Id == BaseBusinessResults.Success || br.Id == BaseBusinessResults.Failed || br.Id == BaseBusinessResults.Repeat)
                .OrderBy(br => br.DisplayOrder);
            return QueryResult.Create(entityList, Mapper.Map<BusinessResultData>);
        }
    }
}
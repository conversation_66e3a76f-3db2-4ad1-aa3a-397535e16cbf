﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;

namespace TinyCRM.DigitalContact.Commands
{
    public class AddDigitalContactsToCampaignCommand : CommandBase
    {
        public Guid CampaignId { get; set; }  
        public List<Guid> DigitalContactIds { get; set; } 
    }

    internal class AddDigitalContactsToCampaignCommandHandler : CommandHandlerBase<AddDigitalContactsToCampaignCommand>
    {
        public AddDigitalContactsToCampaignCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task ExecuteAsync(AddDigitalContactsToCampaignCommand command)
        {
            var cmd = EntitySet.CreateDbCommand();
            cmd.CommandTimeout = 90;
            cmd.Parameters.AddRange(new[]
            {
                DbParameterHelper.AddNullableGuid(cmd, "@CampaignId", command.CampaignId),
                DbParameterHelper.NewIdListParameter("@DigitalContactIds", command.DigitalContactIds)
            });

            cmd.CommandText = "dbo.AddDigitalContactsToCampaign";
            cmd.CommandType = CommandType.StoredProcedure;

            await EntitySet.ExecuteNonQueryAsync(cmd);
        }
    }
}
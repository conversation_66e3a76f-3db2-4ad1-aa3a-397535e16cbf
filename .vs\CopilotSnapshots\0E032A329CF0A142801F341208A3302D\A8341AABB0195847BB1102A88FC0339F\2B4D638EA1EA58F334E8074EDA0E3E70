﻿using System;
using Webaby;

namespace TinyCRM.Outbound.CallResult.Queries
{
    public class GetCallStrategyByIdQuery : QueryBase<CallStrategyData>
    {
        public Guid Id
        {
            get;
            set;
        }
    }

    internal class GetCallStrategyByIdQueryHandler : QueryHandlerBase<GetCallStrategyByIdQuery, CallStrategyData>
    {
        public override QueryResult<CallStrategyData> Execute(GetCallStrategyByIdQuery query)
        {
            var callStrategyResult = EntitySet.Get<CallResultCallPlanStrategyEntity>(query.Id);

            if (callStrategyResult == null) throw new InvalidOperationException(T["Không tìm  thấy chiến lược gọi có id '{0}'", query.Id]);
            return new QueryResult<CallStrategyData>(CallStrategyData.FromEntity(callStrategyResult));
        }
    }
}
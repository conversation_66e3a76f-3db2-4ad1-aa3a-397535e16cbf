﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TinyCRM.Enums;
using Webaby;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;

namespace TinyCRM.Campaign.Commands
{
    public class UpdateCampaignAssignmentResultCommand : CommandBase
    {
        public Guid CampaignAssignmentId { get; set; }

        public AssignmentStatus AssignmentStatus { get; set; }

        public string AssignmentTag { get; set; }

        public Guid? ResultCodeId { get; set; }
    }

    internal class UpdateCampaignAssignmentResultCommandHandler : CommandHandlerBase<UpdateCampaignAssignmentResultCommand>
    {
        public UpdateCampaignAssignmentResultCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task ExecuteAsync(UpdateCampaignAssignmentResultCommand command)
        {
            List<IEntity> entities = new List<IEntity>();

            #region Cập nhật Campaign Assignment

            var campaignAssignmentEntity = await EntitySet.GetAsync<CampaignAssignmentEntity>(command.CampaignAssignmentId);
            if (campaignAssignmentEntity == null)
            {
                throw new Exception("Không tìm thấy Campaign Assignment có Id: " + command.CampaignAssignmentId.ToString());
            }
            campaignAssignmentEntity.Status = command.AssignmentStatus;
            campaignAssignmentEntity.Tag = command.AssignmentTag;
            campaignAssignmentEntity.ResultCodeId = command.ResultCodeId;

            // Nếu "Kết thúc" => Work.Status=Hoàn thành, Assignment.Inactive=1
            if (command.AssignmentStatus == AssignmentStatus.Done)
            {
                campaignAssignmentEntity.Active = false;
            }

            entities.Add(campaignAssignmentEntity);

            #endregion

            #region Nếu "Kết thúc" => Work.Status=Hoàn thành, Assignment.Inactive=1

            if (command.AssignmentStatus == AssignmentStatus.Done)
            {
                var campaignWorkEntity = await EntitySet.GetAsync<CampaignWorkEntity>(campaignAssignmentEntity.WorkId);
                if (campaignWorkEntity == null)
                {
                    throw new Exception("Không tìm thấy Campaign Work có Id: " + campaignAssignmentEntity.WorkId.ToString());
                }
                campaignWorkEntity.Status = CampaignWorkStatus.Done;

                entities.Add(campaignWorkEntity);
            }

            #endregion

            await Repository.SaveAsync(entities);
        }
    }
}
﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;

namespace TinyCRM.ServiceType.Queries
{
    public class SearchServiceTypeByTextQuery : QueryBase<ServiceTypeData>
    {
        public string SearchServiceName { get; set; }
    }

    internal class SearchServiceTypeByTextQueryHandler : QueryHandlerBase<SearchServiceTypeByTextQuery, ServiceTypeData>
    {
        public SearchServiceTypeByTextQueryHandler(IEntitySet entitySet, IRepository repository, IText text, IMapper mapper)
            : base(entitySet, repository, text, mapper) { }

        public override async Task<QueryResult<ServiceTypeData>> ExecuteAsync(SearchServiceTypeByTextQuery query)
        {
            var cmd = EntitySet.CreateDbCommand();
            DbParameterHelper.AddNullableString(cmd, "@SearchServiceName", query.SearchServiceName);
            cmd.CommandText = "dbo.SearchServiceTypeByText";
            cmd.CommandType = CommandType.StoredProcedure;
            var mainQuery = await EntitySet.ExecuteReadCommandAsync<ServiceTypeData>(cmd);
            return new QueryResult<ServiceTypeData>(mainQuery);
        }
    }
}

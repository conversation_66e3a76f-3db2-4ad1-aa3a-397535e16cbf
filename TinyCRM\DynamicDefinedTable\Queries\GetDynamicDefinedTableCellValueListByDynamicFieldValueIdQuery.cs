﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using System.Data;
using System.Data.Common;
using Webaby;
using Webaby.Core.DynamicForm;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.DynamicDefinedTable.Queries
{
    public class GetDynamicDefinedTableCellValueListByDynamicFieldValueIdQuery : QueryBase<DynamicDefinedTableCellValueData>
    {
        public Guid DynamicFieldValueId { get; set; }

        public Guid DynamicDefinedTableSchemaId { get; set; }
    }

    internal class GetDynamicDefinedTableCellValueListByDynamicFieldValueIdQueryHandler : QueryHandlerBase<GetDynamicDefinedTableCellValueListByDynamicFieldValueIdQuery, DynamicDefinedTableCellValueData>
    {
        public GetDynamicDefinedTableCellValueListByDynamicFieldValueIdQueryHandler(IEntitySet entitySet, IRepository repository, IText text, IMapper mapper)
            : base(entitySet, repository, text, mapper) { }

        public override async Task<QueryResult<DynamicDefinedTableCellValueData>> ExecuteAsync(GetDynamicDefinedTableCellValueListByDynamicFieldValueIdQuery query)
        {
            DynamicDefinedTableSchemaEntity dynamicDefinedTableSchemaEntity = null;

            DynamicFieldValueEntity dynamicFieldValueEntity = await EntitySet.GetAsync<DynamicFieldValueEntity>(query.DynamicFieldValueId);
            if (dynamicFieldValueEntity != null)
            {
                DynamicFieldDefinitionEntity dynamicFieldDefinitionEntity = await EntitySet.GetAsync<DynamicFieldDefinitionEntity>(dynamicFieldValueEntity.DynamicFieldId);
                if (dynamicFieldDefinitionEntity != null && dynamicFieldDefinitionEntity.DynamicDefinedTableSchemaId.IsNotNullOrEmpty())
                {
                    dynamicDefinedTableSchemaEntity = await EntitySet.GetAsync<DynamicDefinedTableSchemaEntity>(dynamicFieldDefinitionEntity.DynamicDefinedTableSchemaId.Value);
                }
            }
            else if (query.DynamicDefinedTableSchemaId != Guid.Empty)
            {
                dynamicDefinedTableSchemaEntity = await EntitySet.GetAsync<DynamicDefinedTableSchemaEntity>(query.DynamicDefinedTableSchemaId);
            }

            if (dynamicDefinedTableSchemaEntity.RowStoredMethod == DynamicDefinedTableStoreMethod.RowJsonValue)
            {
                List<DynamicDefinedTableCellValueData> resultCellValues = new List<DynamicDefinedTableCellValueData>();

                var dynamicDefinedTableColumnList = EntitySet.Get<DynamicDefinedTableColumnEntity>().Where(c => c.DynamicDefinedTableSchemaId == dynamicDefinedTableSchemaEntity.Id).ToList();

                DbCommand dbCommand = EntitySet.CreateDbCommand();
                dbCommand.CommandType = CommandType.StoredProcedure;
                dbCommand.CommandText = "dbo.GetDynamicDefinedTableRowJsonValueList";

                dbCommand.Parameters.Add(DbParameterHelper.AddNullableGuid(dbCommand, "@DynamicFieldValueId", query.DynamicFieldValueId));
                dbCommand.Parameters.Add(DbParameterHelper.AddNullableGuid(dbCommand, "@DynamicDefinedTableSchemaId", query.DynamicDefinedTableSchemaId));

                DataTable dataTableCellValues = (await EntitySet.ExecuteReadCommandAsync(dbCommand)).Tables[0];
                foreach (DataRow row in dataTableCellValues.Rows)
                {
                    foreach (var dynamicDefinedTableColumn in dynamicDefinedTableColumnList)
                    {
                        DynamicDefinedTableCellValueData dynamicDefinedTableCellValueData = new DynamicDefinedTableCellValueData();
                        if (row["Id"].ToString().IsNotNullOrEmpty())
                        {
                            dynamicDefinedTableCellValueData.Id = new Guid(row["Id"].ToString());
                        }
                        else
                        {
                            dynamicDefinedTableCellValueData.Id = Guid.NewGuid();
                        }
                        dynamicDefinedTableCellValueData.DynamicFieldValueId = Guid.Parse(row["DynamicFieldValueId"].ToString());
                        dynamicDefinedTableCellValueData.RowNumber = int.Parse(row["RowNumber"].ToString());

                        dynamicDefinedTableCellValueData.DynamicDefinedTableColumnId = dynamicDefinedTableColumn.Id;
                        dynamicDefinedTableCellValueData.Value = row[dynamicDefinedTableColumn.Name].ToString();

                        resultCellValues.Add(dynamicDefinedTableCellValueData);
                    }
                }

                return QueryResult.Create(resultCellValues.OrderBy(cell => cell.RowNumber));
            }

            if (dynamicDefinedTableSchemaEntity.RowStoredMethod == DynamicDefinedTableStoreMethod.OwnDbTable)
            {
                List<DynamicDefinedTableCellValueData> resultCellValues = new List<DynamicDefinedTableCellValueData>();

                var dynamicDefinedTableColumnList = EntitySet.Get<DynamicDefinedTableColumnEntity>().Where(c => c.DynamicDefinedTableSchemaId == dynamicDefinedTableSchemaEntity.Id).OrderBy(x=>x.ColumnOrder).ToList();

                DbCommand dbCommand = EntitySet.CreateDbCommand();
                dbCommand.CommandType = CommandType.Text;
                dbCommand.CommandText = string.Format("SELECT * FROM dbo.DynamicDefinedTable_{0} WHERE DynamicFieldValueId = @DynamicFieldValueId", dynamicDefinedTableSchemaEntity.Name);                
                dbCommand.Parameters.Add(DbParameterHelper.AddNullableGuid(dbCommand, "@DynamicFieldValueId", query.DynamicFieldValueId));

                DataTable dataTableCellValues = (await EntitySet.ExecuteReadCommandAsync(dbCommand)).Tables[0];
                foreach (DataRow row in dataTableCellValues.Rows)
                {
                    var cellCout = 0;
                    foreach (var dynamicDefinedTableColumn in dynamicDefinedTableColumnList)
                    {
                        DynamicDefinedTableCellValueData dynamicDefinedTableCellValueData = new DynamicDefinedTableCellValueData();
                        if(cellCout == 0) dynamicDefinedTableCellValueData.Id = new Guid(row["Id"].ToString());
                        else dynamicDefinedTableCellValueData.Id = Guid.NewGuid();
                        dynamicDefinedTableCellValueData.DynamicFieldValueId = Guid.Parse(row["DynamicFieldValueId"].ToString());
                        dynamicDefinedTableCellValueData.RowNumber = int.Parse(row["RowNumber"].ToString());
                        dynamicDefinedTableCellValueData.DynamicDefinedTableColumnName = dynamicDefinedTableColumn.Name;
                        dynamicDefinedTableCellValueData.DynamicDefinedTableColumnId = dynamicDefinedTableColumn.Id;
                        dynamicDefinedTableCellValueData.Value = row[dynamicDefinedTableColumn.Name].ToString();
                        resultCellValues.Add(dynamicDefinedTableCellValueData);
                        cellCout++;
                    }
                }

                return QueryResult.Create(resultCellValues.OrderBy(cell => cell.RowNumber));
            }

            var dynamicDefinedTableCellValueEntityList = await (from val in EntitySet.Get<DynamicDefinedTableCellValueEntity>()
                                                                where val.DynamicFieldValueId == query.DynamicFieldValueId
                                                                orderby val.RowNumber
                                                                select val).ToListAsync();

            var dynamicDefinedTableCellValueList = Mapper.Map<List<DynamicDefinedTableCellValueData>>(dynamicDefinedTableCellValueEntityList);

            return QueryResult.Create(dynamicDefinedTableCellValueList);
        }
    }
}
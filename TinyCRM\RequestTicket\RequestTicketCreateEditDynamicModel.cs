﻿using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using Webaby;
using Webaby.DynamicModel;

namespace TinyCRM.RequestTicket
{
    public static class RequestTicketCreateEditDynamicModel
    {
        public static List<DynamicModelProperty> GetModelPropertyList(IConfiguration configuration)
        {
            string businessSetting = configuration.GetValue<string>("createdditticket.dynamicmodelpropertylist");
            if (businessSetting.IsNotNullOrEmpty())
            {
                try
                {
                    return JsonConvert.DeserializeObject<List<DynamicModelProperty>>(businessSetting);
                }
                catch
                {
                    return DefaultModelPropertyList();
                }
            }
            return DefaultModelPropertyList();
        }

        public static List<DynamicModelProperty> DefaultModelPropertyList()
        {
            List<DynamicModelProperty> dynamicModelProperties = new List<DynamicModelProperty>();

            DynamicModelProperty IsNew = new DynamicModelProperty();
            IsNew.Name = "IsNew";
            IsNew.DataType = "System.Boolean";
            IsNew.IsHidden = true;
            dynamicModelProperties.Add(IsNew);

            DynamicModelProperty IsNoneCustomerTicket = new DynamicModelProperty();
            IsNoneCustomerTicket.Name = "IsNoneCustomerTicket";
            IsNoneCustomerTicket.DataType = "System.Boolean";
            IsNoneCustomerTicket.IsHidden = true;
            dynamicModelProperties.Add(IsNoneCustomerTicket);

            DynamicModelProperty Code = new DynamicModelProperty();
            Code.Name = "Code";
            Code.DataType = "System.String";
            Code.DisplayName = "Mã số phiếu";
            Code.IsReadOnly = true;
            dynamicModelProperties.Add(Code);

            DynamicModelProperty UsingIsoCode = new DynamicModelProperty();
            UsingIsoCode.Name = "UsingIsoCode";
            UsingIsoCode.DataType = "System.Boolean";
            UsingIsoCode.DisplayName = "Tạo phiếu ISO";
            UsingIsoCode.UiHint = "Boolean_v5";
            dynamicModelProperties.Add(UsingIsoCode);

            DynamicModelProperty IsoCode = new DynamicModelProperty();
            IsoCode.Name = "IsoCode";
            IsoCode.DataType = "System.String";
            IsoCode.DisplayName = "Mã số ISO";
            dynamicModelProperties.Add(IsoCode);

            DynamicModelProperty Status = new DynamicModelProperty();
            Status.Name = "Status";
            Status.Type = typeof(TinyCRM.Enums.RequestTicketStatus);
            Status.DisplayName = "Trạng thái phiếu";
            Status.UiHint = "Enum-v5";
            dynamicModelProperties.Add(Status);

            DynamicModelProperty SourceChannel = new DynamicModelProperty();
            SourceChannel.Name = "SourceChannel";
            SourceChannel.DataType = "System.Nullable`1[System.Int32]";
            SourceChannel.DisplayName = "Kênh liên lạc";
            SourceChannel.UiHint = "Select_v5";
            SourceChannel.IsRequired = true;
            SourceChannel.AdditionalMetadata.Add("LoadUrl", "/Channel/GetListChannel");
            SourceChannel.AdditionalMetadata.Add("NoneTitle", "Chọn kênh liên lạc");
            dynamicModelProperties.Add(SourceChannel);

            DynamicModelProperty TicketBusinessResultId = new DynamicModelProperty();
            TicketBusinessResultId.Name = "TicketBusinessResultId";
            TicketBusinessResultId.DataType = "System.Nullable`1[System.Guid]";
            TicketBusinessResultId.DisplayName = "Kết quả";
            TicketBusinessResultId.UiHint = "Select_v5";
            TicketBusinessResultId.AdditionalMetadata.Add("LoadUrl", "/BusinessResult/SearchDropdownList");
            TicketBusinessResultId.AdditionalMetadata.Add("NoneTitle", "Chọn kết quả");
            dynamicModelProperties.Add(TicketBusinessResultId);

            DynamicModelProperty DelegatedTicket = new DynamicModelProperty();
            DelegatedTicket.Name = "DelegatedTicket";
            DelegatedTicket.DataType = "System.Boolean";
            DelegatedTicket.DisplayName = "Không phải khách hàng chính chủ";
            DelegatedTicket.UiHint = "Boolean_v5";
            dynamicModelProperties.Add(DelegatedTicket);

            DynamicModelProperty DelegatedRelationship = new DynamicModelProperty();
            DelegatedRelationship.Name = "DelegatedRelationship";
            DelegatedRelationship.Type = typeof(TinyCRM.RequestTicket.TicketDelegatedRelationship);
            DelegatedRelationship.DisplayName = "Mối quan hệ";
            DelegatedRelationship.UiHint = "Enum-v5";
            DelegatedRelationship.AdditionalMetadata.Add("NoneTitle", "Chọn mối quan hệ");
            dynamicModelProperties.Add(DelegatedRelationship);

            DynamicModelProperty DelegatedOtherRelationship = new DynamicModelProperty();
            DelegatedOtherRelationship.Name = "DelegatedOtherRelationship";
            DelegatedOtherRelationship.DataType = "System.String";
            DelegatedOtherRelationship.DisplayName = "Khác";
            DelegatedOtherRelationship.UiHint = "String_v5";
            dynamicModelProperties.Add(DelegatedOtherRelationship);

            DynamicModelProperty RpName = new DynamicModelProperty();
            RpName.Name = "RpName";
            RpName.DataType = "System.String";
            RpName.DisplayName = "Tên người yêu cầu";
            dynamicModelProperties.Add(RpName);

            DynamicModelProperty RpPhone = new DynamicModelProperty();
            RpPhone.Name = "RpPhone";
            RpPhone.DataType = "System.String";
            RpPhone.DisplayName = "Điện thoại yêu cầu";
            dynamicModelProperties.Add(RpPhone);

            DynamicModelProperty RpEmail = new DynamicModelProperty();
            RpEmail.Name = "RpEmail";
            RpEmail.DataType = "System.String";
            RpEmail.DisplayName = "Email yêu cầu";
            dynamicModelProperties.Add(RpEmail);

            DynamicModelProperty OwnerId = new DynamicModelProperty();
            OwnerId.Name = "OwnerId";
            OwnerId.DataType = "System.Nullable`1[System.Guid]";
            OwnerId.DisplayName = "Người phụ trách phiếu";
            OwnerId.UiHint = "Select_v5";
            OwnerId.AdditionalMetadata.Add("LoadUrl", "/RequestTicket/GetUserOwner?isApproved=true&ticketId={{Id}}");
            OwnerId.AdditionalMetadata.Add("NoneTitle", "Chọn người phụ trách");
            OwnerId.IsRequired = true;
            dynamicModelProperties.Add(OwnerId);

            DynamicModelProperty DifficultyDegree = new DynamicModelProperty();
            DifficultyDegree.Name = "DifficultyDegree";
            DifficultyDegree.Type = typeof(TinyCRM.Enums.Difficulty);
            DifficultyDegree.DisplayName = "Độ khẩn";
            DifficultyDegree.UiHint = "Enum-v5";
            dynamicModelProperties.Add(DifficultyDegree);

            DynamicModelProperty BehaviorClassifications = new DynamicModelProperty();
            BehaviorClassifications.Name = "BehaviorClassifications";
            BehaviorClassifications.Type = typeof(List<Guid>);
            BehaviorClassifications.DisplayName = "Định dạng khách hàng";
            BehaviorClassifications.UiHint = "MultiSelect_v5";
            BehaviorClassifications.AdditionalMetadata.Add("LoadUrl", "/Behavior/GetListBehaviors");
            BehaviorClassifications.AdditionalMetadata.Add("NoneTitle", "Chọn định dạng khách hàng");
            dynamicModelProperties.Add(BehaviorClassifications);

            DynamicModelProperty AcceptDueTimeId = new DynamicModelProperty();
            AcceptDueTimeId.Name = "AcceptDueTimeId";
            AcceptDueTimeId.DataType = "System.Nullable`1[System.Guid]";
            AcceptDueTimeId.DisplayName = "Thời hạn tiếp nhận";
            dynamicModelProperties.Add(AcceptDueTimeId);

            DynamicModelProperty AcceptDueTime = new DynamicModelProperty();
            AcceptDueTime.Name = "AcceptDueTime";
            AcceptDueTime.Type = typeof(Webaby.Core.DueTime.Queries.DueTimeInfo);
            AcceptDueTime.DisplayName = "Thời hạn tiếp nhận";
            AcceptDueTime.UiHint = "DueTime_v5";
            dynamicModelProperties.Add(AcceptDueTime);

            DynamicModelProperty ProcessDueTimeId = new DynamicModelProperty();
            ProcessDueTimeId.Name = "ProcessDueTimeId";
            ProcessDueTimeId.DataType = "System.Nullable`1[System.Guid]";
            ProcessDueTimeId.DisplayName = "Thời hạn hoàn thành";
            dynamicModelProperties.Add(ProcessDueTimeId);

            DynamicModelProperty ProcessDueTime = new DynamicModelProperty();
            ProcessDueTime.Name = "ProcessDueTime";
            ProcessDueTime.Type = typeof(Webaby.Core.DueTime.Queries.DueTimeInfo);
            ProcessDueTime.DisplayName = "Thời hạn hoàn thành";
            ProcessDueTime.UiHint = "DueTime_v5";
            dynamicModelProperties.Add(ProcessDueTime);

            DynamicModelProperty Notes = new DynamicModelProperty();
            Notes.Name = "Notes";
            Notes.DataType = "System.String";
            Notes.DisplayName = "Nội dung phản ánh";
            Notes.UiHint = "MultilineText";
            Notes.AdditionalMetadata.Add("Suggest", "/ContentTemplate/GetAutoComplete");
            dynamicModelProperties.Add(Notes);

            DynamicModelProperty Treatment = new DynamicModelProperty();
            Treatment.Name = "Treatment";
            Treatment.DataType = "System.String";
            Treatment.DisplayName = "Tóm tắt cách xử lý";
            Treatment.UiHint = "MultilineText";
            dynamicModelProperties.Add(Treatment);

            DynamicModelProperty PlannedDate_Begin = new DynamicModelProperty();
            PlannedDate_Begin.Name = "PlannedDate_Begin";
            PlannedDate_Begin.DataType = "System.Nullable`1[System.DateTime]";
            PlannedDate_Begin.DisplayName = "Thời gian dự kiến bắt đầu";
            PlannedDate_Begin.UiHint = "DateTimeFull_v5";
            dynamicModelProperties.Add(PlannedDate_Begin);

            DynamicModelProperty PlannedDate_End = new DynamicModelProperty();
            PlannedDate_End.Name = "PlannedDate_End";
            PlannedDate_End.DataType = "System.Nullable`1[System.DateTime]";
            PlannedDate_End.DisplayName = "Thời gian dự kiến kết thúc";
            PlannedDate_End.UiHint = "DateTimeFull_v5";
            PlannedDate_End.AdditionalMetadata.Add("defaultTime", "23:59");
            dynamicModelProperties.Add(PlannedDate_End);

            return dynamicModelProperties;
        }
    }
}
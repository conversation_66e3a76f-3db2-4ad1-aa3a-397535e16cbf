﻿using System.Linq;
using Webaby;

namespace TinyCRM.Outbound.CallResult.Queries
{
    public class GetResultCodeSuiteListQuery : QueryBase<ResultCodeSuiteData>
    {
        public string SuiteName { get; set; }
    }

    internal class GetResultCodeSuiteListQueryHandler : QueryHandlerBase<GetResultCodeSuiteListQuery, ResultCodeSuiteData>
    {
        public override QueryResult<ResultCodeSuiteData> Execute(GetResultCodeSuiteListQuery query)
        {
            var resultCodeSuiteQuery = EntitySet.Get<ResultCodeSuiteEntity>();
            if (query.SuiteName.IsNotNullOrEmpty())
            {
                resultCodeSuiteQuery = resultCodeSuiteQuery.Where(x => x.Name.Contains(query.SuiteName));
            }
            return QueryResult.Create(resultCodeSuiteQuery,query.Pagination, ResultCodeSuiteData.FromEntity);
        }
    }
}
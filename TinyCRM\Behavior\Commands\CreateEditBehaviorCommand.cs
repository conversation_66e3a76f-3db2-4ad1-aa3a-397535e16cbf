﻿using AutoMapper;
using System;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.Behavior.Commands
{
    public class CreateEditBehaviorCommand : CommandBase
    {
        public Guid Id { get; set; }

        public string Name { get; set; }

        public int Code { get; set; }

        public bool IsDisabled { get; set; }
    }

    internal class CreateEditBehaviorCommandHandler : CommandHandlerBase<CreateEditBehaviorCommand>
    {
        public CreateEditBehaviorCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task ExecuteAsync(CreateEditBehaviorCommand command)
        {
            var entity = await EntitySet.GetAsync<BehaviorEntity>(command.Id);
            if (entity == null)
            {
                entity = new BehaviorEntity();
            }
            Mapper.Map(command, entity);
            await Repository.SaveAsync(entity);
        }
    }
}

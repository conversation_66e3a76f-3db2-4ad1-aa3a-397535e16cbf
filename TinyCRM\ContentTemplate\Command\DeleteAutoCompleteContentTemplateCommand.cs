﻿using System;
using Webaby;

namespace TinyCRM.ContentTemplate.Command
{
    public class DeleteAutoCompleteContentTemplateCommand : CommandBase
    {
        public Guid Id { get; set; }
    }

    internal class DeleteAutoCompleteContentTemplateCommandHandler : CommandHandlerBase<DeleteAutoCompleteContentTemplateCommand>
    {
        public DeleteAutoCompleteContentTemplateCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }
        public override async Task ExecuteAsync(DeleteAutoCompleteContentTemplateCommand command)
        {
            await Repository.DeleteAsync<AutoCompleteContentTemplateEntity>(command.Id);
        }
    }
}

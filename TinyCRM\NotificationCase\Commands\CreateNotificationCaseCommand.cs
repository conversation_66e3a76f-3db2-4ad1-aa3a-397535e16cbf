﻿using AutoMapper;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Sockets;
using System.Text;
using System.Threading.Tasks;
using TinyCRM.ContentTemplate.Queries;
using TinyCRM.NotificationChanelSetting;
using TinyCRM.Phase.Queries;
using TinyCRM.RequestTicket.Queries;
using Webaby;
using Webaby.Data;
using Webaby.Localization;
using Webaby.Notification;

namespace TinyCRM.NotificationCase.Commands
{
    public class CreateNotificationCaseCommand : CommandBase
    {
        public Guid Id { get; set; }

        public Guid ToUserId { get; set; }

        public NotificationCaseToUserType ToUserType { get; set; }

        public Guid NotificationChanelSettingId { get; set; }

        public Guid RootEntityId { get; set; }

        public DateTime NotifiedDate { get; set; }

        public string EmailType { get; set; }

        public string Link { get; set; }

        public Guid? ReferenceObjectId { get; set; }

        public string ReferenceObjectType { get; set; }

        public Guid? NotificationTypeId { get; set; }

        public string CloseNotificationChannelIdsWhenNotify { get; set; }

        public Dictionary<string, string> ExtendedTemplateParams { get; set; }

        //public Guid? ServiceTypeId { get; set; }

        //public Guid? Level1Id { get; set; }

        //public Guid? Level2Id { get; set; }

        //public Guid? Level3Id { get; set; }

        //public Guid? Level4Id { get; set; }

        //public Guid? TaskTypeId { get; set; }
    }

    internal class CreateNotificationCaseCommandHandler : CommandHandlerBase<CreateNotificationCaseCommand>
    {
        public CreateNotificationCaseCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task ExecuteAsync(CreateNotificationCaseCommand command)
        {
            string extendedTemplateParams = string.Empty;
            if (command.ExtendedTemplateParams != null && command.ExtendedTemplateParams.Count > 0)
            {
                extendedTemplateParams = JsonConvert.SerializeObject(command.ExtendedTemplateParams);
            }
            TaskInfo task = new TaskInfo();
            RequestTicketData ticket = new RequestTicketData();
            if (command.ReferenceObjectId.HasValue)
            {                
                if (command.ReferenceObjectType.Equals("Task"))
                {                    
                    task = await QueryExecutor.ExecuteOneAsync(new GetTaskByIdQuery(command.ReferenceObjectId.Value));
                    ticket = await QueryExecutor.ExecuteOneAsync(new GetRequestTicketByTaskIdQuery(task.Id));
                }
                if (command.ReferenceObjectType.Equals("RequestTicket"))
                { 
                    ticket = await QueryExecutor.ExecuteOneAsync(new GetRequestTicketByIdQuery(command.ReferenceObjectId.Value));
                }
            } 
            var altContentTemplate = await QueryExecutor.ExecuteOneAsync(new GetAlternativeContentTemplateNotificationChanelSettingQuery
            {
                ServiceTypeId = ticket.ServiceTypeId,
                Level1Id = ticket.Level1Id,
                Level2Id = ticket.Level2Id,
                Level3Id = ticket.Level3Id,
                Level4Id = ticket.Level4Id,
                TaskTypeId = task.TaskTypeId  ,
                NotificationChannelSettingId = command.NotificationChanelSettingId
            });

            NotificationChanelSettingEntity notificationChanelSettingEntity = await EntitySet.GetAsync<NotificationChanelSettingEntity>(command.NotificationChanelSettingId);

            var emailContentTemplateId = altContentTemplate != null ? altContentTemplate.ContentTemplateId : notificationChanelSettingEntity.EmailContentTemplateId;
            if (notificationChanelSettingEntity != null)
            {
                if (notificationChanelSettingEntity.SystemNotificationContentTemplateId.IsNotNullOrEmpty() || emailContentTemplateId.IsNotNullOrEmpty() || notificationChanelSettingEntity.SmsContentTemplateId.IsNotNullOrEmpty())
                {
                    NotificationCaseEntity notificationCaseEntity = new NotificationCaseEntity
                    {
                        Id = command.Id,
                        ToUserId = command.ToUserId,
                        ToUserType = command.ToUserType,
                        NotificationChanelSettingId = command.NotificationChanelSettingId,
                        RootEntityId = command.RootEntityId,
                        NotifiedDate = command.NotifiedDate,
                        EmailType = command.EmailType,
                        Link = command.Link,
                        ReferenceObjectId = command.ReferenceObjectId,
                        ReferenceObjectType = command.ReferenceObjectType,
                        NotificationTypeId = command.NotificationTypeId,

                        LinkEntityParamsKey = notificationChanelSettingEntity.LinkEntityParamsKey,
                        StoredProcedureHandle = notificationChanelSettingEntity.StoredProcedureHandle,

                        SystemNotificationContentTemplateId = notificationChanelSettingEntity.SystemNotificationContentTemplateId,
                        EmailContentTemplateId = emailContentTemplateId,
                        SmsContentTemplateId = notificationChanelSettingEntity.SmsContentTemplateId,
                        Status = NotificationStatus.New,
                        ExtendedTemplateParams = extendedTemplateParams,

                        CloseNotificationChannelIdsWhenNotify = command.CloseNotificationChannelIdsWhenNotify
                    };

                    await Repository.SaveAsync(notificationCaseEntity);
                }
            }
        }
    }
}

﻿using System;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Webaby;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.BusinessResult.Queries
{
    public class GetBusinessResultListQuery : QueryBase<BusinessResultData>
    {
    }

    public class GetBusinessResultListQueryHandler : QueryHandlerBase<GetBusinessResultListQuery, BusinessResultData>
    {
        public GetBusinessResultListQueryHandler(IServiceProvider serviceProvider)
            : base(serviceProvider) { }

        public override async Task<QueryResult<BusinessResultData>> ExecuteAsync(GetBusinessResultListQuery query)
        {
            var entityList = (await EntitySet.GetAsync<BusinessResultEntity>()).OrderBy(tt => tt.DisplayOrder);
            var items = entityList.Select(x => new BusinessResultData
            {
                Id = x.Id,
                Name = x.Name,
                // ... map các property khác cần thiết
            }).ToList();
            return QueryResult.Create(items);
        }
    }
}
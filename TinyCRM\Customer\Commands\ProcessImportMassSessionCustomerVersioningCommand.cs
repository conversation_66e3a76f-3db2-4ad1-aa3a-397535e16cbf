﻿using System.Data;
using System.Data.SqlClient;
using Webaby;
using TinyCRM.Customer.Queries;
using System;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;

namespace TinyCRM.Customer.Commands
{
    public class ProcessImportMassSessionCustomerVersioningCommand : CommandBase
    {
        public Guid ImportSessionId { get; set; }

        public string Version { get; set; }
    }

    internal class ProcessImportMassSessionCustomerVersioningCommandHandler : CommandHandlerBase<ProcessImportMassSessionCustomerVersioningCommand>
    {
        public ProcessImportMassSessionCustomerVersioningCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task ExecuteAsync(ProcessImportMassSessionCustomerVersioningCommand command)
        {
            var cmd = EntitySet.CreateDbCommand();
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@ImportSessionId", command.ImportSessionId));
            cmd.Parameters.Add(DbParameterHelper.AddNullableString(cmd, "@Version", command.Version));
            cmd.CommandType = CommandType.StoredProcedure;
            cmd.CommandText = "dbo.ProcessImportMassSessionCustomerVersioning";
            await EntitySet.ExecuteNonQueryAsync(cmd);
        }
    }
}
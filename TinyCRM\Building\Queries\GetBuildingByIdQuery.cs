﻿using Webaby.Data;
using AutoMapper;
using Webaby.Localization;
using Webaby;
using System.Threading.Tasks;
using System;

namespace TinyCRM.Building.Queries
{
    public class GetBuildingByIdQuery : QueryBase<BuildingData>
    {
        public Guid Id { get; set; }
        public GetBuildingByIdQuery(Guid id) { Id = id; }
    }

    internal class GetBuildingByIdQueryHandler : QueryHandlerBase<GetBuildingByIdQuery, BuildingData>
    {
        public GetBuildingByIdQueryHandler(IServiceProvider serviceProvider)
            : base(serviceProvider) { }

        public override async Task<QueryResult<BuildingData>> ExecuteAsync(GetBuildingByIdQuery query)
        {
            var entity = await EntitySet.GetAsync<BuildingEntity>(query.Id);
            return new QueryResult<BuildingData>(Mapper.Map<BuildingData>(entity));
        }
    }
}

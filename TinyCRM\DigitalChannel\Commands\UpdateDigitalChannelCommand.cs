﻿using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using Webaby;

namespace TinyCRM.DigitalChannel.Commands
{
    public class UpdateDigitalChannelCommand : CommandBase
    {
        public string ChannelCode { get; set; }
        public string Configuration { get; set; }
    }

    internal class UpdateDigitalChannelCommandHandler : CommandHandlerBase<UpdateDigitalChannelCommand>
    {
        public UpdateDigitalChannelCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task ExecuteAsync(UpdateDigitalChannelCommand command)
        {
            //Update thông qua bảng DigitalServiceConfigurationEntity
            var digitalChannel = await EntitySet.Get<DigitalChannelEntity>().FirstOrDefaultAsync(x => x.ChannelCode == command.ChannelCode);
            if (digitalChannel == null) throw new InvalidOperationException(T["Không tìm thấy kênh digital có channel code '{0}'", command.ChannelCode]);


            var digitalServiceConfigurationEntity = await EntitySet.GetAsync<DigitalServiceConfigurationEntity>(digitalChannel.DigitalServiceConfigurationId.Value);
            digitalServiceConfigurationEntity.Configuration = command.Configuration;

            await Repository.SaveAsync(digitalServiceConfigurationEntity);
        }
    }
}

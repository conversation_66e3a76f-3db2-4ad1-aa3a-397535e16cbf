﻿using System;
using System.Data;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;

namespace TinyCRM.Campaign.Queries
{
    public class GetCampaignWorkTicketQuery : QueryBase<WorkTicketListItem>
    {
        public Guid CampaignId { get; set; }
        public Guid? OrganizationId { get; set; }
        public Guid? ServiceTypeId { get; set; }
        public Guid? AssignedToId { get; set; }
        public Guid? UserCallId { get; set; }
        public int Status { get; set; }
    }

    internal class GetCampaignWorkTicketQueryHandler : QueryHandlerBase<GetCampaignWorkTicketQuery, WorkTicketListItem>
    {
        public GetCampaignWorkTicketQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<WorkTicketListItem>> ExecuteAsync(GetCampaignWorkTicketQuery query)
        {
            var cmd = EntitySet.CreateDbCommand();
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@campaignId", query.CampaignId));
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@organizationId", query.OrganizationId));
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@serviceTypeId", query.ServiceTypeId));
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@assignedToId", query.AssignedToId));
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@userCallId", query.UserCallId));
            cmd.Parameters.Add(DbParameterHelper.AddNullableInt(cmd, "@status", query.Status));
            cmd.Parameters.Add(DbParameterHelper.AddNullableInt(cmd, "@startRow", query.Pagination.StartRow));
            cmd.Parameters.Add(DbParameterHelper.AddNullableInt(cmd, "@endRow", query.Pagination.EndRow));
            cmd.CommandText = "GetCampaignWorkTicket";
            cmd.CommandType = CommandType.StoredProcedure;
            var mainQuery = await EntitySet.ExecuteReadCommandAsync<WorkTicketListItem>(cmd);
            return new QueryResult<WorkTicketListItem>(mainQuery);
        }
    }
}

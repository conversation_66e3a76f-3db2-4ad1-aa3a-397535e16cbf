﻿using AutoMapper;
using System;
using System.Collections.Generic;
using TinyCRM.Enums;
using Webaby.Core.File;

namespace TinyCRM.ContentTemplate.Queries
{
    public class AlternativeNotiChannelContentTemplateData
    {
        public Guid Id { get; set; }
        
        public Guid ContentTemplateId { get; set; }
                
        public Guid? TaskTypeId { get; set; }
        
        public Guid? ServiceTypeId { get; set; }
        
        public Guid? Level1Id { get; set; }
        
        public Guid? Level2Id { get; set; }
        
        public Guid? Level3Id { get; set; }
        
        public Guid? Level4Id { get; set; }
        
        public Guid NotificationChannelSettingId { get; set; }
        
        public int Order { get; set; }
        
        public DateTime CreatedDate { get; set; }
        
        public Guid CreatedBy { get; set; }
        
        public bool Deleted { get; set; }
        
        public DateTime? DeletedDate { get; set; }
        
        public Guid? DeletedBy { get; set; }
    }
}

﻿using System;
using System.Data;
using System.Data.SqlClient;
using TinyCRM.Enums;
using TinyCRM.Survey;
using Webaby;
using Webaby.Core.File.Queries;
using Webaby.Data;

namespace TinyCRM.Campaign.Queries
{
    public class SearchCampaignByQuery : QueryBase<CampaignListItem>
    {
        public string Name { get; set; }

        public DateTime? StartDate { get; set; }

        public DateTime? EndDate { get; set; }

        public CampaignStatus? Status { get; set; }

        public CampaignType? Type { get; set; }

        public SurveyType? SurveyType { get; set; }
    }

    internal class SearchCampaignByQueryHandler : QueryHandlerBase<SearchCampaignByQuery, CampaignListItem>
    {
        public SearchCampaignByQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<CampaignListItem>> ExecuteAsync(SearchCampaignByQuery query)
        {
            var name = string.Empty;
            if (!String.IsNullOrEmpty(query.Name))
            {
                string tempName = query.Name.Trim(' ').Replace(",", "").Replace(";", "");
                while (tempName.Contains("  "))
                {
                    tempName = tempName.Replace("  ", " ");
                }

                name = tempName.Replace(" ", " and ");
            }
            var cmd = EntitySet.CreateDbCommand();
            cmd.Parameters.AddRange(new[]
            {
                DbParameterHelper.AddNullableString(cmd ,"@name", name),
                DbParameterHelper.NewNullableDateTimeParameter(cmd, "@startDate", query.StartDate),
                DbParameterHelper.NewNullableDateTimeParameter(cmd, "@endDate", query.EndDate),
                DbParameterHelper.AddNullableEnum(cmd, "@status", query.Status), 
                DbParameterHelper.AddNullableEnum(cmd, "@type", query.Type),
                DbParameterHelper.AddNullableEnum(cmd,"@surveytype", query.SurveyType),
                DbParameterHelper.AddNullableInt(cmd,"@startRow", query.Pagination.StartRow),
                DbParameterHelper.AddNullableInt(cmd,"@endRow", query.Pagination.EndRow),  
            });

            cmd.CommandText = "SearchCampaign";
            cmd.CommandType = CommandType.StoredProcedure;
            var mainQuery = await EntitySet.ExecuteReadCommandAsync<CampaignListItem>(cmd);
            return new QueryResult<CampaignListItem>(mainQuery);
        }
    }
}

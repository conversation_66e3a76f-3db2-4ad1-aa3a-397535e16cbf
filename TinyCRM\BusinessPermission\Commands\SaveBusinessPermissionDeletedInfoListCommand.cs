﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Threading.Tasks;
using AutoMapper;
using Webaby;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.BusinessPermission.Commands
{
    public class SaveBusinessPermissionDeletedInfoListCommand : CommandBase
    {
        public List<Guid> DeletedList { get; set; }

        public List<Guid> UndeletedList { get; set; }

        public Guid UserId { get; set; }
    }

    internal class SaveBusinessPermissionDeletedInfoListCommandHandler : CommandHandlerBase<SaveBusinessPermissionDeletedInfoListCommand>
    {
        public SaveBusinessPermissionDeletedInfoListCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task ExecuteAsync(SaveBusinessPermissionDeletedInfoListCommand command)
        {
            var cmd = EntitySet.CreateDbCommand();
            cmd.CommandType = CommandType.StoredProcedure;
            cmd.Parameters.Add(DbParameterHelper.NewIdListParameter("@DeletedList", command.DeletedList));
            cmd.Parameters.Add(DbParameterHelper.NewIdListParameter("@UndeletedList", command.UndeletedList));
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@UserId", command.UserId));                        
            cmd.CommandText = "dbo.SaveBusinessPermissionDeletedInfoList";

            await EntitySet.ExecuteNonQueryAsync(cmd);
        }
    }
}
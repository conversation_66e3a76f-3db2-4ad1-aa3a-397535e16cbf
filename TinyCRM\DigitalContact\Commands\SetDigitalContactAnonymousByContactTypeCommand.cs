﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;
using Webaby;
using TinyCRM.DigitalChannel.Queries;
using TinyCRM.DigitalPushCode.Queries;

namespace TinyCRM.DigitalContact.Commands
{
    public class SetDigitalContactAnonymousByContactTypeCommand : CommandBase
    {
        public string UserId { get; set; }
        public Guid ContactTypeId { get; set; }
    }

    internal class SetDigitalContactAnonymousByContactTypeCommandHandler : CommandHandlerBase<SetDigitalContactAnonymousByContactTypeCommand>
    {
        public SetDigitalContactAnonymousByContactTypeCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task ExecuteAsync(SetDigitalContactAnonymousByContactTypeCommand command)
        {
            var digitalContact = (await EntitySet.GetAsync<DigitalContactEntity>()).FirstOrDefault(x => x.UserId == command.UserId && x.DigitalContactTypeId == command.ContactTypeId);
            digitalContact.CustomerId = null;
            digitalContact.Name = null;
            await Repository.SaveAsync(digitalContact);
        }
    }
}

﻿using System;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;
using TinyCRM.Outbound.Campaign;

namespace TinyCRM.Campaign.Commands
{
    public class DeleteCampaignCommand : CommandBase
    {
        public Guid Id { get; set; }
    }

    internal class DeleteCampaignCommandHandler : CommandHandlerBase<DeleteCampaignCommand>
    {
        public DeleteCampaignCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task ExecuteAsync(DeleteCampaignCommand command)
        {
            var entity = await EntitySet.GetAsync<CampaignEntity>(command.Id);
            if (entity != null)
            {
                await Repository.DeleteAsync(entity);
            }
        }
    }
}
